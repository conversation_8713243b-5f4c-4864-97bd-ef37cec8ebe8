/**
 * ASN.1 types for classes
 */
export declare enum AsnTypeTypes {
    Sequence = 0,
    Set = 1,
    Choice = 2
}
/**
 * ASN.1 types for properties
 */
export declare enum AsnPropTypes {
    Any = 1,
    Boolean = 2,
    OctetString = 3,
    BitString = 4,
    Integer = 5,
    Enumerated = 6,
    ObjectIdentifier = 7,
    Utf8String = 8,
    BmpString = 9,
    UniversalString = 10,
    NumericString = 11,
    PrintableString = 12,
    TeletexString = 13,
    VideotexString = 14,
    IA5String = 15,
    GraphicString = 16,
    VisibleString = 17,
    GeneralString = 18,
    CharacterString = 19,
    UTCTime = 20,
    GeneralizedTime = 21,
    DATE = 22,
    TimeOfDay = 23,
    DateTime = 24,
    Duration = 25,
    TIME = 26,
    Null = 27
}
