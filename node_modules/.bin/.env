    # Clerk Keys (Corrected Names)
    CLERK_PUBLISHABLE_KEY="pk_test_Y2VudHJhbC1zZWFndWxsLTcwLmNsZXJrLmFjY291bnRzLmRldiQ"
    NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_Y2VudHJhbC1zZWFndWxsLTcwLmNsZXJrLmFjY291bnRzLmRldiQ
    CLERK_SECRET_KEY=sk_test_DzVyIgHVZTAE45SAgK7FyvGq10dE1rPLDCHFcbbCfz

    # Other existing keys from your file
    OpenAI_API_KEY="********************************************************************************************************************************************************************"
    TELEGRAM_BOT_TOKEN="**********************************************"
    SERPER_API_KEY="57d1d24e603cb2ce8cbb4a065d56cd34ccbb8556"
    TAVILY_API_KEY="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"
    DEEPSEEK_API_KEY="***********************************"
    BINANCE_API_KEY="aUvmNXFNjk1jpm6jHCd12XeuVdQtcNjOivJHlU6qPazwBLU6VnGFAaHh3rpqF6vq"
    BINANCE_API_SECRET="1n2O8oOkjLg7XYSJdkIBhunITky9K5GrhdydKv2AgyaUztY275GGMZuCPzgH6vsQ"
    OPENROUTER_API_KEY="*************************************************************************"
    ACCESS_TOKEN="*******************************************************************************************************************************************************************************************************************************************"
    PHONE_NUMBER_ID="***************"
    ADMIN_PHONE_NUMBER="+************"
    WA_ADMIN_WAID="+************"
    WHATSAPP_BUSINESS_ACCOUNT_ID="***************"
    NGROK_URL="https:/arriving-ultimate-bulldog.ngrok-free.app"
    PORT="5050"
    WA_VERIFY_TOKEN="12345"
    DISCORD_BOT_TOKEN="MTM4MjY1Mjc1MzE1NDkzNjg0Mg.GlLx0y.f4t5dImuMGD-xE2IJmIhq2cCv0Ir6xlTqRrdM8"
    GOOGLE_CLIENT_ID=************-pslegm9i83845r8jqfc0hnp2of53ckal.apps.googleusercontent.com
    GOOGLE_CLIENT_SECRET="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"
    MONGO_URI="mongodb://localhost:27017/"
    GITHUB_CLIENT_SECRET="b249c57345cc0dcf1e1228e1f0af7d5c96e3f47d"
    GITHUB_CLIENT_ID="********************"
    AUTH0_DOMAIN="dev-xyozjfogfibd37yk.us.auth0.com"
    AUTH0_CLIENT_ID="kM5Ca1qXUbXYsgDogRBdbqA9rHXpmNKx"
    AUTH0_CLIENT_SECRET="****************************************************************"
    AUTH0_CALLBACK_URL="http://localhost:3000/callback"
    AUTH0_SCOPES="openid email profile https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/spreadsheets repo"