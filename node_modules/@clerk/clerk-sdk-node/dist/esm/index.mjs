import {
  Clerk,
  ClerkExpressRequireAuth,
  ClerkExpressWithAuth,
  clerkClient,
  createClerkClient,
  createClerkExpressRequireAuth,
  createClerkExpressWithAuth,
  runMiddleware,
  setClerkApiKey,
  setClerkApiVersion,
  setClerkHttpOptions,
  setClerkServerApiUrl
} from "./chunk-AKYUVZYP.mjs";

// src/index.ts
export * from "@clerk/backend";

// src/withAuth.ts
function withAuth(handler, options) {
  return async (req, res) => {
    await runMiddleware(req, res, createClerkExpressWithAuth({ clerkClient })(options));
    return handler(req, res);
  };
}

// src/requireAuth.ts
function requireAuth(handler, options) {
  return async (req, res) => {
    await runMiddleware(req, res, createClerkExpressRequireAuth({ clerkClient })(options));
    return handler(req, res);
  };
}

// src/index.ts
var {
  users,
  smsMessages,
  sessions,
  emailAddresses,
  phoneNumbers,
  emails,
  invitations,
  organizations,
  clients,
  allowlistIdentifiers,
  domains
} = clerkClient;
var src_default = clerkClient;
export {
  Clerk,
  ClerkExpressRequireAuth,
  ClerkExpressWithAuth,
  allowlistIdentifiers,
  clerkClient,
  clients,
  createClerkClient,
  createClerkExpressRequireAuth,
  createClerkExpressWithAuth,
  src_default as default,
  domains,
  emailAddresses,
  emails,
  invitations,
  organizations,
  phoneNumbers,
  requireAuth,
  sessions,
  setClerkApiKey,
  setClerkApiVersion,
  setClerkHttpOptions,
  setClerkServerApiUrl,
  smsMessages,
  users,
  withAuth
};
//# sourceMappingURL=index.mjs.map