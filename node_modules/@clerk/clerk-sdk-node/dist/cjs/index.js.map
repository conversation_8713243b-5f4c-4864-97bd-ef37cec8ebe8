{"version": 3, "sources": ["../../src/index.ts", "../../src/clerkClient.ts", "../../src/authenticateRequest.ts", "../../src/utils.ts", "../../src/clerkExpressRequireAuth.ts", "../../src/clerkExpressWithAuth.ts", "../../src/withAuth.ts", "../../src/requireAuth.ts"], "sourcesContent": ["import {\n  Clerk,\n  clerkClient,\n  ClerkExpressRe<PERSON>A<PERSON>,\n  ClerkExpress<PERSON>ithAuth,\n  createClerkClient,\n  setClerkApiKey,\n  setClerkApiVersion,\n  setClerkHttpOptions,\n  setClerkServerApiUrl,\n} from './clerkClient';\nimport { createClerkExpressRequireAuth } from './clerkExpressRequireAuth';\nimport { createClerkExpressWithAuth } from './clerkExpressWithAuth';\nimport type {\n  ClerkMiddleware,\n  ClerkMiddlewareOptions,\n  LooseAuthProp,\n  RequireAuthProp,\n  StrictAuthProp,\n  WithAuthProp,\n} from './types';\n\nexport * from '@clerk/backend';\n/**\n * The order of these exports is important, as we want Clerk from clerk/sdk-node\n * to shadow the Clerk export from clerk/backend, because it needs to support\n * 2 additional apis: clerk.expressWithAuth, clerk.expressRequireAuth\n */\nexport {\n  createClerkClient,\n  clerkClient,\n  setClerkApiKey,\n  setClerkApiVersion,\n  setClerkHttpOptions,\n  setClerkServer<PERSON><PERSON><PERSON>rl,\n  <PERSON>,\n  ClerkExpressRe<PERSON><PERSON>uth,\n  ClerkExpressWithAuth,\n};\n\nconst {\n  users,\n  smsMessages,\n  sessions,\n  emailAddresses,\n  phoneNumbers,\n  emails,\n  invitations,\n  organizations,\n  clients,\n  allowlistIdentifiers,\n  domains,\n} = clerkClient;\n\nexport {\n  users,\n  smsMessages,\n  sessions,\n  emailAddresses,\n  phoneNumbers,\n  emails,\n  invitations,\n  organizations,\n  clients,\n  allowlistIdentifiers,\n  domains,\n};\n\nexport default clerkClient;\n\nexport type { LooseAuthProp, StrictAuthProp, WithAuthProp, RequireAuthProp, ClerkMiddleware, ClerkMiddlewareOptions };\n\nexport { createClerkExpressRequireAuth };\nexport { createClerkExpressWithAuth };\n\nexport { withAuth } from './withAuth';\nexport { requireAuth } from './requireAuth';\n", "import type { ClerkOptions, VerifyTokenOptions } from '@clerk/backend';\nimport { Clerk as _Clerk, decodeJwt, verifyToken as _verifyToken } from '@clerk/backend';\n\nimport { createClerkExpressRequireAuth } from './clerkExpressRequireAuth';\nimport { createClerkExpressWithAuth } from './clerkExpressWithAuth';\nimport { loadApiEnv, loadClientEnv } from './utils';\n\ntype ExtendedClerk = ReturnType<typeof _Clerk> & {\n  expressWithAuth: ReturnType<typeof createClerkExpressWithAuth>;\n  expressRequireAuth: ReturnType<typeof createClerkExpressRequireAuth>;\n  verifyToken: (token: string, verifyOpts?: Parameters<typeof _verifyToken>[1]) => ReturnType<typeof _verifyToken>;\n} & ReturnType<typeof createBasePropForRedwoodCompatibility>;\n\n/**\n * This needs to be a *named* function in order to support the older\n * new Clerk() syntax for v4 compatibility.\n * Arrow functions can never be called with the new keyword because they do not have the [[Construct]] method\n */\nexport function Clerk(options: ClerkOptions): ExtendedClerk {\n  const clerkClient = _Clerk(options);\n  const expressWithAuth = createClerkExpressWithAuth({ ...options, clerkClient });\n  const expressRequireAuth = createClerkExpressRequireAuth({ ...options, clerkClient });\n  const verifyToken = (token: string, verifyOpts?: VerifyTokenOptions) => {\n    const issuer = (iss: string) => iss.startsWith('https://clerk.') || iss.includes('.clerk.accounts');\n    return _verifyToken(token, { issuer, ...options, ...verifyOpts });\n  };\n\n  return Object.assign(clerkClient, {\n    expressWithAuth,\n    expressRequireAuth,\n    verifyToken,\n    ...createBasePropForRedwoodCompatibility(),\n  });\n}\n\nconst createBasePropForRedwoodCompatibility = () => {\n  const verifySessionToken = (token: string) => {\n    const { jwtKey } = loadApiEnv();\n    const { payload } = decodeJwt(token);\n    return _verifyToken(token, {\n      issuer: payload.iss,\n      jwtKey,\n    });\n  };\n  return { base: { verifySessionToken } };\n};\n\nexport const createClerkClient = Clerk;\n\nlet clerkClientSingleton = {} as unknown as ReturnType<typeof Clerk>;\n\nexport const clerkClient = new Proxy(clerkClientSingleton, {\n  get(_target, property) {\n    const hasBeenInitialised = !!clerkClientSingleton.authenticateRequest;\n    if (hasBeenInitialised) {\n      // @ts-expect-error\n      return clerkClientSingleton[property];\n    }\n\n    const env = { ...loadApiEnv(), ...loadClientEnv() };\n    if (env.secretKey) {\n      clerkClientSingleton = Clerk({ ...env, userAgent: '@clerk/clerk-sdk-node' });\n      // @ts-expect-error\n      return clerkClientSingleton[property];\n    }\n\n    // @ts-expect-error\n    return Clerk({ ...env, userAgent: '@clerk/clerk-sdk-node' })[property];\n  },\n  set() {\n    return false;\n  },\n});\n\n/**\n * Stand-alone express middlewares bound to the pre-initialised clerkClient\n */\nexport const ClerkExpressRequireAuth = (...args: Parameters<ReturnType<typeof createClerkExpressRequireAuth>>) => {\n  const env = { ...loadApiEnv(), ...loadClientEnv() };\n  const fn = createClerkExpressRequireAuth({ clerkClient, ...env });\n  return fn(...args);\n};\n\nexport const ClerkExpressWithAuth = (...args: Parameters<ReturnType<typeof createClerkExpressWithAuth>>) => {\n  const env = { ...loadApiEnv(), ...loadClientEnv() };\n  const fn = createClerkExpressWithAuth({ clerkClient, ...env });\n  return fn(...args);\n};\n\n/**\n * Stand-alone setters bound to the pre-initialised clerkClient\n */\nexport const setClerkApiKey = (value: string) => {\n  clerkClient.__unstable_options.apiKey = value;\n};\n\nexport const setClerkServerApiUrl = (value: string) => {\n  clerkClient.__unstable_options.apiUrl = value;\n};\n\nexport const setClerkApiVersion = (value: string) => {\n  clerkClient.__unstable_options.apiVersion = value;\n};\n\nexport const setClerkHttpOptions = (value: RequestInit) => {\n  clerkClient.__unstable_options.httpOptions = value;\n};\n", "import type { RequestState } from '@clerk/backend';\nimport { buildRequestUrl, constants, createIsomorphicRequest } from '@clerk/backend';\nimport { handleValueOrFn } from '@clerk/shared/handleValueOrFn';\nimport { isHttpOrHttps, isProxyUrlRelative, isValidProxyUrl } from '@clerk/shared/proxy';\nimport type { ServerResponse } from 'http';\n\nimport type { AuthenticateRequestParams, ClerkClient } from './types';\nimport { loadApiEnv, loadClientEnv } from './utils';\n\nexport async function loadInterstitial({\n  clerkClient,\n  requestState,\n}: {\n  clerkClient: ClerkClient;\n  requestState: RequestState;\n}) {\n  const { clerkJSVersion, clerkJSUrl } = loadClientEnv();\n  /**\n   * When publishable key or frontendApi is present utilize the localInterstitial method\n   * and avoid the extra network call\n   */\n  if (requestState.publishableKey || requestState.frontendApi) {\n    return clerkClient.localInterstitial({\n      // Use frontendApi only when legacy frontendApi is used to avoid showing deprecation warning\n      // since the requestState always contains the frontendApi constructed by publishableKey.\n      frontendApi: requestState.publishableKey ? '' : requestState.frontendApi,\n      publishableKey: requestState.publishableKey,\n      proxyUrl: requestState.proxyUrl,\n      signInUrl: requestState.signInUrl,\n      isSatellite: requestState.isSatellite,\n      domain: requestState.domain,\n      clerkJSVersion,\n      clerkJSUrl,\n    });\n  }\n  return await clerkClient.remotePrivateInterstitial();\n}\n\nexport const authenticateRequest = (opts: AuthenticateRequestParams) => {\n  const { clerkClient, apiKey, secretKey, frontendApi, publishableKey, req, options } = opts;\n  const { jwtKey, authorizedParties, audience } = options || {};\n\n  const env = { ...loadApiEnv(), ...loadClientEnv() };\n\n  const isomorphicRequest = createIsomorphicRequest((Request, Headers) => {\n    const headers = Object.keys(req.headers).reduce((acc, key) => Object.assign(acc, { [key]: req?.headers[key] }), {});\n\n    // @ts-ignore Optimistic attempt to get the protocol in case\n    // req extends IncomingMessage in a useful way. No guarantee\n    // it'll work.\n    const protocol = req.connection?.encrypted ? 'https' : 'http';\n    const dummyOriginReqUrl = new URL(req.url || '', `${protocol}://clerk-dummy`);\n    return new Request(dummyOriginReqUrl, {\n      method: req.method,\n      headers: new Headers(headers),\n    });\n  });\n\n  const requestUrl = buildRequestUrl(isomorphicRequest);\n  const isSatellite = handleValueOrFn(options?.isSatellite, requestUrl, env.isSatellite);\n  const domain = handleValueOrFn(options?.domain, requestUrl) || env.domain;\n  const signInUrl = options?.signInUrl || env.signInUrl;\n  const proxyUrl = absoluteProxyUrl(\n    handleValueOrFn(options?.proxyUrl, requestUrl, env.proxyUrl),\n    requestUrl.toString(),\n  );\n\n  if (isSatellite && !proxyUrl && !domain) {\n    throw new Error(satelliteAndMissingProxyUrlAndDomain);\n  }\n\n  if (isSatellite && !isHttpOrHttps(signInUrl) && isDevelopmentFromApiKey(secretKey || apiKey || '')) {\n    throw new Error(satelliteAndMissingSignInUrl);\n  }\n\n  return clerkClient.authenticateRequest({\n    audience,\n    apiKey,\n    secretKey,\n    frontendApi,\n    publishableKey,\n    jwtKey,\n    authorizedParties,\n    proxyUrl,\n    isSatellite,\n    domain,\n    signInUrl,\n    request: isomorphicRequest,\n  });\n};\nexport const handleUnknownCase = (res: ServerResponse, requestState: RequestState) => {\n  if (requestState.isUnknown) {\n    res.writeHead(401, { 'Content-Type': 'text/html' });\n    res.end();\n  }\n};\n\nexport const handleInterstitialCase = (res: ServerResponse, requestState: RequestState, interstitial: string) => {\n  if (requestState.isInterstitial) {\n    res.writeHead(401, { 'Content-Type': 'text/html' });\n    res.end(interstitial);\n  }\n};\n\nexport const decorateResponseWithObservabilityHeaders = (res: ServerResponse, requestState: RequestState) => {\n  requestState.message && res.setHeader(constants.Headers.AuthMessage, encodeURIComponent(requestState.message));\n  requestState.reason && res.setHeader(constants.Headers.AuthReason, encodeURIComponent(requestState.reason));\n  requestState.status && res.setHeader(constants.Headers.AuthStatus, encodeURIComponent(requestState.status));\n};\n\nconst isDevelopmentFromApiKey = (apiKey: string): boolean =>\n  apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n\nconst absoluteProxyUrl = (relativeOrAbsoluteUrl: string, baseUrl: string): string => {\n  if (!relativeOrAbsoluteUrl || !isValidProxyUrl(relativeOrAbsoluteUrl) || !isProxyUrlRelative(relativeOrAbsoluteUrl)) {\n    return relativeOrAbsoluteUrl;\n  }\n  return new URL(relativeOrAbsoluteUrl, baseUrl).toString();\n};\n\nconst satelliteAndMissingProxyUrlAndDomain =\n  'Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl';\nconst satelliteAndMissingSignInUrl = `\nInvalid signInUrl. A satellite application requires a signInUrl for development instances.\nCheck if signInUrl is missing from your configuration or if it is not an absolute URL.`;\n", "/* eslint-disable turbo/no-undeclared-env-vars */\nimport { deprecated } from '@clerk/shared/deprecated';\nimport type { IncomingMessage, ServerResponse } from 'http';\n\n// https://nextjs.org/docs/api-routes/api-middlewares#connectexpress-middleware-support\nexport function runMiddleware(req: IncomingMessage, res: ServerResponse, fn: (...args: any) => any) {\n  return new Promise((resolve, reject) => {\n    // @ts-ignore\n    void fn(req, res, result => {\n      if (result instanceof Error) {\n        return reject(result);\n      }\n      return resolve(result);\n    });\n  });\n}\n\nexport const loadClientEnv = () => {\n  if (process.env.CLERK_FRONTEND_API) {\n    deprecated('CLERK_FRONTEND_API', 'Use `CLERK_PUBLISHABLE_KEY` instead.');\n  }\n\n  return {\n    publishableKey: process.env.CLERK_PUBLISHABLE_KEY || '',\n    frontendApi: process.env.CLERK_FRONTEND_API || '',\n    clerkJSUrl: process.env.CLERK_JS || '',\n    clerkJSVersion: process.env.CLERK_JS_VERSION || '',\n  };\n};\n\nexport const loadApiEnv = () => {\n  if (process.env.CLERK_API_KEY) {\n    deprecated('CLERK_API_KEY', 'Use `CLERK_SECRET_KEY` instead.');\n  }\n\n  return {\n    secretKey: process.env.CLERK_SECRET_KEY || process.env.CLERK_API_KEY || '',\n    apiKey: process.env.CLERK_API_KEY || '',\n    apiUrl: process.env.CLERK_API_URL || 'https://api.clerk.dev',\n    apiVersion: process.env.CLERK_API_VERSION || 'v1',\n    domain: process.env.CLERK_DOMAIN || '',\n    proxyUrl: process.env.CLERK_PROXY_URL || '',\n    signInUrl: process.env.CLERK_SIGN_IN_URL || '',\n    isSatellite: process.env.CLERK_IS_SATELLITE === 'true',\n    jwtKey: process.env.CLERK_JWT_KEY || '',\n  };\n};\n", "import type { Clerk } from '@clerk/backend';\n\nimport {\n  authenticateRequest,\n  decorateResponseWithObservabilityHeaders,\n  handleInterstitialCase,\n  handleUnknownCase,\n  loadInterstitial,\n} from './authenticateRequest';\nimport type { ClerkMiddlewareOptions, MiddlewareRequireAuthProp, RequireAuthProp } from './types';\n\nexport type CreateClerkExpressMiddlewareOptions = {\n  clerkClient: ReturnType<typeof Clerk>;\n  /**\n   * @deprecated Use `secretKey` instead.\n   */\n  apiKey?: string;\n  /* Secret Key */\n  secretKey?: string;\n  /**\n   * @deprecated Use `publishableKey` instead.\n   */\n  frontendApi?: string;\n  publishableKey?: string;\n  apiUrl?: string;\n};\n\nexport const createClerkExpressRequireAuth = (createOpts: CreateClerkExpressMiddlewareOptions) => {\n  const { clerkClient, frontendApi = '', apiKey = '', secretKey = '', publishableKey = '' } = createOpts;\n\n  return (options: ClerkMiddlewareOptions = {}): MiddlewareRequireAuthProp => {\n    return async (req, res, next) => {\n      const requestState = await authenticateRequest({\n        clerkClient,\n        apiKey,\n        secretKey,\n        frontendApi,\n        publishableKey,\n        req,\n        options,\n      });\n      decorateResponseWithObservabilityHeaders(res, requestState);\n      if (requestState.isUnknown) {\n        return handleUnknownCase(res, requestState);\n      }\n      if (requestState.isInterstitial) {\n        const interstitial = await loadInterstitial({\n          clerkClient,\n          requestState,\n        });\n        return handleInterstitialCase(res, requestState, interstitial);\n      }\n\n      if (requestState.isSignedIn) {\n        (req as RequireAuthProp<any>).auth = { ...requestState.toAuth(), claims: requestState.toAuth().sessionClaims };\n        next();\n        return;\n      }\n\n      next(new Error('Unauthenticated'));\n    };\n  };\n};\n", "import {\n  authenticateRequest,\n  decorateResponseWithObservabilityHeaders,\n  handleInterstitialCase,\n  handleUnknownCase,\n  loadInterstitial,\n} from './authenticateRequest';\nimport type { CreateClerkExpressMiddlewareOptions } from './clerkExpressRequireAuth';\nimport type { ClerkMiddlewareOptions, MiddlewareWithAuthProp, WithAuthProp } from './types';\n\nexport const createClerkExpressWithAuth = (createOpts: CreateClerkExpressMiddlewareOptions) => {\n  const { clerkClient, frontendApi = '', apiKey = '', secretKey = '', publishableKey = '' } = createOpts;\n  return (options: ClerkMiddlewareOptions = {}): MiddlewareWithAuthProp => {\n    return async (req, res, next) => {\n      const requestState = await authenticateRequest({\n        clerkClient,\n        apiKey,\n        secretKey,\n        frontendApi,\n        publishableKey,\n        req,\n        options,\n      });\n      decorateResponseWithObservabilityHeaders(res, requestState);\n      if (requestState.isUnknown) {\n        return handleUnknownCase(res, requestState);\n      }\n      if (requestState.isInterstitial) {\n        const interstitial = await loadInterstitial({\n          clerkClient,\n          requestState,\n        });\n        return handleInterstitialCase(res, requestState, interstitial);\n      }\n\n      (req as WithAuthProp<any>).auth = {\n        ...requestState.toAuth(),\n        claims: requestState.toAuth().sessionClaims,\n      };\n      next();\n    };\n  };\n};\n", "import type { Request, Response } from 'express';\n\nimport { clerkClient } from './clerkClient';\nimport { createClerkExpressWithAuth } from './clerkExpressWithAuth';\nimport type { ClerkMiddlewareOptions, WithAuthProp } from './types';\nimport { runMiddleware } from './utils';\n\ntype ApiHandlerWithAuth<TRequest, TResponse> = (req: WithAuthProp<TRequest>, res: TResponse) => void | Promise<void>;\n\n// TODO: drop the Request/Response default values in v5 version\nexport function withAuth<TRequest extends Request = Request, TResponse extends Response = Response>(\n  handler: ApiHandlerWithAuth<TRequest, TResponse>,\n  options?: ClerkMiddlewareOptions,\n): any {\n  return async (req: TRequest, res: TResponse) => {\n    await runMiddleware(req, res, createClerkExpressWithAuth({ clerkClient })(options));\n\n    return handler(req as WithAuthProp<TRequest>, res);\n  };\n}\n", "import type { Request, Response } from 'express';\n\nimport { clerkClient } from './clerkClient';\nimport { createClerkExpressRequireAuth } from './clerkExpressRequireAuth';\nimport type { ClerkMiddlewareOptions, RequireAuthProp } from './types';\nimport { runMiddleware } from './utils';\n\ntype ExpressApiHandlerRequireAuth<T = any> = (req: RequireAuthProp<Request>, res: Response<T>) => void | Promise<void>;\n\nexport function requireAuth(handler: ExpressApiHandlerRequireAuth, options?: ClerkMiddlewareOptions): any {\n  return async (req: Request, res: Response) => {\n    await runMiddleware(req, res, createClerkExpressRequireAuth({ clerkClient })(options));\n\n    return handler(req as RequireAuthProp<Request>, res);\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACCA,IAAAA,kBAAwE;;;ACAxE,qBAAoE;AACpE,6BAAgC;AAChC,mBAAmE;;;ACFnE,wBAA2B;AAIpB,SAAS,cAAc,KAAsB,KAAqB,IAA2B;AAClG,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,SAAK,GAAG,KAAK,KAAK,YAAU;AAC1B,UAAI,kBAAkB,OAAO;AAC3B,eAAO,OAAO,MAAM;AAAA,MACtB;AACA,aAAO,QAAQ,MAAM;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AACH;AAEO,IAAM,gBAAgB,MAAM;AACjC,MAAI,QAAQ,IAAI,oBAAoB;AAClC,sCAAW,sBAAsB,sCAAsC;AAAA,EACzE;AAEA,SAAO;AAAA,IACL,gBAAgB,QAAQ,IAAI,yBAAyB;AAAA,IACrD,aAAa,QAAQ,IAAI,sBAAsB;AAAA,IAC/C,YAAY,QAAQ,IAAI,YAAY;AAAA,IACpC,gBAAgB,QAAQ,IAAI,oBAAoB;AAAA,EAClD;AACF;AAEO,IAAM,aAAa,MAAM;AAC9B,MAAI,QAAQ,IAAI,eAAe;AAC7B,sCAAW,iBAAiB,iCAAiC;AAAA,EAC/D;AAEA,SAAO;AAAA,IACL,WAAW,QAAQ,IAAI,oBAAoB,QAAQ,IAAI,iBAAiB;AAAA,IACxE,QAAQ,QAAQ,IAAI,iBAAiB;AAAA,IACrC,QAAQ,QAAQ,IAAI,iBAAiB;AAAA,IACrC,YAAY,QAAQ,IAAI,qBAAqB;AAAA,IAC7C,QAAQ,QAAQ,IAAI,gBAAgB;AAAA,IACpC,UAAU,QAAQ,IAAI,mBAAmB;AAAA,IACzC,WAAW,QAAQ,IAAI,qBAAqB;AAAA,IAC5C,aAAa,QAAQ,IAAI,uBAAuB;AAAA,IAChD,QAAQ,QAAQ,IAAI,iBAAiB;AAAA,EACvC;AACF;;;ADrCA,eAAsB,iBAAiB;AAAA,EACrC,aAAAC;AAAA,EACA;AACF,GAGG;AACD,QAAM,EAAE,gBAAgB,WAAW,IAAI,cAAc;AAKrD,MAAI,aAAa,kBAAkB,aAAa,aAAa;AAC3D,WAAOA,aAAY,kBAAkB;AAAA;AAAA;AAAA,MAGnC,aAAa,aAAa,iBAAiB,KAAK,aAAa;AAAA,MAC7D,gBAAgB,aAAa;AAAA,MAC7B,UAAU,aAAa;AAAA,MACvB,WAAW,aAAa;AAAA,MACxB,aAAa,aAAa;AAAA,MAC1B,QAAQ,aAAa;AAAA,MACrB;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,MAAMA,aAAY,0BAA0B;AACrD;AAEO,IAAM,sBAAsB,CAAC,SAAoC;AACtE,QAAM,EAAE,aAAAA,cAAa,QAAQ,WAAW,aAAa,gBAAgB,KAAK,QAAQ,IAAI;AACtF,QAAM,EAAE,QAAQ,mBAAmB,SAAS,IAAI,WAAW,CAAC;AAE5D,QAAM,MAAM,EAAE,GAAG,WAAW,GAAG,GAAG,cAAc,EAAE;AAElD,QAAM,wBAAoB,wCAAwB,CAAC,SAAS,YAAY;AACtE,UAAM,UAAU,OAAO,KAAK,IAAI,OAAO,EAAE,OAAO,CAAC,KAAK,QAAQ,OAAO,OAAO,KAAK,EAAE,CAAC,GAAG,GAAG,KAAK,QAAQ,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;AAKlH,UAAM,WAAW,IAAI,YAAY,YAAY,UAAU;AACvD,UAAM,oBAAoB,IAAI,IAAI,IAAI,OAAO,IAAI,GAAG,QAAQ,gBAAgB;AAC5E,WAAO,IAAI,QAAQ,mBAAmB;AAAA,MACpC,QAAQ,IAAI;AAAA,MACZ,SAAS,IAAI,QAAQ,OAAO;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AAED,QAAM,iBAAa,gCAAgB,iBAAiB;AACpD,QAAM,kBAAc,wCAAgB,SAAS,aAAa,YAAY,IAAI,WAAW;AACrF,QAAM,aAAS,wCAAgB,SAAS,QAAQ,UAAU,KAAK,IAAI;AACnE,QAAM,YAAY,SAAS,aAAa,IAAI;AAC5C,QAAM,WAAW;AAAA,QACf,wCAAgB,SAAS,UAAU,YAAY,IAAI,QAAQ;AAAA,IAC3D,WAAW,SAAS;AAAA,EACtB;AAEA,MAAI,eAAe,CAAC,YAAY,CAAC,QAAQ;AACvC,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACtD;AAEA,MAAI,eAAe,KAAC,4BAAc,SAAS,KAAK,wBAAwB,aAAa,UAAU,EAAE,GAAG;AAClG,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AAEA,SAAOA,aAAY,oBAAoB;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX,CAAC;AACH;AACO,IAAM,oBAAoB,CAAC,KAAqB,iBAA+B;AACpF,MAAI,aAAa,WAAW;AAC1B,QAAI,UAAU,KAAK,EAAE,gBAAgB,YAAY,CAAC;AAClD,QAAI,IAAI;AAAA,EACV;AACF;AAEO,IAAM,yBAAyB,CAAC,KAAqB,cAA4B,iBAAyB;AAC/G,MAAI,aAAa,gBAAgB;AAC/B,QAAI,UAAU,KAAK,EAAE,gBAAgB,YAAY,CAAC;AAClD,QAAI,IAAI,YAAY;AAAA,EACtB;AACF;AAEO,IAAM,2CAA2C,CAAC,KAAqB,iBAA+B;AAC3G,eAAa,WAAW,IAAI,UAAU,yBAAU,QAAQ,aAAa,mBAAmB,aAAa,OAAO,CAAC;AAC7G,eAAa,UAAU,IAAI,UAAU,yBAAU,QAAQ,YAAY,mBAAmB,aAAa,MAAM,CAAC;AAC1G,eAAa,UAAU,IAAI,UAAU,yBAAU,QAAQ,YAAY,mBAAmB,aAAa,MAAM,CAAC;AAC5G;AAEA,IAAM,0BAA0B,CAAC,WAC/B,OAAO,WAAW,OAAO,KAAK,OAAO,WAAW,UAAU;AAE5D,IAAM,mBAAmB,CAAC,uBAA+B,YAA4B;AACnF,MAAI,CAAC,yBAAyB,KAAC,8BAAgB,qBAAqB,KAAK,KAAC,iCAAmB,qBAAqB,GAAG;AACnH,WAAO;AAAA,EACT;AACA,SAAO,IAAI,IAAI,uBAAuB,OAAO,EAAE,SAAS;AAC1D;AAEA,IAAM,uCACJ;AACF,IAAM,+BAA+B;AAAA;AAAA;;;AE/F9B,IAAM,gCAAgC,CAAC,eAAoD;AAChG,QAAM,EAAE,aAAAC,cAAa,cAAc,IAAI,SAAS,IAAI,YAAY,IAAI,iBAAiB,GAAG,IAAI;AAE5F,SAAO,CAAC,UAAkC,CAAC,MAAiC;AAC1E,WAAO,OAAO,KAAK,KAAK,SAAS;AAC/B,YAAM,eAAe,MAAM,oBAAoB;AAAA,QAC7C,aAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,+CAAyC,KAAK,YAAY;AAC1D,UAAI,aAAa,WAAW;AAC1B,eAAO,kBAAkB,KAAK,YAAY;AAAA,MAC5C;AACA,UAAI,aAAa,gBAAgB;AAC/B,cAAM,eAAe,MAAM,iBAAiB;AAAA,UAC1C,aAAAA;AAAA,UACA;AAAA,QACF,CAAC;AACD,eAAO,uBAAuB,KAAK,cAAc,YAAY;AAAA,MAC/D;AAEA,UAAI,aAAa,YAAY;AAC3B,QAAC,IAA6B,OAAO,EAAE,GAAG,aAAa,OAAO,GAAG,QAAQ,aAAa,OAAO,EAAE,cAAc;AAC7G,aAAK;AACL;AAAA,MACF;AAEA,WAAK,IAAI,MAAM,iBAAiB,CAAC;AAAA,IACnC;AAAA,EACF;AACF;;;ACpDO,IAAM,6BAA6B,CAAC,eAAoD;AAC7F,QAAM,EAAE,aAAAC,cAAa,cAAc,IAAI,SAAS,IAAI,YAAY,IAAI,iBAAiB,GAAG,IAAI;AAC5F,SAAO,CAAC,UAAkC,CAAC,MAA8B;AACvE,WAAO,OAAO,KAAK,KAAK,SAAS;AAC/B,YAAM,eAAe,MAAM,oBAAoB;AAAA,QAC7C,aAAAA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,+CAAyC,KAAK,YAAY;AAC1D,UAAI,aAAa,WAAW;AAC1B,eAAO,kBAAkB,KAAK,YAAY;AAAA,MAC5C;AACA,UAAI,aAAa,gBAAgB;AAC/B,cAAM,eAAe,MAAM,iBAAiB;AAAA,UAC1C,aAAAA;AAAA,UACA;AAAA,QACF,CAAC;AACD,eAAO,uBAAuB,KAAK,cAAc,YAAY;AAAA,MAC/D;AAEA,MAAC,IAA0B,OAAO;AAAA,QAChC,GAAG,aAAa,OAAO;AAAA,QACvB,QAAQ,aAAa,OAAO,EAAE;AAAA,MAChC;AACA,WAAK;AAAA,IACP;AAAA,EACF;AACF;;;AJxBO,SAAS,MAAM,SAAsC;AAC1D,QAAMC,mBAAc,gBAAAC,OAAO,OAAO;AAClC,QAAM,kBAAkB,2BAA2B,EAAE,GAAG,SAAS,aAAAD,aAAY,CAAC;AAC9E,QAAM,qBAAqB,8BAA8B,EAAE,GAAG,SAAS,aAAAA,aAAY,CAAC;AACpF,QAAM,cAAc,CAAC,OAAe,eAAoC;AACtE,UAAM,SAAS,CAAC,QAAgB,IAAI,WAAW,gBAAgB,KAAK,IAAI,SAAS,iBAAiB;AAClG,eAAO,gBAAAE,aAAa,OAAO,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,CAAC;AAAA,EAClE;AAEA,SAAO,OAAO,OAAOF,cAAa;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG,sCAAsC;AAAA,EAC3C,CAAC;AACH;AAEA,IAAM,wCAAwC,MAAM;AAClD,QAAM,qBAAqB,CAAC,UAAkB;AAC5C,UAAM,EAAE,OAAO,IAAI,WAAW;AAC9B,UAAM,EAAE,QAAQ,QAAI,2BAAU,KAAK;AACnC,eAAO,gBAAAE,aAAa,OAAO;AAAA,MACzB,QAAQ,QAAQ;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,EAAE,MAAM,EAAE,mBAAmB,EAAE;AACxC;AAEO,IAAM,oBAAoB;AAEjC,IAAI,uBAAuB,CAAC;AAErB,IAAM,cAAc,IAAI,MAAM,sBAAsB;AAAA,EACzD,IAAI,SAAS,UAAU;AACrB,UAAM,qBAAqB,CAAC,CAAC,qBAAqB;AAClD,QAAI,oBAAoB;AAEtB,aAAO,qBAAqB,QAAQ;AAAA,IACtC;AAEA,UAAM,MAAM,EAAE,GAAG,WAAW,GAAG,GAAG,cAAc,EAAE;AAClD,QAAI,IAAI,WAAW;AACjB,6BAAuB,MAAM,EAAE,GAAG,KAAK,WAAW,wBAAwB,CAAC;AAE3E,aAAO,qBAAqB,QAAQ;AAAA,IACtC;AAGA,WAAO,MAAM,EAAE,GAAG,KAAK,WAAW,wBAAwB,CAAC,EAAE,QAAQ;AAAA,EACvE;AAAA,EACA,MAAM;AACJ,WAAO;AAAA,EACT;AACF,CAAC;AAKM,IAAM,0BAA0B,IAAI,SAAuE;AAChH,QAAM,MAAM,EAAE,GAAG,WAAW,GAAG,GAAG,cAAc,EAAE;AAClD,QAAM,KAAK,8BAA8B,EAAE,aAAa,GAAG,IAAI,CAAC;AAChE,SAAO,GAAG,GAAG,IAAI;AACnB;AAEO,IAAM,uBAAuB,IAAI,SAAoE;AAC1G,QAAM,MAAM,EAAE,GAAG,WAAW,GAAG,GAAG,cAAc,EAAE;AAClD,QAAM,KAAK,2BAA2B,EAAE,aAAa,GAAG,IAAI,CAAC;AAC7D,SAAO,GAAG,GAAG,IAAI;AACnB;AAKO,IAAM,iBAAiB,CAAC,UAAkB;AAC/C,cAAY,mBAAmB,SAAS;AAC1C;AAEO,IAAM,uBAAuB,CAAC,UAAkB;AACrD,cAAY,mBAAmB,SAAS;AAC1C;AAEO,IAAM,qBAAqB,CAAC,UAAkB;AACnD,cAAY,mBAAmB,aAAa;AAC9C;AAEO,IAAM,sBAAsB,CAAC,UAAuB;AACzD,cAAY,mBAAmB,cAAc;AAC/C;;;ADpFA,wBAAc,2BAtBd;;;AMUO,SAAS,SACd,SACA,SACK;AACL,SAAO,OAAO,KAAe,QAAmB;AAC9C,UAAM,cAAc,KAAK,KAAK,2BAA2B,EAAE,YAAY,CAAC,EAAE,OAAO,CAAC;AAElF,WAAO,QAAQ,KAA+B,GAAG;AAAA,EACnD;AACF;;;ACVO,SAAS,YAAY,SAAuC,SAAuC;AACxG,SAAO,OAAO,KAAc,QAAkB;AAC5C,UAAM,cAAc,KAAK,KAAK,8BAA8B,EAAE,YAAY,CAAC,EAAE,OAAO,CAAC;AAErF,WAAO,QAAQ,KAAiC,GAAG;AAAA,EACrD;AACF;;;APyBA,IAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI;AAgBJ,IAAO,cAAQ;", "names": ["import_backend", "clerkClient", "clerkClient", "clerkClient", "clerkClient", "_Clerk", "_verifyToken"]}