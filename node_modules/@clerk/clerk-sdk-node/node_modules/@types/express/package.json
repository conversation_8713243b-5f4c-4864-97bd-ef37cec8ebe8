{"name": "@types/express", "version": "4.17.14", "description": "TypeScript definitions for Express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "China Medical University Hospital", "url": "https://github.com/CMUH", "githubUsername": "CMUH"}, {"name": "<PERSON>et A<PERSON>ra", "url": "https://github.com/puneetar", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dfrankland", "githubUsername": "dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}, "typesPublisherContentHash": "c1bc3eb1de87678353401acb958785c75095c71fec66004c015b6aba2aeee230", "typeScriptVersion": "4.1"}