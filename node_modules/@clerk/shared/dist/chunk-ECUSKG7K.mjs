// src/devBrowser.ts
var DEV_BROWSER_SSO_JWT_PARAMETER = "__dev_session";
var DEV_BROWSER_JWT_MARKER = "__clerk_db_jwt";
var DEV_BROWSER_JWT_MARKER_REGEXP = /__clerk_db_jwt\[(.*)\]/;
function setDevBrowserJWTInURL(url, jwt, opts = { hash: true }) {
  const resultURL = new URL(url);
  const jwtFromHash = extractDevBrowserJWTFromURLHash(resultURL);
  const jwtFromSearch = extractDevBrowserJWTFromURLSearchParams(resultURL);
  const jwtToSet = jwtFromHash || jwtFromSearch || jwt;
  if (jwtToSet) {
    resultURL.searchParams.append(DEV_BROWSER_SSO_JWT_PARAMETER, jwtToSet);
    resultURL.searchParams.append(DEV_BROWSER_JWT_MARKER, jwtToSet);
    if (opts.hash) {
      resultURL.hash = resultURL.hash + `${DEV_BROWSER_JWT_MARKER}[${jwtToSet}]`;
    }
  }
  return resultURL;
}
function extractDevBrowserJWTFromHash(hash) {
  const matches = hash.match(DEV_BROWSER_JWT_MARKER_REGEXP);
  return matches ? matches[1] : "";
}
function extractDevBrowserJWTFromURLHash(url) {
  const jwt = extractDevBrowserJWTFromHash(url.hash);
  url.hash = url.hash.replace(DEV_BROWSER_JWT_MARKER_REGEXP, "");
  if (url.href.endsWith("#")) {
    url.hash = "";
  }
  return jwt;
}
function extractDevBrowserJWTFromURLSearchParams(url) {
  const jwtFromDevSession = url.searchParams.get(DEV_BROWSER_SSO_JWT_PARAMETER);
  url.searchParams.delete(DEV_BROWSER_SSO_JWT_PARAMETER);
  const jwtFromClerkDbJwt = url.searchParams.get(DEV_BROWSER_JWT_MARKER);
  url.searchParams.delete(DEV_BROWSER_JWT_MARKER);
  return jwtFromDevSession || jwtFromClerkDbJwt || "";
}

export {
  DEV_BROWSER_SSO_JWT_PARAMETER,
  DEV_BROWSER_JWT_MARKER,
  setDevBrowserJWTInURL,
  extractDevBrowserJWTFromURLHash,
  extractDevBrowserJWTFromURLSearchParams
};
//# sourceMappingURL=chunk-ECUSKG7K.mjs.map