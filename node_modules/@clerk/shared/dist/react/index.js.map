{"version": 3, "sources": ["../../src/react/index.ts", "../../src/react/hooks/createContextAndHook.ts", "../../src/utils/runtimeEnvironment.ts", "../../src/deprecated.ts", "../../src/react/clerk-swr.ts", "../../src/react/contexts.tsx", "../../src/react/hooks/usePagesOrInfinite.ts", "../../src/react/hooks/useOrganization.tsx", "../../src/react/hooks/useOrganizationList.tsx", "../../src/react/hooks/useOrganizations.tsx", "../../src/react/hooks/useSafeLayoutEffect.tsx"], "sourcesContent": ["export * from './hooks';\n\nexport {\n  ClerkInstanceContext,\n  ClientContext,\n  OrganizationContext,\n  OrganizationProvider,\n  SessionContext,\n  useClerkInstanceContext,\n  useClientContext,\n  useOrganizationContext,\n  UserContext,\n  useSessionContext,\n  useUserContext,\n} from './contexts';\n", "'use client';\nimport React from 'react';\n\nexport function assertContextExists(contextVal: unknown, msgOrCtx: string | React.Context<any>): asserts contextVal {\n  if (!contextVal) {\n    throw typeof msgOrCtx === 'string' ? new Error(msgOrCtx) : new Error(`${msgOrCtx.displayName} not found`);\n  }\n}\n\ntype Options = { assertCtxFn?: (v: unknown, msg: string) => void };\ntype ContextOf<T> = React.Context<{ value: T } | undefined>;\ntype UseCtxFn<T> = () => T;\n\n/**\n * Creates and returns a Context and two hooks that return the context value.\n * The Context type is derived from the type passed in by the user.\n * The first hook returned guarantees that the context exists so the returned value is always CtxValue\n * The second hook makes no guarantees, so the returned value can be CtxValue | undefined\n */\nexport const createContextAndHook = <CtxVal>(\n  displayName: string,\n  options?: Options,\n): [ContextOf<CtxVal>, UseCtxFn<CtxVal>, UseCtxFn<CtxVal | Partial<CtxVal>>] => {\n  const { assertCtxFn = assertContextExists } = options || {};\n  const Ctx = React.createContext<{ value: CtxVal } | undefined>(undefined);\n  Ctx.displayName = displayName;\n\n  const useCtx = () => {\n    const ctx = React.useContext(Ctx);\n    assertCtxFn(ctx, `${displayName} not found`);\n    return (ctx as any).value as CtxVal;\n  };\n\n  const useCtxWithoutGuarantee = () => {\n    const ctx = React.useContext(Ctx);\n    return ctx ? ctx.value : {};\n  };\n\n  return [Ctx, useCtx, useCtxWithoutGuarantee];\n};\n", "export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch (err) {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch (err) {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch (err) {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n", "import { isProductionEnvironment, isTestEnvironment } from './utils/runtimeEnvironment';\n/**\n * Mark class method / function as deprecated.\n *\n * A console WARNING will be displayed when class method / function is invoked.\n *\n * Examples\n * 1. Deprecate class method\n * class Example {\n *   getSomething = (arg1, arg2) => {\n *       deprecated('Example.getSomething', 'Use `getSomethingElse` instead.');\n *       return `getSomethingValue:${arg1 || '-'}:${arg2 || '-'}`;\n *   };\n * }\n *\n * 2. Deprecate function\n * const getSomething = () => {\n *   deprecated('getSomething', 'Use `getSomethingElse` instead.');\n *   return 'getSomethingValue';\n * };\n */\nconst displayedWarnings = new Set<string>();\nexport const deprecated = (fnName: string, warning: string, key?: string): void => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\\n${warning}`,\n  );\n};\n/**\n * Mark class property as deprecated.\n *\n * A console WARNING will be displayed when class property is being accessed.\n *\n * 1. Deprecate class property\n * class Example {\n *   something: string;\n *   constructor(something: string) {\n *     this.something = something;\n *   }\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.');\n *\n * 2. Deprecate class static property\n * class Example {\n *   static something: string;\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.', true);\n */\ntype AnyClass = new (...args: any[]) => any;\n\nexport const deprecatedProperty = (cls: AnyClass, propName: string, warning: string, isStatic = false): void => {\n  const target = isStatic ? cls : cls.prototype;\n\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n\n/**\n * Mark object property as deprecated.\n *\n * A console WARNING will be displayed when object property is being accessed.\n *\n * 1. Deprecate object property\n * const obj = { something: 'aloha' };\n *\n * deprecatedObjectProperty(obj, 'something', 'Use `somethingElse` instead.');\n */\nexport const deprecatedObjectProperty = (\n  obj: Record<string, any>,\n  propName: string,\n  warning: string,\n  key?: string,\n): void => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n", "'use client';\nexport * from 'swr';\nexport { default as useSWR, SWRConfig } from 'swr';\nexport { default as useSWRInfinite } from 'swr/infinite';\n", "'use client';\n\nimport type {\n  ActiveSessionResource,\n  ClientResource,\n  LoadedClerk,\n  OrganizationInvitationResource,\n  OrganizationMembershipResource,\n  OrganizationResource,\n  UserResource,\n} from '@clerk/types';\nimport type { PropsWithChildren } from 'react';\nimport React from 'react';\n\nimport { deprecated } from '../deprecated';\nimport { SWRConfig } from './clerk-swr';\nimport { createContextAndHook } from './hooks/createContextAndHook';\n\nconst [ClerkInstanceContext, useClerkInstanceContext] = createContextAndHook<LoadedClerk>('ClerkInstanceContext');\nconst [UserContext, useUserContext] = createContextAndHook<UserResource | null | undefined>('UserContext');\nconst [ClientContext, useClientContext] = createContextAndHook<ClientResource | null | undefined>('ClientContext');\nconst [SessionContext, useSessionContext] = createContextAndHook<ActiveSessionResource | null | undefined>(\n  'SessionContext',\n);\n\ntype OrganizationContextProps = {\n  organization: OrganizationResource | null | undefined;\n\n  /**\n   * @deprecated This property will be dropped in the next major release.\n   * This property is only used in another deprecated part: `invitationList` from useOrganization\n   */\n  lastOrganizationInvitation: OrganizationInvitationResource | null | undefined;\n  /**\n   * @deprecated This property will be dropped in the next major release.\n   * This property is only used in another deprecated part: `membershipList` from useOrganization\n   */\n  lastOrganizationMember: OrganizationMembershipResource | null | undefined;\n};\nconst [OrganizationContextInternal, useOrganizationContext] = createContextAndHook<{\n  organization: OrganizationResource | null | undefined;\n  lastOrganizationInvitation: OrganizationInvitationResource | null | undefined;\n  lastOrganizationMember: OrganizationMembershipResource | null | undefined;\n}>('OrganizationContext');\n\nconst OrganizationProvider = ({\n  children,\n  organization,\n  lastOrganizationMember,\n  lastOrganizationInvitation,\n  swrConfig,\n}: PropsWithChildren<\n  OrganizationContextProps & {\n    // Exporting inferred types  directly from SWR will result in error while building declarations\n    swrConfig?: any;\n  }\n>) => {\n  return (\n    <SWRConfig value={swrConfig}>\n      <OrganizationContextInternal.Provider\n        value={{\n          value: {\n            organization,\n            lastOrganizationMember,\n            lastOrganizationInvitation,\n          },\n        }}\n      >\n        {children}\n      </OrganizationContextInternal.Provider>\n    </SWRConfig>\n  );\n};\n\n/**\n * @deprecated use OrganizationProvider instead\n */\nexport const OrganizationContext = (...args: Parameters<typeof OrganizationProvider>) => {\n  deprecated('OrganizationContext', 'Use `OrganizationProvider` instead');\n  return OrganizationProvider(...args);\n};\n\nexport {\n  ClientContext,\n  useClientContext,\n  OrganizationProvider,\n  useOrganizationContext,\n  UserContext,\n  useUserContext,\n  SessionContext,\n  useSessionContext,\n  ClerkInstanceContext,\n  useClerkInstanceContext,\n};\n", "'use client';\n\nimport { useCallback, useMemo, useRef, useState } from 'react';\n\nimport { useSWR, useSWRInfinite } from '../clerk-swr';\nimport type { CacheSetter, PaginatedResources, ValueOrSetter } from '../types';\n\nfunction getDifferentKeys(obj1: Record<string, unknown>, obj2: Record<string, unknown>): Record<string, unknown> {\n  const keysSet = new Set(Object.keys(obj2));\n  const differentKeysObject: Record<string, unknown> = {};\n\n  for (const key1 of Object.keys(obj1)) {\n    if (!keysSet.has(key1)) {\n      differentKeysObject[key1] = obj1[key1];\n    }\n  }\n\n  return differentKeysObject;\n}\n\ntype PagesOrInfiniteOptions = {\n  /**\n   * This the starting point for your fetched results. The initial value persists between re-renders\n   */\n  initialPage?: number;\n  /**\n   * Maximum number of items returned per request. The initial value persists between re-renders\n   */\n  pageSize?: number;\n};\n\nexport const useWithSafeValues = <T extends PagesOrInfiniteOptions>(params: T | true | undefined, defaultValues: T) => {\n  const shouldUseDefaults = typeof params === 'boolean' && params;\n\n  // Cache initialPage and initialPageSize until unmount\n  const initialPageRef = useRef(\n    shouldUseDefaults ? defaultValues.initialPage : params?.initialPage ?? defaultValues.initialPage,\n  );\n  const pageSizeRef = useRef(shouldUseDefaults ? defaultValues.pageSize : params?.pageSize ?? defaultValues.pageSize);\n\n  const newObj: Record<string, unknown> = {};\n  for (const key of Object.keys(defaultValues)) {\n    // @ts-ignore\n    newObj[key] = shouldUseDefaults ? defaultValues[key] : params?.[key] ?? defaultValues[key];\n  }\n\n  return {\n    ...newObj,\n    initialPage: initialPageRef.current,\n    pageSize: pageSizeRef.current,\n  } as T;\n};\n\ntype ArrayType<DataArray> = DataArray extends Array<infer ElementType> ? ElementType : never;\ntype ExtractData<Type> = Type extends { data: infer Data } ? ArrayType<Data> : Type;\n\ntype DefaultOptions = {\n  /**\n   * Persists the previous pages with new ones in the same array\n   */\n  infinite?: boolean;\n  /**\n   * Return the previous key's data until the new data has been loaded\n   */\n  keepPreviousData?: boolean;\n  /**\n   * Should a request be triggered\n   */\n  enabled?: boolean;\n};\n\ntype UsePagesOrInfinite = <\n  Params extends PagesOrInfiniteOptions,\n  FetcherReturnData extends Record<string, any>,\n  CacheKeys = Record<string, unknown>,\n  TOptions extends DefaultOptions = DefaultOptions,\n>(\n  /**\n   * The parameters will be passed to the fetcher\n   */\n  params: Params,\n  /**\n   * A Promise returning function to fetch your data\n   */\n  fetcher: ((p: Params) => FetcherReturnData | Promise<FetcherReturnData>) | undefined,\n  /**\n   * Internal configuration of the hook\n   */\n  options: TOptions,\n  cacheKeys: CacheKeys,\n) => PaginatedResources<ExtractData<FetcherReturnData>, TOptions['infinite']>;\n\nexport const usePagesOrInfinite: UsePagesOrInfinite = (params, fetcher, options, cacheKeys) => {\n  const [paginatedPage, setPaginatedPage] = useState(params.initialPage ?? 1);\n\n  // Cache initialPage and initialPageSize until unmount\n  const initialPageRef = useRef(params.initialPage ?? 1);\n  const pageSizeRef = useRef(params.pageSize ?? 10);\n\n  const enabled = options.enabled ?? true;\n  const triggerInfinite = options.infinite ?? false;\n  const keepPreviousData = options.keepPreviousData ?? false;\n\n  const pagesCacheKey = {\n    ...cacheKeys,\n    ...params,\n    initialPage: paginatedPage,\n    pageSize: pageSizeRef.current,\n  };\n\n  const {\n    data: swrData,\n    isValidating: swrIsValidating,\n    isLoading: swrIsLoading,\n    error: swrError,\n    mutate: swrMutate,\n  } = useSWR(\n    !triggerInfinite && !!fetcher && enabled ? pagesCacheKey : null,\n    cacheKeyParams => {\n      // @ts-ignore\n      const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n      // @ts-ignore\n      return fetcher?.(requestParams);\n    },\n    { keepPreviousData },\n  );\n\n  const {\n    data: swrInfiniteData,\n    isLoading: swrInfiniteIsLoading,\n    isValidating: swrInfiniteIsValidating,\n    error: swrInfiniteError,\n    size,\n    setSize,\n    mutate: swrInfiniteMutate,\n  } = useSWRInfinite(\n    pageIndex => {\n      if (!triggerInfinite || !enabled) {\n        return null;\n      }\n\n      return {\n        ...params,\n        ...cacheKeys,\n        initialPage: initialPageRef.current + pageIndex,\n        pageSize: pageSizeRef.current,\n      };\n    },\n    cacheKeyParams => {\n      // @ts-ignore\n      const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n      // @ts-ignore\n      return fetcher?.(requestParams);\n    },\n  );\n\n  const page = useMemo(() => {\n    if (triggerInfinite) {\n      return size;\n    }\n    return paginatedPage;\n  }, [triggerInfinite, size, paginatedPage]);\n\n  const fetchPage: ValueOrSetter<number> = useCallback(\n    numberOrgFn => {\n      if (triggerInfinite) {\n        void setSize(numberOrgFn);\n        return;\n      }\n      return setPaginatedPage(numberOrgFn);\n    },\n    [setSize],\n  );\n\n  const data = useMemo(() => {\n    if (triggerInfinite) {\n      return swrInfiniteData?.map(a => a?.data).flat() ?? [];\n    }\n    return swrData?.data ?? [];\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n\n  const count = useMemo(() => {\n    if (triggerInfinite) {\n      return swrInfiniteData?.[swrInfiniteData?.length - 1]?.total_count || 0;\n    }\n    return swrData?.total_count ?? 0;\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n\n  const isLoading = triggerInfinite ? swrInfiniteIsLoading : swrIsLoading;\n  const isFetching = triggerInfinite ? swrInfiniteIsValidating : swrIsValidating;\n  const isError = !!(triggerInfinite ? swrInfiniteError : swrError);\n  /**\n   * Helpers\n   */\n  const fetchNext = useCallback(() => {\n    fetchPage(n => Math.max(0, n + 1));\n  }, [fetchPage]);\n\n  const fetchPrevious = useCallback(() => {\n    fetchPage(n => Math.max(0, n - 1));\n  }, [fetchPage]);\n\n  const offsetCount = (initialPageRef.current - 1) * pageSizeRef.current;\n\n  const pageCount = Math.ceil((count - offsetCount) / pageSizeRef.current);\n  const hasNextPage = count - offsetCount * pageSizeRef.current > page * pageSizeRef.current;\n  const hasPreviousPage = (page - 1) * pageSizeRef.current > offsetCount * pageSizeRef.current;\n\n  const setData: CacheSetter = triggerInfinite\n    ? value =>\n        swrInfiniteMutate(value, {\n          revalidate: false,\n        })\n    : value =>\n        swrMutate(value, {\n          revalidate: false,\n        });\n\n  const revalidate = triggerInfinite ? () => swrInfiniteMutate() : () => swrMutate();\n\n  return {\n    data,\n    count,\n    isLoading,\n    isFetching,\n    isError,\n    page,\n    pageCount,\n    fetchPage,\n    fetchNext,\n    fetchPrevious,\n    hasNextPage,\n    hasPreviousPage,\n    // Let the hook return type define this type\n    revalidate: revalidate as any,\n    // Let the hook return type define this type\n    setData: setData as any,\n  };\n};\n", "import type {\n  ClerkPaginatedResponse,\n  ClerkPaginationParams,\n  GetDomainsParams,\n  GetInvitationsParams,\n  GetMembershipRequestParams,\n  GetMembershipsParams,\n  GetMembersParams,\n  GetPendingInvitationsParams,\n  OrganizationDomainResource,\n  OrganizationInvitationResource,\n  OrganizationMembershipRequestResource,\n  OrganizationMembershipResource,\n  OrganizationResource,\n} from '@clerk/types';\n\nimport { deprecated } from '../../deprecated';\nimport { useSWR } from '../clerk-swr';\nimport { useClerkInstanceContext, useOrganizationContext, useSessionContext } from '../contexts';\nimport type { PaginatedResources, PaginatedResourcesWithDefault } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\ntype UseOrganizationParams = {\n  /**\n   * @deprecated Use `invitations` instead\n   */\n  invitationList?: GetPendingInvitationsParams;\n  /**\n   * @deprecated Use `memberships` instead\n   */\n  membershipList?: GetMembershipsParams;\n  domains?:\n    | true\n    | (GetDomainsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  membershipRequests?:\n    | true\n    | (GetMembershipRequestParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  memberships?:\n    | true\n    | (GetMembersParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n\n  invitations?:\n    | true\n    | (GetInvitationsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n};\n\ntype UseOrganization = <T extends UseOrganizationParams>(\n  params?: T,\n) =>\n  | {\n      isLoaded: false;\n      organization: undefined;\n      /**\n       * @deprecated Use `invitations` instead\n       */\n      invitationList: undefined;\n      /**\n       * @deprecated Use `memberships` instead\n       */\n      membershipList: undefined;\n      membership: undefined;\n      domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;\n      membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;\n      memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;\n    }\n  | {\n      isLoaded: true;\n      organization: OrganizationResource;\n      /**\n       * @deprecated Use `invitations` instead\n       */\n      invitationList: undefined;\n      /**\n       * @deprecated Use `memberships` instead\n       */\n      membershipList: undefined;\n      membership: undefined;\n      domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;\n      membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;\n      memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;\n    }\n  | {\n      isLoaded: boolean;\n      organization: OrganizationResource | null;\n      /**\n       * @deprecated Use `invitations` instead\n       */\n      invitationList: OrganizationInvitationResource[] | null | undefined;\n      /**\n       * @deprecated Use `memberships` instead\n       */\n      membershipList: OrganizationMembershipResource[] | null | undefined;\n      membership: OrganizationMembershipResource | null | undefined;\n      domains: PaginatedResources<\n        OrganizationDomainResource,\n        T['membershipRequests'] extends { infinite: true } ? true : false\n      > | null;\n      membershipRequests: PaginatedResources<\n        OrganizationMembershipRequestResource,\n        T['membershipRequests'] extends { infinite: true } ? true : false\n      > | null;\n      memberships: PaginatedResources<\n        OrganizationMembershipResource,\n        T['memberships'] extends { infinite: true } ? true : false\n      > | null;\n      invitations: PaginatedResources<\n        OrganizationInvitationResource,\n        T['invitations'] extends { infinite: true } ? true : false\n      > | null;\n    };\n\nconst undefinedPaginatedResource = {\n  data: undefined,\n  count: undefined,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: undefined,\n  pageCount: undefined,\n  fetchPage: undefined,\n  fetchNext: undefined,\n  fetchPrevious: undefined,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: undefined,\n  setData: undefined,\n} as const;\n\nexport const useOrganization: UseOrganization = params => {\n  const {\n    invitationList: invitationListParams,\n    membershipList: membershipListParams,\n    domains: domainListParams,\n    membershipRequests: membershipRequestsListParams,\n    memberships: membersListParams,\n    invitations: invitationsListParams,\n  } = params || {};\n  const { organization, lastOrganizationMember, lastOrganizationInvitation } = useOrganizationContext();\n  const session = useSessionContext();\n\n  const domainSafeValues = useWithSafeValues(domainListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n    enrollmentMode: undefined,\n  });\n\n  const membershipRequestSafeValues = useWithSafeValues(membershipRequestsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const membersSafeValues = useWithSafeValues(membersListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    role: undefined,\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const invitationsSafeValues = useWithSafeValues(invitationsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: ['pending'],\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const clerk = useClerkInstanceContext();\n\n  const shouldFetch = !!(clerk.loaded && session && organization);\n\n  const domainParams =\n    typeof domainListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: domainSafeValues.initialPage,\n          pageSize: domainSafeValues.pageSize,\n          enrollmentMode: domainSafeValues.enrollmentMode,\n        };\n\n  const membershipRequestParams =\n    typeof membershipRequestsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: membershipRequestSafeValues.initialPage,\n          pageSize: membershipRequestSafeValues.pageSize,\n          status: membershipRequestSafeValues.status,\n        };\n\n  const membersParams =\n    typeof membersListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: membersSafeValues.initialPage,\n          pageSize: membersSafeValues.pageSize,\n          role: membersSafeValues.role,\n        };\n\n  const invitationsParams =\n    typeof invitationsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: invitationsSafeValues.initialPage,\n          pageSize: invitationsSafeValues.pageSize,\n          status: invitationsSafeValues.status,\n        };\n\n  const domains = usePagesOrInfinite<GetDomainsParams, ClerkPaginatedResponse<OrganizationDomainResource>>(\n    {\n      ...domainParams,\n    },\n    organization?.getDomains,\n    {\n      keepPreviousData: domainSafeValues.keepPreviousData,\n      infinite: domainSafeValues.infinite,\n      enabled: !!domainParams,\n    },\n    {\n      type: 'domains',\n      organizationId: organization?.id,\n    },\n  );\n\n  const membershipRequests = usePagesOrInfinite<\n    GetMembershipRequestParams,\n    ClerkPaginatedResponse<OrganizationMembershipRequestResource>\n  >(\n    {\n      ...membershipRequestParams,\n    },\n    organization?.getMembershipRequests,\n    {\n      keepPreviousData: membershipRequestSafeValues.keepPreviousData,\n      infinite: membershipRequestSafeValues.infinite,\n      enabled: !!membershipRequestParams,\n    },\n    {\n      type: 'membershipRequests',\n      organizationId: organization?.id,\n    },\n  );\n\n  const memberships = usePagesOrInfinite<GetMembersParams, ClerkPaginatedResponse<OrganizationMembershipResource>>(\n    {\n      ...membersParams,\n      paginated: true,\n    } as any,\n    organization?.getMemberships as unknown as any,\n    {\n      keepPreviousData: membersSafeValues.keepPreviousData,\n      infinite: membersSafeValues.infinite,\n      enabled: !!membersParams,\n    },\n    {\n      type: 'members',\n      organizationId: organization?.id,\n    },\n  );\n\n  const invitations = usePagesOrInfinite<GetInvitationsParams, ClerkPaginatedResponse<OrganizationInvitationResource>>(\n    {\n      ...invitationsParams,\n    },\n    organization?.getInvitations,\n    {\n      keepPreviousData: invitationsSafeValues.keepPreviousData,\n      infinite: invitationsSafeValues.infinite,\n      enabled: !!invitationsParams,\n    },\n    {\n      type: 'invitations',\n      organizationId: organization?.id,\n    },\n  );\n\n  // Some gymnastics to adhere to the rules of hooks\n  // We need to make sure useSWR is called on every render\n  const pendingInvitations = !clerk.loaded\n    ? () => [] as OrganizationInvitationResource[]\n    : () => clerk.organization?.getPendingInvitations(invitationListParams);\n\n  const currentOrganizationMemberships = !clerk.loaded\n    ? () => [] as OrganizationMembershipResource[]\n    : () => clerk.organization?.getMemberships(membershipListParams);\n\n  if (invitationListParams) {\n    deprecated('invitationList in useOrganization', 'Use the `invitations` property and return value instead.');\n  }\n\n  const {\n    data: invitationList,\n    isValidating: isInvitationsLoading,\n    mutate: mutateInvitationList,\n  } = useSWR(\n    shouldFetch && invitationListParams\n      ? cacheKey('invites', organization, lastOrganizationInvitation, invitationListParams)\n      : null,\n    pendingInvitations,\n  );\n\n  if (membershipListParams) {\n    deprecated('membershipList in useOrganization', 'Use the `memberships` property and return value instead.');\n  }\n\n  const {\n    data: membershipList,\n    isValidating: isMembershipsLoading,\n    mutate: mutateMembershipList,\n  } = useSWR(\n    shouldFetch && membershipListParams\n      ? cacheKey('memberships', organization, lastOrganizationMember, membershipListParams)\n      : null,\n    currentOrganizationMemberships,\n  );\n\n  if (organization === undefined) {\n    return {\n      isLoaded: false,\n      organization: undefined,\n      invitationList: undefined,\n      membershipList: undefined,\n      membership: undefined,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource,\n    };\n  }\n\n  if (organization === null) {\n    return {\n      isLoaded: true,\n      organization: null,\n      invitationList: null,\n      membershipList: null,\n      membership: null,\n      domains: null,\n      membershipRequests: null,\n      memberships: null,\n      invitations: null,\n    };\n  }\n\n  /** In SSR context we include only the organization object when loadOrg is set to true. */\n  if (!clerk.loaded && organization) {\n    return {\n      isLoaded: true,\n      organization,\n      invitationList: undefined,\n      membershipList: undefined,\n      membership: undefined,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource,\n    };\n  }\n\n  return {\n    isLoaded: !isMembershipsLoading && !isInvitationsLoading,\n    organization,\n    membershipList,\n    membership: getCurrentOrganizationMembership(session!.user.organizationMemberships, organization.id), // your membership in the current org\n    invitationList,\n    unstable__mutate: () => {\n      void mutateMembershipList();\n      void mutateInvitationList();\n    },\n    domains,\n    membershipRequests,\n    memberships,\n    invitations,\n  };\n};\n\nfunction getCurrentOrganizationMembership(\n  organizationMemberships: OrganizationMembershipResource[],\n  activeOrganizationId: string,\n) {\n  return organizationMemberships.find(\n    organizationMembership => organizationMembership.organization.id === activeOrganizationId,\n  );\n}\n\nfunction cacheKey(\n  type: 'memberships' | 'invites',\n  organization: OrganizationResource,\n  resource: OrganizationInvitationResource | OrganizationMembershipResource | null | undefined,\n  pagination: ClerkPaginationParams,\n) {\n  return [type, organization.id, resource?.id, resource?.updatedAt, pagination.offset, pagination.limit]\n    .filter(Boolean)\n    .join('-');\n}\n", "import type {\n  ClerkPaginatedResponse,\n  CreateOrganizationParams,\n  GetUserOrganizationInvitationsParams,\n  GetUserOrganizationMembershipParams,\n  GetUserOrganizationSuggestionsParams,\n  OrganizationMembershipResource,\n  OrganizationResource,\n  OrganizationSuggestionResource,\n  SetActive,\n  UserOrganizationInvitationResource,\n} from '@clerk/types';\n\nimport { deprecatedObjectProperty } from '../../deprecated';\nimport { useClerkInstanceContext, useUserContext } from '../contexts';\nimport type { PaginatedResources, PaginatedResourcesWithDefault } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\ntype UseOrganizationListParams = {\n  userMemberships?:\n    | true\n    | (GetUserOrganizationMembershipParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  userInvitations?:\n    | true\n    | (GetUserOrganizationInvitationsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  userSuggestions?:\n    | true\n    | (GetUserOrganizationSuggestionsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n};\n\ntype OrganizationList = ReturnType<typeof createOrganizationList>;\nconst undefinedPaginatedResource = {\n  data: undefined,\n  count: undefined,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: undefined,\n  pageCount: undefined,\n  fetchPage: undefined,\n  fetchNext: undefined,\n  fetchPrevious: undefined,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: undefined,\n  setData: undefined,\n} as const;\n\ntype UseOrganizationList = <T extends UseOrganizationListParams>(\n  params?: T,\n) =>\n  | {\n      isLoaded: false;\n      /**\n       * @deprecated Use userMemberships instead\n       */\n      organizationList: undefined;\n      createOrganization: undefined;\n      setActive: undefined;\n      userMemberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      userInvitations: PaginatedResourcesWithDefault<UserOrganizationInvitationResource>;\n      userSuggestions: PaginatedResourcesWithDefault<OrganizationSuggestionResource>;\n    }\n  | {\n      isLoaded: boolean;\n      /**\n       * @deprecated Use userMemberships instead\n       */\n      organizationList: OrganizationList;\n      createOrganization: (params: CreateOrganizationParams) => Promise<OrganizationResource>;\n      setActive: SetActive;\n      userMemberships: PaginatedResources<\n        OrganizationMembershipResource,\n        T['userMemberships'] extends { infinite: true } ? true : false\n      >;\n      userInvitations: PaginatedResources<\n        UserOrganizationInvitationResource,\n        T['userInvitations'] extends { infinite: true } ? true : false\n      >;\n      userSuggestions: PaginatedResources<\n        OrganizationSuggestionResource,\n        T['userSuggestions'] extends { infinite: true } ? true : false\n      >;\n    };\n\nexport const useOrganizationList: UseOrganizationList = params => {\n  const { userMemberships, userInvitations, userSuggestions } = params || {};\n\n  const userMembershipsSafeValues = useWithSafeValues(userMemberships, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const userInvitationsSafeValues = useWithSafeValues(userInvitations, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const userSuggestionsSafeValues = useWithSafeValues(userSuggestions, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const clerk = useClerkInstanceContext();\n  const user = useUserContext();\n\n  const userMembershipsParams =\n    typeof userMemberships === 'undefined'\n      ? undefined\n      : {\n          initialPage: userMembershipsSafeValues.initialPage,\n          pageSize: userMembershipsSafeValues.pageSize,\n        };\n\n  const userInvitationsParams =\n    typeof userInvitations === 'undefined'\n      ? undefined\n      : {\n          initialPage: userInvitationsSafeValues.initialPage,\n          pageSize: userInvitationsSafeValues.pageSize,\n          status: userInvitationsSafeValues.status,\n        };\n\n  const userSuggestionsParams =\n    typeof userSuggestions === 'undefined'\n      ? undefined\n      : {\n          initialPage: userSuggestionsSafeValues.initialPage,\n          pageSize: userSuggestionsSafeValues.pageSize,\n          status: userSuggestionsSafeValues.status,\n        };\n\n  const isClerkLoaded = !!(clerk.loaded && user);\n\n  const memberships = usePagesOrInfinite<\n    GetUserOrganizationMembershipParams,\n    ClerkPaginatedResponse<OrganizationMembershipResource>\n  >(\n    {\n      ...userMembershipsParams,\n      paginated: true,\n    } as any,\n    user?.getOrganizationMemberships as unknown as any,\n    {\n      keepPreviousData: userMembershipsSafeValues.keepPreviousData,\n      infinite: userMembershipsSafeValues.infinite,\n      enabled: !!userMembershipsParams,\n    },\n    {\n      type: 'userMemberships',\n      userId: user?.id,\n    },\n  );\n\n  const invitations = usePagesOrInfinite<\n    GetUserOrganizationInvitationsParams,\n    ClerkPaginatedResponse<UserOrganizationInvitationResource>\n  >(\n    {\n      ...userInvitationsParams,\n    },\n    user?.getOrganizationInvitations,\n    {\n      keepPreviousData: userInvitationsSafeValues.keepPreviousData,\n      infinite: userInvitationsSafeValues.infinite,\n      enabled: !!userInvitationsParams,\n    },\n    {\n      type: 'userInvitations',\n      userId: user?.id,\n    },\n  );\n\n  const suggestions = usePagesOrInfinite<\n    GetUserOrganizationSuggestionsParams,\n    ClerkPaginatedResponse<OrganizationSuggestionResource>\n  >(\n    {\n      ...userSuggestionsParams,\n    },\n    user?.getOrganizationSuggestions,\n    {\n      keepPreviousData: userSuggestionsSafeValues.keepPreviousData,\n      infinite: userSuggestionsSafeValues.infinite,\n      enabled: !!userSuggestionsParams,\n    },\n    {\n      type: 'userSuggestions',\n      userId: user?.id,\n    },\n  );\n\n  // TODO: Properly check for SSR user values\n  if (!isClerkLoaded) {\n    return {\n      isLoaded: false,\n      organizationList: undefined,\n      createOrganization: undefined,\n      setActive: undefined,\n      userMemberships: undefinedPaginatedResource,\n      userInvitations: undefinedPaginatedResource,\n      userSuggestions: undefinedPaginatedResource,\n    };\n  }\n\n  const result = {\n    isLoaded: isClerkLoaded,\n    organizationList: createOrganizationList(user.organizationMemberships),\n    setActive: clerk.setActive,\n    createOrganization: clerk.createOrganization,\n    userMemberships: memberships,\n    userInvitations: invitations,\n    userSuggestions: suggestions,\n  };\n  deprecatedObjectProperty(result, 'organizationList', 'Use `userMemberships` instead.');\n\n  return result;\n};\n\nfunction createOrganizationList(organizationMemberships: OrganizationMembershipResource[]) {\n  return organizationMemberships.map(organizationMembership => ({\n    membership: organizationMembership,\n    organization: organizationMembership.organization,\n  }));\n}\n", "import type { CreateOrganizationParams, OrganizationMembershipResource, OrganizationResource } from '@clerk/types';\n\nimport { deprecated } from '../../deprecated';\nimport { useClerkInstanceContext } from '../contexts';\n\ntype UseOrganizationsReturn =\n  | {\n      isLoaded: false;\n\n      /**\n       * @deprecated Use `createOrganization` from `useOrganizationList`\n       * Example: `const {createOrganization} = useOrganizationList()`\n       */\n      createOrganization: undefined;\n\n      /**\n       * @deprecated Use `memberships` from `useOrganization`\n       * Example: `const {memberships} = useOrganization()`\n       */\n      getOrganizationMemberships: undefined;\n\n      /**\n       * @deprecated Use `getOrganization` from `useClerk`\n       * Example: `const {getOrganization} = useClerk()`\n       */\n      getOrganization: undefined;\n    }\n  | {\n      isLoaded: true;\n      /**\n       * @deprecated Use `createOrganization` from `useOrganizationList`\n       * Example: `const {createOrganization} = useOrganizationList()`\n       */\n      createOrganization: (params: CreateOrganizationParams) => Promise<OrganizationResource>;\n\n      /**\n       * @deprecated Use `memberships` from `useOrganization`\n       * Example: `const {memberships} = useOrganization()`\n       */\n      getOrganizationMemberships: () => Promise<OrganizationMembershipResource[]>;\n\n      /**\n       * @deprecated Use `getOrganization` from `useClerk`\n       * Example: `const {getOrganization} = useClerk()`\n       */\n      getOrganization: (organizationId: string) => Promise<OrganizationResource | undefined>;\n    };\n\ntype UseOrganizations = () => UseOrganizationsReturn;\n\n/**\n * @deprecated Use useOrganizationList, useOrganization, or useClerk instead\n */\nexport const useOrganizations: UseOrganizations = () => {\n  deprecated('useOrganizations', 'Use useOrganizationList, useOrganization, or useClerk instead.');\n  const clerk = useClerkInstanceContext();\n  if (!clerk.loaded) {\n    return {\n      isLoaded: false,\n      createOrganization: undefined,\n      getOrganizationMemberships: undefined,\n      getOrganization: undefined,\n    };\n  }\n\n  return {\n    isLoaded: true,\n    createOrganization: clerk.createOrganization,\n    getOrganizationMemberships: clerk.getOrganizationMemberships,\n    getOrganization: clerk.getOrganization,\n  };\n};\n", "import React from 'react';\n\nexport const useSafeLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACCA,mBAAkB;AAEX,SAAS,oBAAoB,YAAqB,UAA2D;AAClH,MAAI,CAAC,YAAY;AACf,UAAM,OAAO,aAAa,WAAW,IAAI,MAAM,QAAQ,IAAI,IAAI,MAAM,GAAG,SAAS,WAAW,YAAY;AAAA,EAC1G;AACF;AAYO,IAAM,uBAAuB,CAClC,aACA,YAC8E;AAC9E,QAAM,EAAE,cAAc,oBAAoB,IAAI,WAAW,CAAC;AAC1D,QAAM,MAAM,aAAAA,QAAM,cAA6C,MAAS;AACxE,MAAI,cAAc;AAElB,QAAM,SAAS,MAAM;AACnB,UAAM,MAAM,aAAAA,QAAM,WAAW,GAAG;AAChC,gBAAY,KAAK,GAAG,WAAW,YAAY;AAC3C,WAAQ,IAAY;AAAA,EACtB;AAEA,QAAM,yBAAyB,MAAM;AACnC,UAAM,MAAM,aAAAA,QAAM,WAAW,GAAG;AAChC,WAAO,MAAM,IAAI,QAAQ,CAAC;AAAA,EAC5B;AAEA,SAAO,CAAC,KAAK,QAAQ,sBAAsB;AAC7C;;;AC5BO,IAAM,oBAAoB,MAAe;AAC9C,MAAI;AACF,WAAO,QAAQ,IAAI,aAAa;AAAA,EAElC,SAAS,KAAK;AAAA,EAAC;AAGf,SAAO;AACT;AAEO,IAAM,0BAA0B,MAAe;AACpD,MAAI;AACF,WAAO,QAAQ,IAAI,aAAa;AAAA,EAElC,SAAS,KAAK;AAAA,EAAC;AAGf,SAAO;AACT;;;ACRA,IAAM,oBAAoB,oBAAI,IAAY;AACnC,IAAM,aAAa,CAAC,QAAgB,SAAiB,QAAuB;AACjF,QAAM,cAAc,kBAAkB,KAAK,wBAAwB;AACnE,QAAM,YAAY,oBAAO;AACzB,MAAI,kBAAkB,IAAI,SAAS,KAAK,aAAa;AACnD;AAAA,EACF;AACA,oBAAkB,IAAI,SAAS;AAE/B,UAAQ;AAAA,IACN,iCAAiC,MAAM;AAAA,EAAmE,OAAO;AAAA,EACnH;AACF;AAkDO,IAAM,2BAA2B,CACtC,KACA,UACA,SACA,QACS;AACT,MAAI,QAAQ,IAAI,QAAQ;AACxB,SAAO,eAAe,KAAK,UAAU;AAAA,IACnC,MAAM;AACJ,iBAAW,UAAU,SAAS,GAAG;AACjC,aAAO;AAAA,IACT;AAAA,IACA,IAAI,GAAY;AACd,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;;;ACnGA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,8BAAc;AACd,iBAA6C;AAC7C,sBAA0C;;;ACS1C,IAAAC,gBAAkB;AAMlB,IAAM,CAAC,sBAAsB,uBAAuB,IAAI,qBAAkC,sBAAsB;AAChH,IAAM,CAAC,aAAa,cAAc,IAAI,qBAAsD,aAAa;AACzG,IAAM,CAAC,eAAe,gBAAgB,IAAI,qBAAwD,eAAe;AACjH,IAAM,CAAC,gBAAgB,iBAAiB,IAAI;AAAA,EAC1C;AACF;AAgBA,IAAM,CAAC,6BAA6B,sBAAsB,IAAI,qBAI3D,qBAAqB;AAExB,IAAM,uBAAuB,CAAC;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAKM;AACJ,SACE,8BAAAC,QAAA,cAAC,wBAAU,OAAO,aAChB,8BAAAA,QAAA;AAAA,IAAC,4BAA4B;AAAA,IAA5B;AAAA,MACC,OAAO;AAAA,QACL,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA;AAAA,IAEC;AAAA,EACH,CACF;AAEJ;AAKO,IAAM,sBAAsB,IAAI,SAAkD;AACvF,aAAW,uBAAuB,oCAAoC;AACtE,SAAO,qBAAqB,GAAG,IAAI;AACrC;;;AC9EA,IAAAC,gBAAuD;AAKvD,SAAS,iBAAiB,MAA+B,MAAwD;AAC/G,QAAM,UAAU,IAAI,IAAI,OAAO,KAAK,IAAI,CAAC;AACzC,QAAM,sBAA+C,CAAC;AAEtD,aAAW,QAAQ,OAAO,KAAK,IAAI,GAAG;AACpC,QAAI,CAAC,QAAQ,IAAI,IAAI,GAAG;AACtB,0BAAoB,IAAI,IAAI,KAAK,IAAI;AAAA,IACvC;AAAA,EACF;AAEA,SAAO;AACT;AAaO,IAAM,oBAAoB,CAAmC,QAA8B,kBAAqB;AA/BvH;AAgCE,QAAM,oBAAoB,OAAO,WAAW,aAAa;AAGzD,QAAM,qBAAiB;AAAA,IACrB,oBAAoB,cAAc,eAAc,sCAAQ,gBAAR,YAAuB,cAAc;AAAA,EACvF;AACA,QAAM,kBAAc,sBAAO,oBAAoB,cAAc,YAAW,sCAAQ,aAAR,YAAoB,cAAc,QAAQ;AAElH,QAAM,SAAkC,CAAC;AACzC,aAAW,OAAO,OAAO,KAAK,aAAa,GAAG;AAE5C,WAAO,GAAG,IAAI,oBAAoB,cAAc,GAAG,KAAI,sCAAS,SAAT,YAAiB,cAAc,GAAG;AAAA,EAC3F;AAEA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,aAAa,eAAe;AAAA,IAC5B,UAAU,YAAY;AAAA,EACxB;AACF;AAyCO,IAAM,qBAAyC,CAAC,QAAQ,SAAS,SAAS,cAAc;AA5F/F;AA6FE,QAAM,CAAC,eAAe,gBAAgB,QAAI,yBAAS,YAAO,gBAAP,YAAsB,CAAC;AAG1E,QAAM,qBAAiB,uBAAO,YAAO,gBAAP,YAAsB,CAAC;AACrD,QAAM,kBAAc,uBAAO,YAAO,aAAP,YAAmB,EAAE;AAEhD,QAAM,WAAU,aAAQ,YAAR,YAAmB;AACnC,QAAM,mBAAkB,aAAQ,aAAR,YAAoB;AAC5C,QAAM,oBAAmB,aAAQ,qBAAR,YAA4B;AAErD,QAAM,gBAAgB;AAAA,IACpB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,aAAa;AAAA,IACb,UAAU,YAAY;AAAA,EACxB;AAEA,QAAM;AAAA,IACJ,MAAM;AAAA,IACN,cAAc;AAAA,IACd,WAAW;AAAA,IACX,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,QAAI;AAAA,IACF,CAAC,mBAAmB,CAAC,CAAC,WAAW,UAAU,gBAAgB;AAAA,IAC3D,oBAAkB;AAEhB,YAAM,gBAAgB,iBAAiB,gBAAgB,SAAS;AAEhE,aAAO,mCAAU;AAAA,IACnB;AAAA,IACA,EAAE,iBAAiB;AAAA,EACrB;AAEA,QAAM;AAAA,IACJ,MAAM;AAAA,IACN,WAAW;AAAA,IACX,cAAc;AAAA,IACd,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACV,QAAI;AAAA,IACF,eAAa;AACX,UAAI,CAAC,mBAAmB,CAAC,SAAS;AAChC,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,QACH,aAAa,eAAe,UAAU;AAAA,QACtC,UAAU,YAAY;AAAA,MACxB;AAAA,IACF;AAAA,IACA,oBAAkB;AAEhB,YAAM,gBAAgB,iBAAiB,gBAAgB,SAAS;AAEhE,aAAO,mCAAU;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,WAAO,uBAAQ,MAAM;AACzB,QAAI,iBAAiB;AACnB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,GAAG,CAAC,iBAAiB,MAAM,aAAa,CAAC;AAEzC,QAAM,gBAAmC;AAAA,IACvC,iBAAe;AACb,UAAI,iBAAiB;AACnB,aAAK,QAAQ,WAAW;AACxB;AAAA,MACF;AACA,aAAO,iBAAiB,WAAW;AAAA,IACrC;AAAA,IACA,CAAC,OAAO;AAAA,EACV;AAEA,QAAM,WAAO,uBAAQ,MAAM;AA9K7B,QAAAC,KAAAC;AA+KI,QAAI,iBAAiB;AACnB,cAAOD,MAAA,mDAAiB,IAAI,OAAK,uBAAG,MAAM,WAAnC,OAAAA,MAA6C,CAAC;AAAA,IACvD;AACA,YAAOC,MAAA,mCAAS,SAAT,OAAAA,MAAiB,CAAC;AAAA,EAC3B,GAAG,CAAC,iBAAiB,SAAS,eAAe,CAAC;AAE9C,QAAM,YAAQ,uBAAQ,MAAM;AArL9B,QAAAD,KAAAC;AAsLI,QAAI,iBAAiB;AACnB,eAAOD,MAAA,oDAAkB,mDAAiB,UAAS,OAA5C,gBAAAA,IAAgD,gBAAe;AAAA,IACxE;AACA,YAAOC,MAAA,mCAAS,gBAAT,OAAAA,MAAwB;AAAA,EACjC,GAAG,CAAC,iBAAiB,SAAS,eAAe,CAAC;AAE9C,QAAM,YAAY,kBAAkB,uBAAuB;AAC3D,QAAM,aAAa,kBAAkB,0BAA0B;AAC/D,QAAM,UAAU,CAAC,EAAE,kBAAkB,mBAAmB;AAIxD,QAAM,gBAAY,2BAAY,MAAM;AAClC,cAAU,OAAK,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AAAA,EACnC,GAAG,CAAC,SAAS,CAAC;AAEd,QAAM,oBAAgB,2BAAY,MAAM;AACtC,cAAU,OAAK,KAAK,IAAI,GAAG,IAAI,CAAC,CAAC;AAAA,EACnC,GAAG,CAAC,SAAS,CAAC;AAEd,QAAM,eAAe,eAAe,UAAU,KAAK,YAAY;AAE/D,QAAM,YAAY,KAAK,MAAM,QAAQ,eAAe,YAAY,OAAO;AACvE,QAAM,cAAc,QAAQ,cAAc,YAAY,UAAU,OAAO,YAAY;AACnF,QAAM,mBAAmB,OAAO,KAAK,YAAY,UAAU,cAAc,YAAY;AAErF,QAAM,UAAuB,kBACzB,WACE,kBAAkB,OAAO;AAAA,IACvB,YAAY;AAAA,EACd,CAAC,IACH,WACE,UAAU,OAAO;AAAA,IACf,YAAY;AAAA,EACd,CAAC;AAEP,QAAM,aAAa,kBAAkB,MAAM,kBAAkB,IAAI,MAAM,UAAU;AAEjF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,EACF;AACF;;;ACjHA,IAAM,6BAA6B;AAAA,EACjC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,MAAM;AAAA,EACN,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,eAAe;AAAA,EACf,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,SAAS;AACX;AAEO,IAAM,kBAAmC,YAAU;AACxD,QAAM;AAAA,IACJ,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,oBAAoB;AAAA,IACpB,aAAa;AAAA,IACb,aAAa;AAAA,EACf,IAAI,UAAU,CAAC;AACf,QAAM,EAAE,cAAc,wBAAwB,2BAA2B,IAAI,uBAAuB;AACpG,QAAM,UAAU,kBAAkB;AAElC,QAAM,mBAAmB,kBAAkB,kBAAkB;AAAA,IAC3D,aAAa;AAAA,IACb,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,gBAAgB;AAAA,EAClB,CAAC;AAED,QAAM,8BAA8B,kBAAkB,8BAA8B;AAAA,IAClF,aAAa;AAAA,IACb,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,UAAU;AAAA,EACZ,CAAC;AAED,QAAM,oBAAoB,kBAAkB,mBAAmB;AAAA,IAC7D,aAAa;AAAA,IACb,UAAU;AAAA,IACV,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,UAAU;AAAA,EACZ,CAAC;AAED,QAAM,wBAAwB,kBAAkB,uBAAuB;AAAA,IACrE,aAAa;AAAA,IACb,UAAU;AAAA,IACV,QAAQ,CAAC,SAAS;AAAA,IAClB,kBAAkB;AAAA,IAClB,UAAU;AAAA,EACZ,CAAC;AAED,QAAM,QAAQ,wBAAwB;AAEtC,QAAM,cAAc,CAAC,EAAE,MAAM,UAAU,WAAW;AAElD,QAAM,eACJ,OAAO,qBAAqB,cACxB,SACA;AAAA,IACE,aAAa,iBAAiB;AAAA,IAC9B,UAAU,iBAAiB;AAAA,IAC3B,gBAAgB,iBAAiB;AAAA,EACnC;AAEN,QAAM,0BACJ,OAAO,iCAAiC,cACpC,SACA;AAAA,IACE,aAAa,4BAA4B;AAAA,IACzC,UAAU,4BAA4B;AAAA,IACtC,QAAQ,4BAA4B;AAAA,EACtC;AAEN,QAAM,gBACJ,OAAO,sBAAsB,cACzB,SACA;AAAA,IACE,aAAa,kBAAkB;AAAA,IAC/B,UAAU,kBAAkB;AAAA,IAC5B,MAAM,kBAAkB;AAAA,EAC1B;AAEN,QAAM,oBACJ,OAAO,0BAA0B,cAC7B,SACA;AAAA,IACE,aAAa,sBAAsB;AAAA,IACnC,UAAU,sBAAsB;AAAA,IAChC,QAAQ,sBAAsB;AAAA,EAChC;AAEN,QAAM,UAAU;AAAA,IACd;AAAA,MACE,GAAG;AAAA,IACL;AAAA,IACA,6CAAc;AAAA,IACd;AAAA,MACE,kBAAkB,iBAAiB;AAAA,MACnC,UAAU,iBAAiB;AAAA,MAC3B,SAAS,CAAC,CAAC;AAAA,IACb;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,gBAAgB,6CAAc;AAAA,IAChC;AAAA,EACF;AAEA,QAAM,qBAAqB;AAAA,IAIzB;AAAA,MACE,GAAG;AAAA,IACL;AAAA,IACA,6CAAc;AAAA,IACd;AAAA,MACE,kBAAkB,4BAA4B;AAAA,MAC9C,UAAU,4BAA4B;AAAA,MACtC,SAAS,CAAC,CAAC;AAAA,IACb;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,gBAAgB,6CAAc;AAAA,IAChC;AAAA,EACF;AAEA,QAAM,cAAc;AAAA,IAClB;AAAA,MACE,GAAG;AAAA,MACH,WAAW;AAAA,IACb;AAAA,IACA,6CAAc;AAAA,IACd;AAAA,MACE,kBAAkB,kBAAkB;AAAA,MACpC,UAAU,kBAAkB;AAAA,MAC5B,SAAS,CAAC,CAAC;AAAA,IACb;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,gBAAgB,6CAAc;AAAA,IAChC;AAAA,EACF;AAEA,QAAM,cAAc;AAAA,IAClB;AAAA,MACE,GAAG;AAAA,IACL;AAAA,IACA,6CAAc;AAAA,IACd;AAAA,MACE,kBAAkB,sBAAsB;AAAA,MACxC,UAAU,sBAAsB;AAAA,MAChC,SAAS,CAAC,CAAC;AAAA,IACb;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,gBAAgB,6CAAc;AAAA,IAChC;AAAA,EACF;AAIA,QAAM,qBAAqB,CAAC,MAAM,SAC9B,MAAM,CAAC,IACP,MAAG;AA1ST;AA0SY,uBAAM,iBAAN,mBAAoB,sBAAsB;AAAA;AAEpD,QAAM,iCAAiC,CAAC,MAAM,SAC1C,MAAM,CAAC,IACP,MAAG;AA9ST;AA8SY,uBAAM,iBAAN,mBAAoB,eAAe;AAAA;AAE7C,MAAI,sBAAsB;AACxB,eAAW,qCAAqC,0DAA0D;AAAA,EAC5G;AAEA,QAAM;AAAA,IACJ,MAAM;AAAA,IACN,cAAc;AAAA,IACd,QAAQ;AAAA,EACV,QAAI;AAAA,IACF,eAAe,uBACX,SAAS,WAAW,cAAc,4BAA4B,oBAAoB,IAClF;AAAA,IACJ;AAAA,EACF;AAEA,MAAI,sBAAsB;AACxB,eAAW,qCAAqC,0DAA0D;AAAA,EAC5G;AAEA,QAAM;AAAA,IACJ,MAAM;AAAA,IACN,cAAc;AAAA,IACd,QAAQ;AAAA,EACV,QAAI;AAAA,IACF,eAAe,uBACX,SAAS,eAAe,cAAc,wBAAwB,oBAAoB,IAClF;AAAA,IACJ;AAAA,EACF;AAEA,MAAI,iBAAiB,QAAW;AAC9B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,oBAAoB;AAAA,MACpB,aAAa;AAAA,MACb,aAAa;AAAA,IACf;AAAA,EACF;AAEA,MAAI,iBAAiB,MAAM;AACzB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,oBAAoB;AAAA,MACpB,aAAa;AAAA,MACb,aAAa;AAAA,IACf;AAAA,EACF;AAGA,MAAI,CAAC,MAAM,UAAU,cAAc;AACjC,WAAO;AAAA,MACL,UAAU;AAAA,MACV;AAAA,MACA,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,oBAAoB;AAAA,MACpB,aAAa;AAAA,MACb,aAAa;AAAA,IACf;AAAA,EACF;AAEA,SAAO;AAAA,IACL,UAAU,CAAC,wBAAwB,CAAC;AAAA,IACpC;AAAA,IACA;AAAA,IACA,YAAY,iCAAiC,QAAS,KAAK,yBAAyB,aAAa,EAAE;AAAA;AAAA,IACnG;AAAA,IACA,kBAAkB,MAAM;AACtB,WAAK,qBAAqB;AAC1B,WAAK,qBAAqB;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,iCACP,yBACA,sBACA;AACA,SAAO,wBAAwB;AAAA,IAC7B,4BAA0B,uBAAuB,aAAa,OAAO;AAAA,EACvE;AACF;AAEA,SAAS,SACP,MACA,cACA,UACA,YACA;AACA,SAAO,CAAC,MAAM,aAAa,IAAI,qCAAU,IAAI,qCAAU,WAAW,WAAW,QAAQ,WAAW,KAAK,EAClG,OAAO,OAAO,EACd,KAAK,GAAG;AACb;;;ACpXA,IAAMC,8BAA6B;AAAA,EACjC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,MAAM;AAAA,EACN,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,eAAe;AAAA,EACf,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,SAAS;AACX;AAuCO,IAAM,sBAA2C,YAAU;AAChE,QAAM,EAAE,iBAAiB,iBAAiB,gBAAgB,IAAI,UAAU,CAAC;AAEzE,QAAM,4BAA4B,kBAAkB,iBAAiB;AAAA,IACnE,aAAa;AAAA,IACb,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,UAAU;AAAA,EACZ,CAAC;AAED,QAAM,4BAA4B,kBAAkB,iBAAiB;AAAA,IACnE,aAAa;AAAA,IACb,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,UAAU;AAAA,EACZ,CAAC;AAED,QAAM,4BAA4B,kBAAkB,iBAAiB;AAAA,IACnE,aAAa;AAAA,IACb,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,kBAAkB;AAAA,IAClB,UAAU;AAAA,EACZ,CAAC;AAED,QAAM,QAAQ,wBAAwB;AACtC,QAAM,OAAO,eAAe;AAE5B,QAAM,wBACJ,OAAO,oBAAoB,cACvB,SACA;AAAA,IACE,aAAa,0BAA0B;AAAA,IACvC,UAAU,0BAA0B;AAAA,EACtC;AAEN,QAAM,wBACJ,OAAO,oBAAoB,cACvB,SACA;AAAA,IACE,aAAa,0BAA0B;AAAA,IACvC,UAAU,0BAA0B;AAAA,IACpC,QAAQ,0BAA0B;AAAA,EACpC;AAEN,QAAM,wBACJ,OAAO,oBAAoB,cACvB,SACA;AAAA,IACE,aAAa,0BAA0B;AAAA,IACvC,UAAU,0BAA0B;AAAA,IACpC,QAAQ,0BAA0B;AAAA,EACpC;AAEN,QAAM,gBAAgB,CAAC,EAAE,MAAM,UAAU;AAEzC,QAAM,cAAc;AAAA,IAIlB;AAAA,MACE,GAAG;AAAA,MACH,WAAW;AAAA,IACb;AAAA,IACA,6BAAM;AAAA,IACN;AAAA,MACE,kBAAkB,0BAA0B;AAAA,MAC5C,UAAU,0BAA0B;AAAA,MACpC,SAAS,CAAC,CAAC;AAAA,IACb;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,6BAAM;AAAA,IAChB;AAAA,EACF;AAEA,QAAM,cAAc;AAAA,IAIlB;AAAA,MACE,GAAG;AAAA,IACL;AAAA,IACA,6BAAM;AAAA,IACN;AAAA,MACE,kBAAkB,0BAA0B;AAAA,MAC5C,UAAU,0BAA0B;AAAA,MACpC,SAAS,CAAC,CAAC;AAAA,IACb;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,6BAAM;AAAA,IAChB;AAAA,EACF;AAEA,QAAM,cAAc;AAAA,IAIlB;AAAA,MACE,GAAG;AAAA,IACL;AAAA,IACA,6BAAM;AAAA,IACN;AAAA,MACE,kBAAkB,0BAA0B;AAAA,MAC5C,UAAU,0BAA0B;AAAA,MACpC,SAAS,CAAC,CAAC;AAAA,IACb;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,QAAQ,6BAAM;AAAA,IAChB;AAAA,EACF;AAGA,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,iBAAiBA;AAAA,MACjB,iBAAiBA;AAAA,MACjB,iBAAiBA;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,SAAS;AAAA,IACb,UAAU;AAAA,IACV,kBAAkB,uBAAuB,KAAK,uBAAuB;AAAA,IACrE,WAAW,MAAM;AAAA,IACjB,oBAAoB,MAAM;AAAA,IAC1B,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EACnB;AACA,2BAAyB,QAAQ,oBAAoB,gCAAgC;AAErF,SAAO;AACT;AAEA,SAAS,uBAAuB,yBAA2D;AACzF,SAAO,wBAAwB,IAAI,6BAA2B;AAAA,IAC5D,YAAY;AAAA,IACZ,cAAc,uBAAuB;AAAA,EACvC,EAAE;AACJ;;;AC5LO,IAAM,mBAAqC,MAAM;AACtD,aAAW,oBAAoB,gEAAgE;AAC/F,QAAM,QAAQ,wBAAwB;AACtC,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,oBAAoB;AAAA,MACpB,4BAA4B;AAAA,MAC5B,iBAAiB;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AAAA,IACL,UAAU;AAAA,IACV,oBAAoB,MAAM;AAAA,IAC1B,4BAA4B,MAAM;AAAA,IAClC,iBAAiB,MAAM;AAAA,EACzB;AACF;;;ACvEA,IAAAC,gBAAkB;AAEX,IAAM,sBAAsB,OAAO,WAAW,cAAc,cAAAC,QAAM,kBAAkB,cAAAA,QAAM;", "names": ["React", "import_react", "React", "import_react", "_a", "_b", "undefinedPaginatedResource", "import_react", "React"]}