{"version": 3, "sources": ["../src/color.ts"], "sourcesContent": ["import type { Color, HslaColor, RgbaColor, TransparentColor } from '@clerk/types';\n\nconst IS_HEX_COLOR_REGEX = /^#?([A-F0-9]{6}|[A-F0-9]{3})$/i;\n\nconst IS_RGB_COLOR_REGEX = /^rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)$/i;\nconst IS_RGBA_COLOR_REGEX = /^rgba\\((\\d+),\\s*(\\d+),\\s*(\\d+)(,\\s*\\d+(\\.\\d+)?)\\)$/i;\n\nconst IS_HSL_COLOR_REGEX = /^hsl\\((\\d+),\\s*([\\d.]+)%,\\s*([\\d.]+)%\\)$/i;\nconst IS_HSLA_COLOR_REGEX = /^hsla\\((\\d+),\\s*([\\d.]+)%,\\s*([\\d.]+)%(,\\s*\\d+(\\.\\d+)?)*\\)$/i;\n\nexport const isValidHexString = (s: string) => {\n  return !!s.match(IS_HEX_COLOR_REGEX);\n};\n\nexport const isValidRgbaString = (s: string) => {\n  return !!(s.match(IS_RGB_COLOR_REGEX) || s.match(IS_RGBA_COLOR_REGEX));\n};\n\nexport const isValidHslaString = (s: string) => {\n  return !!s.match(IS_HSL_COLOR_REGEX) || !!s.match(IS_HSLA_COLOR_REGEX);\n};\n\nexport const isRGBColor = (c: Color): c is RgbaColor => {\n  return typeof c !== 'string' && 'r' in c;\n};\n\nexport const isHSLColor = (c: Color): c is HslaColor => {\n  return typeof c !== 'string' && 'h' in c;\n};\n\nexport const isTransparent = (c: Color): c is TransparentColor => {\n  return c === 'transparent';\n};\n\nexport const hasAlpha = (color: Color): boolean => {\n  return typeof color !== 'string' && color.a != undefined && color.a < 1;\n};\n\nconst CLEAN_HSLA_REGEX = /[hsla()]/g;\nconst CLEAN_RGBA_REGEX = /[rgba()]/g;\n\nexport const stringToHslaColor = (value: string): HslaColor | null => {\n  if (value === 'transparent') {\n    return { h: 0, s: 0, l: 0, a: 0 };\n  }\n\n  if (isValidHexString(value)) {\n    return hexStringToHslaColor(value);\n  }\n\n  if (isValidHslaString(value)) {\n    return parseHslaString(value);\n  }\n\n  if (isValidRgbaString(value)) {\n    return rgbaStringToHslaColor(value);\n  }\n\n  return null;\n};\n\nexport const stringToSameTypeColor = (value: string): Color => {\n  value = value.trim();\n  if (isValidHexString(value)) {\n    return value.startsWith('#') ? value : `#${value}`;\n  }\n\n  if (isValidRgbaString(value)) {\n    return parseRgbaString(value);\n  }\n\n  if (isValidHslaString(value)) {\n    return parseHslaString(value);\n  }\n\n  if (isTransparent(value)) {\n    return value;\n  }\n  return '';\n};\n\nexport const colorToSameTypeString = (color: Color): string | TransparentColor => {\n  if (typeof color === 'string' && (isValidHexString(color) || isTransparent(color))) {\n    return color;\n  }\n\n  if (isRGBColor(color)) {\n    return rgbaColorToRgbaString(color);\n  }\n\n  if (isHSLColor(color)) {\n    return hslaColorToHslaString(color);\n  }\n\n  return '';\n};\n\nexport const hexStringToRgbaColor = (hex: string): RgbaColor => {\n  hex = hex.replace('#', '');\n  const r = parseInt(hex.substring(0, 2), 16);\n  const g = parseInt(hex.substring(2, 4), 16);\n  const b = parseInt(hex.substring(4, 6), 16);\n  return { r, g, b };\n};\n\nconst rgbaColorToRgbaString = (color: RgbaColor): string => {\n  const { a, b, g, r } = color;\n  return color.a === 0 ? 'transparent' : color.a != undefined ? `rgba(${r},${g},${b},${a})` : `rgb(${r},${g},${b})`;\n};\n\nconst hslaColorToHslaString = (color: HslaColor): string => {\n  const { h, s, l, a } = color;\n  const sPerc = Math.round(s * 100);\n  const lPerc = Math.round(l * 100);\n  return color.a === 0\n    ? 'transparent'\n    : color.a != undefined\n    ? `hsla(${h},${sPerc}%,${lPerc}%,${a})`\n    : `hsl(${h},${sPerc}%,${lPerc}%)`;\n};\n\nconst hexStringToHslaColor = (hex: string): HslaColor => {\n  const rgbaString = colorToSameTypeString(hexStringToRgbaColor(hex));\n  return rgbaStringToHslaColor(rgbaString);\n};\n\nconst rgbaStringToHslaColor = (rgba: string): HslaColor => {\n  const rgbaColor = parseRgbaString(rgba);\n  const r = rgbaColor.r / 255;\n  const g = rgbaColor.g / 255;\n  const b = rgbaColor.b / 255;\n\n  const max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  let h, s;\n  const l = (max + min) / 2;\n\n  if (max == min) {\n    h = s = 0;\n  } else {\n    const d = max - min;\n    s = l >= 0.5 ? d / (2 - (max + min)) : d / (max + min);\n    switch (max) {\n      case r:\n        h = ((g - b) / d) * 60;\n        break;\n      case g:\n        h = ((b - r) / d + 2) * 60;\n        break;\n      default:\n        h = ((r - g) / d + 4) * 60;\n        break;\n    }\n  }\n\n  const res: HslaColor = { h: Math.round(h), s, l };\n  const a = rgbaColor.a;\n  if (a != undefined) {\n    res.a = a;\n  }\n  return res;\n};\n\nconst parseRgbaString = (str: string): RgbaColor => {\n  const [r, g, b, a] = str\n    .replace(CLEAN_RGBA_REGEX, '')\n    .split(',')\n    .map(c => Number.parseFloat(c));\n  return { r, g, b, a };\n};\n\nconst parseHslaString = (str: string): HslaColor => {\n  const [h, s, l, a] = str\n    .replace(CLEAN_HSLA_REGEX, '')\n    .split(',')\n    .map(c => Number.parseFloat(c));\n  return { h, s: s / 100, l: l / 100, a };\n};\n"], "mappings": ";AAEA,IAAM,qBAAqB;AAE3B,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAE5B,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAErB,IAAM,mBAAmB,CAAC,MAAc;AAC7C,SAAO,CAAC,CAAC,EAAE,MAAM,kBAAkB;AACrC;AAEO,IAAM,oBAAoB,CAAC,MAAc;AAC9C,SAAO,CAAC,EAAE,EAAE,MAAM,kBAAkB,KAAK,EAAE,MAAM,mBAAmB;AACtE;AAEO,IAAM,oBAAoB,CAAC,MAAc;AAC9C,SAAO,CAAC,CAAC,EAAE,MAAM,kBAAkB,KAAK,CAAC,CAAC,EAAE,MAAM,mBAAmB;AACvE;AAEO,IAAM,aAAa,CAAC,MAA6B;AACtD,SAAO,OAAO,MAAM,YAAY,OAAO;AACzC;AAEO,IAAM,aAAa,CAAC,MAA6B;AACtD,SAAO,OAAO,MAAM,YAAY,OAAO;AACzC;AAEO,IAAM,gBAAgB,CAAC,MAAoC;AAChE,SAAO,MAAM;AACf;AAEO,IAAM,WAAW,CAAC,UAA0B;AACjD,SAAO,OAAO,UAAU,YAAY,MAAM,KAAK,UAAa,MAAM,IAAI;AACxE;AAEA,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AAElB,IAAM,oBAAoB,CAAC,UAAoC;AACpE,MAAI,UAAU,eAAe;AAC3B,WAAO,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAAA,EAClC;AAEA,MAAI,iBAAiB,KAAK,GAAG;AAC3B,WAAO,qBAAqB,KAAK;AAAA,EACnC;AAEA,MAAI,kBAAkB,KAAK,GAAG;AAC5B,WAAO,gBAAgB,KAAK;AAAA,EAC9B;AAEA,MAAI,kBAAkB,KAAK,GAAG;AAC5B,WAAO,sBAAsB,KAAK;AAAA,EACpC;AAEA,SAAO;AACT;AAEO,IAAM,wBAAwB,CAAC,UAAyB;AAC7D,UAAQ,MAAM,KAAK;AACnB,MAAI,iBAAiB,KAAK,GAAG;AAC3B,WAAO,MAAM,WAAW,GAAG,IAAI,QAAQ,IAAI,KAAK;AAAA,EAClD;AAEA,MAAI,kBAAkB,KAAK,GAAG;AAC5B,WAAO,gBAAgB,KAAK;AAAA,EAC9B;AAEA,MAAI,kBAAkB,KAAK,GAAG;AAC5B,WAAO,gBAAgB,KAAK;AAAA,EAC9B;AAEA,MAAI,cAAc,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEO,IAAM,wBAAwB,CAAC,UAA4C;AAChF,MAAI,OAAO,UAAU,aAAa,iBAAiB,KAAK,KAAK,cAAc,KAAK,IAAI;AAClF,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,KAAK,GAAG;AACrB,WAAO,sBAAsB,KAAK;AAAA,EACpC;AAEA,MAAI,WAAW,KAAK,GAAG;AACrB,WAAO,sBAAsB,KAAK;AAAA,EACpC;AAEA,SAAO;AACT;AAEO,IAAM,uBAAuB,CAAC,QAA2B;AAC9D,QAAM,IAAI,QAAQ,KAAK,EAAE;AACzB,QAAM,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AAC1C,QAAM,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AAC1C,QAAM,IAAI,SAAS,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE;AAC1C,SAAO,EAAE,GAAG,GAAG,EAAE;AACnB;AAEA,IAAM,wBAAwB,CAAC,UAA6B;AAC1D,QAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI;AACvB,SAAO,MAAM,MAAM,IAAI,gBAAgB,MAAM,KAAK,SAAY,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAChH;AAEA,IAAM,wBAAwB,CAAC,UAA6B;AAC1D,QAAM,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI;AACvB,QAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAChC,QAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAChC,SAAO,MAAM,MAAM,IACf,gBACA,MAAM,KAAK,SACX,QAAQ,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,MAClC,OAAO,CAAC,IAAI,KAAK,KAAK,KAAK;AACjC;AAEA,IAAM,uBAAuB,CAAC,QAA2B;AACvD,QAAM,aAAa,sBAAsB,qBAAqB,GAAG,CAAC;AAClE,SAAO,sBAAsB,UAAU;AACzC;AAEA,IAAM,wBAAwB,CAAC,SAA4B;AACzD,QAAM,YAAY,gBAAgB,IAAI;AACtC,QAAM,IAAI,UAAU,IAAI;AACxB,QAAM,IAAI,UAAU,IAAI;AACxB,QAAM,IAAI,UAAU,IAAI;AAExB,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GAC1B,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AACxB,MAAI,GAAG;AACP,QAAM,KAAK,MAAM,OAAO;AAExB,MAAI,OAAO,KAAK;AACd,QAAI,IAAI;AAAA,EACV,OAAO;AACL,UAAM,IAAI,MAAM;AAChB,QAAI,KAAK,MAAM,KAAK,KAAK,MAAM,QAAQ,KAAK,MAAM;AAClD,YAAQ,KAAK;AAAA,MACX,KAAK;AACH,aAAM,IAAI,KAAK,IAAK;AACpB;AAAA,MACF,KAAK;AACH,cAAM,IAAI,KAAK,IAAI,KAAK;AACxB;AAAA,MACF;AACE,cAAM,IAAI,KAAK,IAAI,KAAK;AACxB;AAAA,IACJ;AAAA,EACF;AAEA,QAAM,MAAiB,EAAE,GAAG,KAAK,MAAM,CAAC,GAAG,GAAG,EAAE;AAChD,QAAM,IAAI,UAAU;AACpB,MAAI,KAAK,QAAW;AAClB,QAAI,IAAI;AAAA,EACV;AACA,SAAO;AACT;AAEA,IAAM,kBAAkB,CAAC,QAA2B;AAClD,QAAM,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,IAClB,QAAQ,kBAAkB,EAAE,EAC5B,MAAM,GAAG,EACT,IAAI,OAAK,OAAO,WAAW,CAAC,CAAC;AAChC,SAAO,EAAE,GAAG,GAAG,GAAG,EAAE;AACtB;AAEA,IAAM,kBAAkB,CAAC,QAA2B;AAClD,QAAM,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,IAClB,QAAQ,kBAAkB,EAAE,EAC5B,MAAM,GAAG,EACT,IAAI,OAAK,OAAO,WAAW,CAAC,CAAC;AAChC,SAAO,EAAE,GAAG,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;AACxC;", "names": []}