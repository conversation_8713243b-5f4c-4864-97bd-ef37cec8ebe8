"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/url.ts
var url_exports = {};
__export(url_exports, {
  addClerkPrefix: () => addClerkPrefix,
  getClerkJsMajorVersionOrTag: () => getClerkJsMajorVersionOrTag,
  getScriptUrl: () => getScriptUrl,
  parseSearchParams: () => parseSearchParams,
  stripScheme: () => stripScheme
});
module.exports = __toCommonJS(url_exports);

// src/utils/instance.ts
function isStaging(frontendApi) {
  return frontendApi.endsWith(".lclstage.dev") || frontendApi.endsWith(".stgstage.dev") || frontendApi.endsWith(".clerkstage.dev") || frontendApi.endsWith(".accountsstage.dev");
}

// src/url.ts
function parseSearchParams(queryString = "") {
  if (queryString.startsWith("?")) {
    queryString = queryString.slice(1);
  }
  return new URLSearchParams(queryString);
}
function stripScheme(url = "") {
  return (url || "").replace(/^.+:\/\//, "");
}
function addClerkPrefix(str) {
  if (!str) {
    return "";
  }
  let regex;
  if (str.match(/^(clerk\.)+\w*$/)) {
    regex = /(clerk\.)*(?=clerk\.)/;
  } else if (str.match(/\.clerk.accounts/)) {
    return str;
  } else {
    regex = /^(clerk\.)*/gi;
  }
  const stripped = str.replace(regex, "");
  return `clerk.${stripped}`;
}
var getClerkJsMajorVersionOrTag = (frontendApi, pkgVersion) => {
  if (!pkgVersion && isStaging(frontendApi)) {
    return "canary";
  }
  if (!pkgVersion) {
    return "latest";
  }
  return pkgVersion.split(".")[0] || "latest";
};
var getScriptUrl = (frontendApi, { pkgVersion = "4.73.9", clerkJSVersion }) => {
  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\/\//, "");
  const major = getClerkJsMajorVersionOrTag(frontendApi, pkgVersion);
  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  addClerkPrefix,
  getClerkJsMajorVersionOrTag,
  getScriptUrl,
  parseSearchParams,
  stripScheme
});
//# sourceMappingURL=url.js.map