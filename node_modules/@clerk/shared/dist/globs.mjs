import "./chunk-NDCDZYN6.mjs";

// src/globs.ts
import globToRegexp from "glob-to-regexp";
var globs = {
  toRegexp: (pattern) => {
    try {
      return globToRegexp(pattern);
    } catch (e) {
      throw new Error(
        `Invalid pattern: ${pattern}.
Consult the documentation of glob-to-regexp here: https://www.npmjs.com/package/glob-to-regexp.
${e.message}`
      );
    }
  }
};
export {
  globs
};
//# sourceMappingURL=globs.mjs.map