export { inBrowser, isBrowserOnline, isValidBrowser, isValidBrowserOnline, userAgentIsRobot } from './browser.mjs';
export { callWithRetry } from './callWithRetry.mjs';
export { colorToSameTypeString, hasAlpha, hexStringToRgbaColor, isHSLColor, isRGBColor, isTransparent, isValidHexString, isValidHslaString, isValidRgbaString, stringToHslaColor, stringToSameTypeColor } from './color.mjs';
export { RelativeDateCase, addYears, dateTo12HourTime, differenceInCalendarDays, formatRelative, normalizeDate } from './date.mjs';
export { deprecated, deprecatedObjectProperty, deprecatedProperty } from './deprecated.mjs';
export { DEV_BROWSER_JWT_MARKER, DEV_BROWSER_SSO_JWT_PARAMETER, extractDevBrowserJWTFromURLHash, extractDevBrowserJWTFromURLSearchParams, setDevBrowserJWTInURL } from './devBrowser.mjs';
export { ClerkAPIResponseError, ClerkRuntimeError, EmailLinkError, EmailLinkErrorCode, ErrorThrower, ErrorThrowerOptions, MagicLinkError, MagicLinkErrorCode, MetamaskError, buildErrorThrower, is4xxError, isCaptchaError, isClerkAPIResponseError, isClerkRuntimeError, isEmailLinkError, isKnownError, isMagicLinkError, isMetamaskError, isNetworkError, isPasswordPwnedError, isUnauthorizedError, isUserLockedError, parseError, parseErrors } from './error.mjs';
export { SupportedMimeType, extension, readJSONFile } from './file.mjs';
export { handleValueOrFn } from './handleValueOrFn.mjs';
export { isomorphicAtob } from './isomorphicAtob.mjs';
export { buildPublishableKey, createDevOrStagingUrlCache, isDevelopmentFromApiKey, isLegacyFrontendApiKey, isProductionFromApiKey, isPublishableKey, parsePublishableKey } from './keys.mjs';
export { loadScript } from './loadScript.mjs';
export { LocalStorageBroadcastChannel } from './localStorageBroadcastChannel.mjs';
export { Poller, PollerCallback, PollerRun, PollerStop } from './poller.mjs';
export { getRequestUrl, isHttpOrHttps, isProxyUrlRelative, isValidProxyUrl, proxyUrlToAbsoluteURL } from './proxy.mjs';
export { camelToSnake, deepCamelToSnake, deepSnakeToCamel, isIPV4Address, snakeToCamel, titleize, toSentence } from './underscore.mjs';
export { addClerkPrefix, getClerkJsMajorVersionOrTag, getScriptUrl, parseSearchParams, stripScheme } from './url.mjs';
import '@clerk/types';

type Callback = (val?: any) => void;
/**
 * Create a promise that can be resolved or rejected from
 * outside the Promise constructor callback
 */
declare const createDeferredPromise: () => {
    promise: Promise<unknown>;
    resolve: Callback;
    reject: Callback;
};

/**
 * Check if the frontendApi ends with a staging domain
 */
declare function isStaging(frontendApi: string): boolean;

declare const noop: (..._args: any[]) => void;

declare const isDevelopmentEnvironment: () => boolean;
declare const isTestEnvironment: () => boolean;
declare const isProductionEnvironment: () => boolean;

type Milliseconds = number;
type BackoffOptions = Partial<{
    firstDelay: Milliseconds;
    maxDelay: Milliseconds;
    timeMultiple: number;
    shouldRetry: (error: unknown, iterationsCount: number) => boolean;
}>;
declare const runWithExponentialBackOff: <T>(callback: () => T | Promise<T>, options?: BackoffOptions) => Promise<T>;

declare const logErrorInDevMode: (message: string) => void;

type WorkerTimerId = number;
type WorkerTimeoutCallback = () => void;
type WorkerSetTimeout = (cb: WorkerTimeoutCallback, ms: number) => WorkerTimerId;
type WorkerClearTimeout = (id: WorkerTimerId) => void;

declare const createWorkerTimers: () => {
    setTimeout: WorkerSetTimeout;
    setInterval: WorkerSetTimeout;
    clearTimeout: WorkerClearTimeout;
    clearInterval: WorkerClearTimeout;
    cleanup: (..._args: any[]) => void;
};

export { createDeferredPromise, createWorkerTimers, isDevelopmentEnvironment, isProductionEnvironment, isStaging, isTestEnvironment, logErrorInDevMode, noop, runWithExponentialBackOff };
