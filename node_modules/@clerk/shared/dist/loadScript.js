"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/loadScript.ts
var loadScript_exports = {};
__export(loadScript_exports, {
  loadScript: () => loadScript
});
module.exports = __toCommonJS(loadScript_exports);
var NO_DOCUMENT_ERROR = "loadScript cannot be called when document does not exist";
var NO_SRC_ERROR = "loadScript cannot be called without a src";
async function loadScript(src = "", opts) {
  const { async, defer, beforeLoad, crossOrigin } = opts || {};
  return new Promise((resolve, reject) => {
    if (!src) {
      reject(NO_SRC_ERROR);
    }
    if (!document || !document.body) {
      reject(NO_DOCUMENT_ERROR);
    }
    const script = document.createElement("script");
    crossOrigin && script.setAttribute("crossorigin", crossOrigin);
    script.async = async || false;
    script.defer = defer || false;
    script.addEventListener("load", () => {
      script.remove();
      resolve(script);
    });
    script.addEventListener("error", () => {
      script.remove();
      reject();
    });
    script.src = src;
    beforeLoad == null ? void 0 : beforeLoad(script);
    document.body.appendChild(script);
  });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  loadScript
});
//# sourceMappingURL=loadScript.js.map