import runtime from '../runtime';
type IsomorphicRequestOptions = (Request: typeof runtime.Request, Headers: typeof runtime.Headers) => Request;
export declare const createIsomorphicRequest: (cb: IsomorphicRequestOptions) => Request;
export declare const buildRequest: (req?: Request) => {
    cookies?: undefined;
    headers?: undefined;
    searchParams?: undefined;
} | {
    cookies: (key: string) => string | undefined;
    headers: (key: string) => string | undefined;
    searchParams: URLSearchParams | undefined;
};
export declare const stripAuthorizationHeader: (authValue: string | undefined | null) => string | undefined;
export {};
//# sourceMappingURL=IsomorphicRequest.d.ts.map