import type { InvitationStatus } from './Enums';
import type { InvitationJSON } from './JSON';
export declare class Invitation {
    readonly id: string;
    readonly emailAddress: string;
    readonly publicMetadata: Record<string, unknown> | null;
    readonly createdAt: number;
    readonly updatedAt: number;
    readonly status: InvitationStatus;
    readonly revoked?: boolean | undefined;
    constructor(id: string, emailAddress: string, publicMetadata: Record<string, unknown> | null, createdAt: number, updatedAt: number, status: InvitationStatus, revoked?: boolean | undefined);
    static fromJSON(data: InvitationJSON): Invitation;
}
//# sourceMappingURL=Invitation.d.ts.map