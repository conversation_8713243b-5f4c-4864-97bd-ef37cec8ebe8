import { Organization } from '../resources';
import type { OrganizationMembershipRole } from './Enums';
import type { OrganizationMembershipJSON, OrganizationMembershipPublicUserDataJSON } from './JSON';
export declare class OrganizationMembership {
    readonly id: string;
    readonly role: OrganizationMembershipRole;
    readonly publicMetadata: OrganizationMembershipPublicMetadata;
    readonly privateMetadata: OrganizationMembershipPrivateMetadata;
    readonly createdAt: number;
    readonly updatedAt: number;
    readonly organization: Organization;
    readonly publicUserData?: OrganizationMembershipPublicUserData | null | undefined;
    constructor(id: string, role: OrganizationMembershipRole, publicMetadata: OrganizationMembershipPublicMetadata, privateMetadata: OrganizationMembershipPrivateMetadata, createdAt: number, updatedAt: number, organization: Organization, publicUserData?: OrganizationMembershipPublicUserData | null | undefined);
    static fromJSON(data: OrganizationMembershipJSON): OrganizationMembership;
}
export declare class OrganizationMembershipPublicUserData {
    readonly identifier: string;
    readonly firstName: string | null;
    readonly lastName: string | null;
    /**
     * @deprecated  Use `imageUrl` instead.
     */
    readonly profileImageUrl: string;
    readonly imageUrl: string;
    readonly hasImage: boolean;
    readonly userId: string;
    constructor(identifier: string, firstName: string | null, lastName: string | null, 
    /**
     * @deprecated  Use `imageUrl` instead.
     */
    profileImageUrl: string, imageUrl: string, hasImage: boolean, userId: string);
    static fromJSON(data: OrganizationMembershipPublicUserDataJSON): OrganizationMembershipPublicUserData;
}
//# sourceMappingURL=OrganizationMembership.d.ts.map