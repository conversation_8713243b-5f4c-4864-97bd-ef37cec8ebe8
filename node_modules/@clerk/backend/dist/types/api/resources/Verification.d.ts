import type { VerificationJSON } from './JSON';
export declare class Verification {
    readonly status: string;
    readonly strategy: string;
    readonly externalVerificationRedirectURL: URL | null;
    readonly attempts: number | null;
    readonly expireAt: number | null;
    readonly nonce: string | null;
    constructor(status: string, strategy: string, externalVerificationRedirectURL?: URL | null, attempts?: number | null, expireAt?: number | null, nonce?: string | null);
    static fromJSON(data: VerificationJSON): Verification;
}
//# sourceMappingURL=Verification.d.ts.map