type RedirectAdapter = (url: string) => any;
type SignUpParams = {
    returnBackUrl?: string;
};
type SignInParams = {
    returnBackUrl?: string;
};
type RedirectParams = {
    redirectAdapter: RedirectAdapter;
    signInUrl?: string;
    signUpUrl?: string;
    publishableKey?: string;
    /**
     * @deprecated Use `publishableKey` instead.
     */
    frontendApi?: string;
};
export declare function redirect({ redirectAdapter, signUpUrl, signInUrl, frontendApi, publishableKey }: RedirectParams): {
    redirectToSignUp: ({ returnBackUrl }?: SignUpParams) => any;
    redirectToSignIn: ({ returnBackUrl }?: SignInParams) => any;
};
export {};
//# sourceMappingURL=redirections.d.ts.map