{"version": 3, "file": "session.d.ts", "sourceRoot": "", "sources": ["../src/session.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AACzC,OAAO,KAAK,EACV,cAAc,EACd,+BAA+B,EAC/B,yBAAyB,EACzB,yBAAyB,EAC1B,MAAM,0BAA0B,CAAC;AAClC,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAChD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAC7C,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAE3C,MAAM,MAAM,oBAAoB,CAAC,MAAM,IAAI,CAAC,kBAAkB,EAAE,MAAM,KAAK,OAAO,CAAC;AAEnF,MAAM,MAAM,uCAAuC,GACjD,oBAAoB,CAAC,6CAA6C,CAAC,CAAC;AAEtE,MAAM,MAAM,6CAA6C,GACrD;IACE,IAAI,EAAE,yBAAyB,CAAC;IAChC,UAAU,CAAC,EAAE,KAAK,CAAC;CACpB,GACD;IACE,IAAI,CAAC,EAAE,KAAK,CAAC;IACb,UAAU,EAAE,+BAA+B,CAAC;CAC7C,CAAC;AAEN,MAAM,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;AAEhF,KAAK,wBAAwB,GACzB;IACE,IAAI,EAAE,cAAc,CAAC;IACrB,UAAU,CAAC,EAAE,KAAK,CAAC;CACpB,GACD;IACE,IAAI,CAAC,EAAE,KAAK,CAAC;IACb,UAAU,EAAE,yBAAyB,CAAC;CACvC,CAAC;AAEN,MAAM,WAAW,eAAgB,SAAQ,aAAa;IACpD,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,aAAa,CAAC;IACtB,QAAQ,EAAE,IAAI,CAAC;IACf,SAAS,EAAE,IAAI,CAAC;IAChB,eAAe,EAAE,aAAa,GAAG,IAAI,CAAC;IACtC,wBAAwB,EAAE,MAAM,GAAG,IAAI,CAAC;IACxC,YAAY,EAAE,IAAI,CAAC;IACnB,KAAK,EAAE,WAAW,GAAG,IAAI,CAAC;IAC1B,IAAI,EAAE,YAAY,GAAG,IAAI,CAAC;IAC1B,cAAc,EAAE,cAAc,CAAC;IAC/B,GAAG,EAAE,MAAM,OAAO,CAAC,eAAe,CAAC,CAAC;IACpC,MAAM,EAAE,MAAM,OAAO,CAAC,eAAe,CAAC,CAAC;IACvC,KAAK,EAAE,MAAM,OAAO,CAAC,eAAe,CAAC,CAAC;IACtC,QAAQ,EAAE,QAAQ,CAAC;IACnB,kBAAkB,EAAE,kBAAkB,CAAC;IACvC,UAAU,EAAE,MAAM,IAAI,CAAC;IACvB,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,MAAM,WAAW,qBAAsB,SAAQ,eAAe;IAC5D,MAAM,EAAE,QAAQ,CAAC;IACjB,IAAI,EAAE,YAAY,CAAC;CACpB;AAED,MAAM,WAAW,6BAA8B,SAAQ,aAAa;IAClE,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,IAAI,CAAC;IACf,SAAS,EAAE,IAAI,CAAC;IAChB,YAAY,EAAE,IAAI,CAAC;IACnB,cAAc,EAAE,eAAe,CAAC;IAChC,KAAK,EAAE,WAAW,GAAG,IAAI,CAAC;IAE1B,MAAM,EAAE,MAAM,OAAO,CAAC,6BAA6B,CAAC,CAAC;CACtD;AAED,MAAM,WAAW,eAAe;IAC9B,EAAE,EAAE,MAAM,CAAC;IACX,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,OAAO,CAAC;CACpB;AAED,MAAM,MAAM,aAAa,GAAG,WAAW,GAAG,QAAQ,GAAG,OAAO,GAAG,SAAS,GAAG,SAAS,GAAG,UAAU,GAAG,SAAS,CAAC;AAE9G,MAAM,WAAW,cAAc;IAC7B,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IACzB,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB;;OAEG;IACH,eAAe,EAAE,MAAM,CAAC;IACxB,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,OAAO,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,MAAM,eAAe,GAAG;IAAE,QAAQ,CAAC,EAAE,MAAM,CAAC;IAAC,eAAe,CAAC,EAAE,MAAM,CAAC;IAAC,SAAS,CAAC,EAAE,OAAO,CAAA;CAAE,CAAC;AACnG,MAAM,MAAM,QAAQ,GAAG,CAAC,OAAO,CAAC,EAAE,eAAe,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC"}