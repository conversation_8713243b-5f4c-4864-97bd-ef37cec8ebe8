# Installation
> `npm install --save @types/cookies`

# Summary
This package contains type definitions for cookies (https://github.com/pillarjs/cookies).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookies.

### Additional Details
 * Last updated: Tue, 06 Jul 2021 20:32:31 GMT
 * Dependencies: [@types/keygrip](https://npmjs.com/package/@types/keygrip), [@types/express](https://npmjs.com/package/@types/express), [@types/connect](https://npmjs.com/package/@types/connect), [@types/node](https://npmjs.com/package/@types/node)
 * Global values: none

# Credits
These definitions were written by [<PERSON>](https://github.com/<PERSON>), [j<PERSON><PERSON>](https://github.com/jkeylu), and [BendingBender](https://github.com/BendingBender).
