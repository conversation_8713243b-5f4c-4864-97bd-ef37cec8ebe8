# Multi-Agent AI Platform

A sophisticated multi-agent AI platform that provides specialized AI assistance across multiple channels (Discord, Telegram, and Web). The system features task-based model routing, cross-platform user management, and comprehensive data storage capabilities.

## 🚀 Features

- **5 Specialized AI Agents**: Chat, Coding, Finance, Math, and Web Search
- **Multi-Platform Support**: Discord bot, Telegram bot, and web interface integration
- **Smart Model Routing**: Automatic model selection based on task type
- **Cross-Platform Data**: Unified user profiles and conversation history
- **OAuth Integration**: Google and GitHub services integration (NextAuth ready)
- **Advanced Data Management**: User memory, instructions, and conversation history
- **PostgreSQL/Prisma Ready**: Prepared for external NextAuth website integration

## 🏗️ Architecture

The platform uses a modular architecture with Python-based agents and external authentication:

- **Agent System**: Specialized AI agents for different tasks
- **Model Core**: OpenRouter integration with multiple AI models
- **Database Layer**: MongoDB with PostgreSQL/Prisma preparation
- **Input Systems**: Discord and Telegram bot integrations
- **External Auth**: NextAuth website integration for OAuth management

## 🤖 Supported AI Models

- Google Gemini 2.5 Pro
- DeepSeek R1
- OpenAI GPT-4.1
- X.AI Grok-3
- Anthropic Claude Sonnet 4

## 📋 Table of Contents

- [Project Structure](#project-structure)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [API Reference](#api-reference)
- [Development](#development)
- [Contributing](#contributing)
