"""
User Store for multi-agent AI platform.

This module provides a UserStore class that interfaces with MongoDB to store and retrieve
user-specific data, including profiles, preferences, and agent-specific information.
"""

import logging
import os
import sys
import time
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

# Add project root to sys.path to allow running this script directly
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import ConnectionFailure, PyMongoError

from agent.utils.helpers import setup_logger


class UserStore:
    """
    User Store for managing persistent user data in MongoDB.
    
    This class provides functionality to:
    - Store and retrieve user profiles and metadata
    - Maintain persistent user preferences across sessions
    - Store agent-specific data (e.g., finance watchlists)
    - Serve as an interface between the application and MongoDB
    
    Each user has their own document in the database, with separate sections for
    different types of data (profile, preferences, agent_data).
    """
    
    def __init__(self, connection_string: str = "mongodb://localhost:27017/", 
                 database_name: str = "agent_platform", debug: bool = False):
        """
        Initialize the UserStore with MongoDB connection.
        
        Args:
            connection_string: MongoDB connection string.
            database_name: Name of the database to use.
            debug: If True, enables debug logging.
        """
        self.debug = debug
        self.connection_string = connection_string
        self.database_name = database_name
        
        # Set up logger
        self.logger = setup_logger(__name__, logging.DEBUG if debug else logging.INFO)
        
        # Initialize MongoDB connection
        self.client = None
        self.db = None
        self.users_collection = None
        
        try:
            self.client = MongoClient(connection_string)
            # Test the connection
            self.client.admin.command('ping')
            self.db = self.client[database_name]
            self.users_collection = self.db.users
            
            # Create indexes for faster lookups
            self.users_collection.create_index("user_id", unique=True)
            self.users_collection.create_index("integrations.telegram_id", sparse=True, unique=True)
            self.users_collection.create_index("linking.token", sparse=True)
            
            if debug:
                self.logger.debug(f"Connected to MongoDB: {database_name}")
        except ConnectionFailure:
            self.logger.error(f"Failed to connect to MongoDB at {connection_string}")
        except Exception as e:
            self.logger.error(f"Error initializing UserStore: {str(e)}")
    
    def _ensure_connection(self) -> bool:
        """
        Ensure that the MongoDB connection is active.
        
        Returns:
            True if connection is active, False otherwise.
        """
        if self.client is None:
            try:
                self.client = MongoClient(self.connection_string)
                self.db = self.client[self.database_name]
                self.users_collection = self.db.users
                
                if self.debug:
                    self.logger.debug("Reconnected to MongoDB")
                return True
            except Exception as e:
                self.logger.error(f"Failed to reconnect to MongoDB: {str(e)}")
                return False
        return True
    
    # User profile methods
    
    def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a user's complete document from the database.
        
        Args:
            user_id: Unique identifier for the user.
            
        Returns:
            The user document, or None if not found.
        """
        if not self._ensure_connection():
            return None
            
        try:
            user = self.users_collection.find_one({"user_id": user_id})
            
            if self.debug and user:
                self.logger.debug(f"Retrieved user document for user {user_id}")
            elif self.debug:
                self.logger.debug(f"No user document found for user {user_id}")
                
            return user
        except PyMongoError as e:
            self.logger.error(f"Error retrieving user {user_id}: {str(e)}")
            return None
    
    def user_exists(self, user_id: str) -> bool:
        """
        Check if a user exists in the database.
        
        Args:
            user_id: Unique identifier for the user.
            
        Returns:
            True if the user exists, False otherwise.
        """
        return self.get_user(user_id) is not None
    
    def create_user(self, user_id: str, profile: Dict[str, Any] = None) -> bool:
        """
        Create a new user in the database.
        
        Args:
            user_id: Unique identifier for the user.
            profile: Optional initial profile data.
            
        Returns:
            True if successful, False otherwise.
        """
        if not self._ensure_connection():
            return False
            
        if self.user_exists(user_id):
            if self.debug:
                self.logger.debug(f"User {user_id} already exists")
            return False
            
        try:
            user_doc = {
                "user_id": user_id,
                "profile": profile or {},
                "preferences": {},
                "agent_data": {},
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
            
            result = self.users_collection.insert_one(user_doc)
            
            if self.debug:
                self.logger.debug(f"Created new user {user_id}")
                
            return result.acknowledged
        except PyMongoError as e:
            self.logger.error(f"Error creating user {user_id}: {str(e)}")
            return False
    
    def update_profile(self, user_id: str, profile_data: Dict[str, Any]) -> bool:
        """
        Update a user's profile information.
        
        Args:
            user_id: Unique identifier for the user.
            profile_data: Profile data to update.
            
        Returns:
            True if successful, False otherwise.
        """
        if not self._ensure_connection():
            return False
            
        try:
            result = self.users_collection.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        "profile": profile_data,
                        "updated_at": datetime.now()
                    }
                },
                upsert=True
            )
            
            if self.debug:
                self.logger.debug(f"Updated profile for user {user_id}")
                
            return result.acknowledged
        except PyMongoError as e:
            self.logger.error(f"Error updating profile for user {user_id}: {str(e)}")
            return False
    
    def get_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a user's profile information.
        
        Args:
            user_id: Unique identifier for the user.
            
        Returns:
            The user's profile data, or None if not found.
        """
        user = self.get_user(user_id)
        if user:
            return user.get("profile", {})
        return None
    
    # --- Cross-Platform Account Linking ---

    def get_central_user_id_from_telegram_id(self, telegram_id: str) -> Optional[str]:
        """
        Finds the central user ID associated with a given Telegram ID.

        Args:
            telegram_id: The user's unique ID from Telegram.

        Returns:
            The central user_id string if found, otherwise None.
        """
        return self.get_central_user_id_from_platform_id('telegram', telegram_id)

    def get_central_user_id_from_platform_id(self, platform: str, platform_id: str) -> Optional[str]:
        """
        Finds the central user ID associated with a given platform ID (e.g., telegram_id, discord_id).

        Args:
            platform: The name of the platform (e.g., 'telegram', 'discord').
            platform_id: The user's unique ID from the specified platform.

        Returns:
            The central user_id string if found, otherwise None.
        """
        if not self._ensure_connection():
            return None
        
        query_field = f"integrations.{platform}_id"
        try:
            user_doc = self.users_collection.find_one({query_field: platform_id})
            if user_doc:
                user_id = user_doc.get("user_id")
                if self.debug:
                    self.logger.debug(f"Found central user_id '{user_id}' for {platform}_id '{platform_id}'")
                return user_id
            else:
                if self.debug:
                    self.logger.debug(f"No central user found for {platform}_id '{platform_id}'")
                return None
        except PyMongoError as e:
            self.logger.error(f"Error finding user by {platform}_id '{platform_id}': {e}")
            return None

    def link_account_by_token(self, platform: str, platform_id: str, token: str) -> bool:
        """
        Links a platform account (e.g., Telegram, Discord) to a central user account using a one-time token.

        This function finds a user by a temporary linking token, associates the
        platform_id with their account, and then deletes the token to prevent reuse.

        Args:
            platform: The platform being linked (e.g., 'telegram', 'discord').
            platform_id: The user's unique ID from the platform.
            token: The one-time linking token provided by the user.

        Returns:
            True if the account was linked successfully, False otherwise.
        """
        if not self._ensure_connection():
            return False
        
        try:
            # Find the user with the given linking token
            user_doc = self.users_collection.find_one({"linking.token": token})

            if not user_doc:
                self.logger.warning(f"Invalid linking token received: {token}")
                return False

            # Optional: Check if the token is expired
            if 'expires_at' in user_doc.get('linking', {}) and user_doc['linking']['expires_at'] < datetime.utcnow():
                self.logger.warning(f"Expired linking token used for user {user_doc['user_id']}")
                # Clean up the expired token
                self.users_collection.update_one({"_id": user_doc["_id"]}, {"$unset": {"linking": ""}})
                return False

            # Link the account and remove the token
            update_field = f"integrations.{platform}_id"
            result = self.users_collection.update_one(
                {"_id": user_doc["_id"]},
                {
                    "$set": {
                        update_field: platform_id,
                        "updated_at": datetime.now()
                    },
                    "$unset": {"linking": ""}
                }
            )

            if result.modified_count > 0:
                self.logger.info(f"Successfully linked {platform}_id '{platform_id}' to user '{user_doc['user_id']}'")
                return True
            else:
                self.logger.error(f"Failed to update document for user '{user_doc['user_id']}' during linking.")
                return False

        except PyMongoError as e:
            self.logger.error(f"Database error during account linking: {e}")
            return False

    # User preferences methods
    
    def set_preference(self, user_id: str, key: str, value: Any) -> bool:
        """
        Set a preference for a user.
        
        Args:
            user_id: Unique identifier for the user.
            key: Preference key.
            value: Preference value.
            
        Returns:
            True if successful, False otherwise.
        """
        if not self._ensure_connection():
            return False
            
        try:
            update_field = f"preferences.{key}"
            result = self.users_collection.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        update_field: value,
                        "updated_at": datetime.now()
                    }
                },
                upsert=True
            )
            
            if self.debug:
                self.logger.debug(f"Set preference '{key}' for user {user_id}")
                
            return result.acknowledged
        except PyMongoError as e:
            self.logger.error(f"Error setting preference '{key}' for user {user_id}: {str(e)}")
            return False
    
    def get_preference(self, user_id: str, key: str, default: Any = None) -> Any:
        """
        Get a preference for a user.
        
        Args:
            user_id: Unique identifier for the user.
            key: Preference key.
            default: Default value to return if preference is not set.
            
        Returns:
            The preference value, or the default if not found.
        """
        user = self.get_user(user_id)
        if user and "preferences" in user:
            return user["preferences"].get(key, default)
        return default
    
    def get_all_preferences(self, user_id: str) -> Dict[str, Any]:
        """
        Get all preferences for a user.
        
        Args:
            user_id: Unique identifier for the user.
            
        Returns:
            Dictionary of all preferences.
        """
        user = self.get_user(user_id)
        if user:
            return user.get("preferences", {})
        return {}
    
    def clear_preferences(self, user_id: str) -> bool:
        """
        Clear all preferences for a user.
        
        Args:
            user_id: Unique identifier for the user.
            
        Returns:
            True if successful, False otherwise.
        """
        if not self._ensure_connection():
            return False
            
        try:
            result = self.users_collection.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        "preferences": {},
                        "updated_at": datetime.now()
                    }
                }
            )
            
            if self.debug:
                self.logger.debug(f"Cleared all preferences for user {user_id}")
                
            return result.acknowledged
        except PyMongoError as e:
            self.logger.error(f"Error clearing preferences for user {user_id}: {str(e)}")
            return False
    
    # Agent-specific data methods
    
    def set_agent_data(self, user_id: str, agent_id: str, key: str, value: Any) -> bool:
        """
        Set agent-specific data for a user.
        
        Args:
            user_id: Unique identifier for the user.
            agent_id: Identifier for the agent (e.g., 'finance', 'chat').
            key: Data key.
            value: Data value.
            
        Returns:
            True if successful, False otherwise.
        """
        if not self._ensure_connection():
            return False
            
        try:
            update_field = f"agent_data.{agent_id}.{key}"
            result = self.users_collection.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        update_field: value,
                        "updated_at": datetime.now()
                    }
                },
                upsert=True
            )
            
            if self.debug:
                self.logger.debug(f"Set agent data '{key}' for agent {agent_id} for user {user_id}")
                
            return result.acknowledged
        except PyMongoError as e:
            self.logger.error(f"Error setting agent data '{key}' for agent {agent_id} for user {user_id}: {str(e)}")
            return False
    
    def get_agent_data(self, user_id: str, agent_id: str, key: str, default: Any = None) -> Any:
        """
        Get agent-specific data for a user.
        
        Args:
            user_id: Unique identifier for the user.
            agent_id: Identifier for the agent (e.g., 'finance', 'chat').
            key: Data key.
            default: Default value to return if data is not set.
            
        Returns:
            The data value, or the default if not found.
        """
        user = self.get_user(user_id)
        if user and "agent_data" in user and agent_id in user["agent_data"]:
            return user["agent_data"][agent_id].get(key, default)
        return default
    
    def get_all_agent_data(self, user_id: str, agent_id: str) -> Dict[str, Any]:
        """
        Get all agent-specific data for a user.
        
        Args:
            user_id: Unique identifier for the user.
            agent_id: Identifier for the agent (e.g., 'finance', 'chat').
            
        Returns:
            Dictionary of all agent-specific data.
        """
        user = self.get_user(user_id)
        if user and "agent_data" in user:
            return user["agent_data"].get(agent_id, {})
        return {}
    
    def clear_agent_data(self, user_id: str, agent_id: str) -> bool:
        """
        Clear all agent-specific data for a user.
        
        Args:
            user_id: Unique identifier for the user.
            agent_id: Identifier for the agent (e.g., 'finance', 'chat').
            
        Returns:
            True if successful, False otherwise.
        """
        if not self._ensure_connection():
            return False
            
        try:
            update_field = f"agent_data.{agent_id}"
            result = self.users_collection.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        update_field: {},
                        "updated_at": datetime.now()
                    }
                }
            )
            
            if self.debug:
                self.logger.debug(f"Cleared all data for agent {agent_id} for user {user_id}")
                
            return result.acknowledged
        except PyMongoError as e:
            self.logger.error(f"Error clearing data for agent {agent_id} for user {user_id}: {str(e)}")
            return False
    
    # Finance agent specific methods (as an example)
    
    def add_to_watchlist(self, user_id: str, ticker: str, asset_type: str = "stock") -> bool:
        """
        Add a ticker to a user's finance watchlist.
        
        Args:
            user_id: Unique identifier for the user.
            ticker: The ticker symbol to add.
            asset_type: Either "stock" or "crypto".
            
        Returns:
            True if successful, False otherwise.
        """
        if not self._ensure_connection():
            return False
            
        try:
            # Get current watchlist
            watchlist = self.get_agent_data(user_id, "finance", "watchlist", [])
            
            # Check if ticker is already in watchlist
            for item in watchlist:
                if item.get("ticker") == ticker:
                    if self.debug:
                        self.logger.debug(f"Ticker {ticker} already in watchlist for user {user_id}")
                    return True
            
            # Add ticker to watchlist
            watchlist.append({
                "ticker": ticker,
                "asset_type": asset_type,
                "added_at": datetime.now()
            })
            
            # Update watchlist
            return self.set_agent_data(user_id, "finance", "watchlist", watchlist)
        except Exception as e:
            self.logger.error(f"Error adding ticker {ticker} to watchlist for user {user_id}: {str(e)}")
            return False
    
    def remove_from_watchlist(self, user_id: str, ticker: str) -> bool:
        """
        Remove a ticker from a user's finance watchlist.
        
        Args:
            user_id: Unique identifier for the user.
            ticker: The ticker symbol to remove.
            
        Returns:
            True if successful, False otherwise.
        """
        if not self._ensure_connection():
            return False
            
        try:
            # Get current watchlist
            watchlist = self.get_agent_data(user_id, "finance", "watchlist", [])
            
            # Remove ticker from watchlist
            new_watchlist = [item for item in watchlist if item.get("ticker") != ticker]
            
            # Update watchlist
            return self.set_agent_data(user_id, "finance", "watchlist", new_watchlist)
        except Exception as e:
            self.logger.error(f"Error removing ticker {ticker} from watchlist for user {user_id}: {str(e)}")
            return False
    
    def get_watchlist(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get a user's finance watchlist.
        
        Args:
            user_id: Unique identifier for the user.
            
        Returns:
            List of watchlist items.
        """
        return self.get_agent_data(user_id, "finance", "watchlist", [])
    
    # Chat history methods
    
    def save_chat_history(self, user_id: str, messages: List[Dict[str, Any]]) -> bool:
        """
        Save a user's chat history.
        
        Args:
            user_id: Unique identifier for the user.
            messages: List of message dictionaries.
            
        Returns:
            True if successful, False otherwise.
        """
        return self.set_agent_data(user_id, "chat", "history", messages)
    
    def get_chat_history(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get a user's chat history.
        
        Args:
            user_id: Unique identifier for the user.
            
        Returns:
            List of message dictionaries.
        """
        return self.get_agent_data(user_id, "chat", "history", [])
    
    # General methods
    
    def delete_user(self, user_id: str) -> bool:
        """
        Delete a user and all associated data.
        
        Args:
            user_id: Unique identifier for the user.
            
        Returns:
            True if successful, False otherwise.
        """
        if not self._ensure_connection():
            return False
            
        try:
            result = self.users_collection.delete_one({"user_id": user_id})
            
            if self.debug:
                self.logger.debug(f"Deleted user {user_id}")
                
            return result.acknowledged and result.deleted_count > 0
        except PyMongoError as e:
            self.logger.error(f"Error deleting user {user_id}: {str(e)}")
            return False
    
    def get_oauth_token_from_postgresql(self, user_id: str, provider: str) -> Optional[Dict[str, Any]]:
        """
        PLACEHOLDER: Get OAuth token from PostgreSQL database via Prisma.

        This method will be implemented to retrieve OAuth tokens from the external
        NextAuth website's PostgreSQL database using Prisma ORM.

        Args:
            user_id: The user's unique identifier
            provider: OAuth provider ('google', 'github', etc.)

        Returns:
            Dict containing OAuth token data, or None if not found

        TODO: Implement PostgreSQL/Prisma integration
        - Connect to PostgreSQL database using Prisma client
        - Query OAuth accounts/tokens table for user_id and provider
        - Return token data in the expected format
        - Handle token decryption if needed
        """
        # PLACEHOLDER IMPLEMENTATION - Replace with actual PostgreSQL/Prisma code
        if self.debug:
            self.logger.debug(f"[POSTGRESQL PLACEHOLDER] Would fetch {provider} token for user: {user_id}")
        return None

    def set_oauth_token_in_postgresql(self, user_id: str, provider: str, token_data: Dict[str, Any]) -> bool:
        """
        PLACEHOLDER: Store OAuth token in PostgreSQL database via Prisma.

        This method will be implemented to store OAuth tokens in the external
        NextAuth website's PostgreSQL database using Prisma ORM.

        Args:
            user_id: The user's unique identifier
            provider: OAuth provider ('google', 'github', etc.)
            token_data: Token data to store

        Returns:
            True if successful, False otherwise

        TODO: Implement PostgreSQL/Prisma integration
        - Connect to PostgreSQL database using Prisma client
        - Upsert token data in OAuth accounts/tokens table
        - Handle token encryption/security as needed
        - Return success/failure status
        """
        # PLACEHOLDER IMPLEMENTATION - Replace with actual PostgreSQL/Prisma code
        if self.debug:
            self.logger.debug(f"[POSTGRESQL PLACEHOLDER] Would store {provider} token for user: {user_id}")
            self.logger.debug(f"[POSTGRESQL PLACEHOLDER] Token data keys: {list(token_data.keys())}")
        return False

    def get_oauth_token(self, user_id: str, provider: str) -> Optional[Dict[str, Any]]:
        """
        Get OAuth token for a user and provider.

        This method first attempts to load from PostgreSQL (via NextAuth website),
        then falls back to MongoDB during the transition period.

        Args:
            user_id: The user's unique identifier
            provider: OAuth provider ('google', 'github', etc.)

        Returns:
            Dict containing OAuth token data, or None if not found
        """
        # STEP 1: Try to load from PostgreSQL database (NextAuth integration)
        # TODO: Uncomment this line once PostgreSQL integration is implemented
        # postgresql_token = self.get_oauth_token_from_postgresql(user_id, provider)
        # if postgresql_token:
        #     if self.debug:
        #         self.logger.debug(f"✅ Loaded {provider} token from PostgreSQL for user: {user_id}")
        #     return postgresql_token

        # STEP 2: Fallback to MongoDB (temporary during transition)
        if self.debug:
            self.logger.debug(f"[FALLBACK] Loading {provider} token from MongoDB for user: {user_id}")

        user_data = self.get_user(user_id)
        if user_data and 'oauth_tokens' in user_data:
            return user_data['oauth_tokens'].get(provider)
        return None

    def set_oauth_token(self, user_id: str, provider: str, token_data: Dict[str, Any]) -> bool:
        """
        Store OAuth token for a user and provider.

        This method first attempts to store in PostgreSQL (via NextAuth website),
        then falls back to MongoDB during the transition period.

        Args:
            user_id: The user's unique identifier
            provider: OAuth provider ('google', 'github', etc.)
            token_data: Token data to store

        Returns:
            True if successful, False otherwise
        """
        # STEP 1: Try to store in PostgreSQL database (NextAuth integration)
        # TODO: Uncomment these lines once PostgreSQL integration is implemented
        # postgresql_success = self.set_oauth_token_in_postgresql(user_id, provider, token_data)
        # if postgresql_success:
        #     if self.debug:
        #         self.logger.debug(f"✅ Stored {provider} token in PostgreSQL for user: {user_id}")
        #     return True

        # STEP 2: Fallback to MongoDB (temporary during transition)
        if self.debug:
            self.logger.debug(f"[FALLBACK] Storing {provider} token in MongoDB for user: {user_id}")

        if not self._ensure_connection():
            return False

        try:
            result = self.users_collection.update_one(
                {"user_id": user_id},
                {"$set": {f"oauth_tokens.{provider}": token_data}},
                upsert=True
            )

            if self.debug:
                self.logger.debug(f"Stored {provider} OAuth token for user {user_id}")

            return result.acknowledged
        except PyMongoError as e:
            self.logger.error(f"Error storing {provider} OAuth token for user {user_id}: {str(e)}")
            return False

    # ==========================================
    # CROSS-PLATFORM CONVERSATION HISTORY
    # ==========================================

    def get_recent_conversations_from_postgresql(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        PLACEHOLDER: Get recent conversations from PostgreSQL database via Prisma.

        This method will be implemented to retrieve cross-platform conversation history
        from the external NextAuth website's PostgreSQL database using Prisma ORM.

        Args:
            user_id: The user's unique identifier
            limit: Maximum number of conversations to retrieve (default: 50)

        Returns:
            List of conversation dictionaries in chronological order

        TODO: Implement PostgreSQL/Prisma integration
        - Connect to PostgreSQL database using Prisma client
        - Query conversations table for user_id
        - Order by timestamp DESC, limit to specified count
        - Return conversation data in expected format:
          {
              'id': 'conversation_id',
              'user_id': 'user_id',
              'platform': 'discord|telegram|web',
              'timestamp': datetime,
              'message_content': 'user_message',
              'response_content': 'ai_response',
              'metadata': {...}
          }
        """
        # PLACEHOLDER IMPLEMENTATION - Replace with actual PostgreSQL/Prisma code
        if self.debug:
            self.logger.debug(f"[POSTGRESQL PLACEHOLDER] Would fetch {limit} recent conversations for user: {user_id}")
        return []

    def add_conversation_to_postgresql(self, user_id: str, platform: str, message_data: Dict[str, Any]) -> bool:
        """
        PLACEHOLDER: Add conversation to PostgreSQL database via Prisma.

        This method will be implemented to store conversation history in the external
        NextAuth website's PostgreSQL database using Prisma ORM.

        Args:
            user_id: The user's unique identifier
            platform: Platform source ('discord', 'telegram', 'web', etc.)
            message_data: Conversation data to store

        Returns:
            True if successful, False otherwise

        TODO: Implement PostgreSQL/Prisma integration
        - Connect to PostgreSQL database using Prisma client
        - Insert conversation data into conversations table
        - Automatically trim to keep only most recent 50 conversations per user
        - Handle conversation metadata and timestamps
        """
        # PLACEHOLDER IMPLEMENTATION - Replace with actual PostgreSQL/Prisma code
        if self.debug:
            self.logger.debug(f"[POSTGRESQL PLACEHOLDER] Would add {platform} conversation for user: {user_id}")
        return False

    def get_recent_conversations(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get recent cross-platform conversations for a user.

        This method first attempts to load from PostgreSQL (via NextAuth website),
        then falls back to MongoDB during the transition period.

        Args:
            user_id: The user's unique identifier
            limit: Maximum number of conversations to retrieve (default: 50)

        Returns:
            List of conversation dictionaries in chronological order (newest first)
        """
        # STEP 1: Try to load from PostgreSQL database (NextAuth integration)
        # TODO: Uncomment these lines once PostgreSQL integration is implemented
        # postgresql_conversations = self.get_recent_conversations_from_postgresql(user_id, limit)
        # if postgresql_conversations:
        #     if self.debug:
        #         self.logger.debug(f"✅ Loaded {len(postgresql_conversations)} conversations from PostgreSQL for user: {user_id}")
        #     return postgresql_conversations

        # STEP 2: Fallback to MongoDB (temporary during transition)
        if self.debug:
            self.logger.debug(f"[FALLBACK] Loading conversations from MongoDB for user: {user_id}")

        if not self._ensure_connection():
            return []

        try:
            user_data = self.users_collection.find_one({"user_id": user_id})
            if user_data and 'conversations' in user_data:
                conversations = user_data['conversations']
                # Sort by timestamp (newest first) and limit
                sorted_conversations = sorted(
                    conversations,
                    key=lambda x: x.get('timestamp', datetime.min),
                    reverse=True
                )
                return sorted_conversations[:limit]
            return []
        except PyMongoError as e:
            self.logger.error(f"Error retrieving conversations for user {user_id}: {str(e)}")
            return []

    def add_conversation(self, user_id: str, platform: str, message_data: Dict[str, Any]) -> bool:
        """
        Add a new conversation to the user's cross-platform history.

        This method first attempts to store in PostgreSQL (via NextAuth website),
        then falls back to MongoDB during the transition period.

        Args:
            user_id: The user's unique identifier
            platform: Platform source ('discord', 'telegram', 'web', etc.)
            message_data: Dictionary containing:
                - message_content: User's message
                - response_content: AI's response
                - timestamp: When the conversation occurred (optional, defaults to now)
                - metadata: Additional platform-specific data (optional)

        Returns:
            True if successful, False otherwise
        """
        # STEP 1: Try to store in PostgreSQL database (NextAuth integration)
        # TODO: Uncomment these lines once PostgreSQL integration is implemented
        # postgresql_success = self.add_conversation_to_postgresql(user_id, platform, message_data)
        # if postgresql_success:
        #     if self.debug:
        #         self.logger.debug(f"✅ Added {platform} conversation to PostgreSQL for user: {user_id}")
        #     return True

        # STEP 2: Fallback to MongoDB (temporary during transition)
        if self.debug:
            self.logger.debug(f"[FALLBACK] Adding {platform} conversation to MongoDB for user: {user_id}")

        if not self._ensure_connection():
            return False

        try:
            # Prepare conversation entry
            conversation_entry = {
                'id': f"{user_id}_{platform}_{int(datetime.utcnow().timestamp())}",
                'user_id': user_id,
                'platform': platform,
                'timestamp': message_data.get('timestamp', datetime.utcnow()),
                'message_content': message_data.get('message_content', ''),
                'response_content': message_data.get('response_content', ''),
                'metadata': message_data.get('metadata', {})
            }

            # Add conversation and maintain limit of 50
            result = self.users_collection.update_one(
                {"user_id": user_id},
                {
                    "$push": {"conversations": conversation_entry},
                    "$slice": {"conversations": -50}  # Keep only last 50
                },
                upsert=True
            )

            if self.debug:
                self.logger.debug(f"Added {platform} conversation for user {user_id}")

            return result.acknowledged
        except PyMongoError as e:
            self.logger.error(f"Error adding conversation for user {user_id}: {str(e)}")
            return False
