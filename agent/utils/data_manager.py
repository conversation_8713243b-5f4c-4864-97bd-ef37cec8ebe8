"""
Data Manager Utility for Multi-Agent AI Platform.

This module provides convenient wrapper functions for the new data management capabilities,
making it easy for agents to use conversation history, user memory, and user instructions.
"""

import os
import sys
from datetime import datetime
from typing import Any, Dict, List, Optional

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from agent.database.user_store import UserStore

class DataManager:
    """
    Convenient wrapper for data management operations.
    
    This class provides easy-to-use methods for agents to interact with
    conversation history, user memory, and user instructions.
    """
    
    def __init__(self, mongodb_uri: str = None, debug: bool = False):
        """
        Initialize the DataManager.
        
        Args:
            mongodb_uri: MongoDB connection URI (optional)
            debug: Enable debug logging
        """
        self.user_store = UserStore(connection_string=mongodb_uri, debug=debug)
        self.debug = debug
    
    # ==========================================
    # CONVERSATION HISTORY METHODS
    # ==========================================
    
    def log_conversation(self, user_id: str, platform: str, user_message: str, 
                        ai_response: str, metadata: Dict[str, Any] = None) -> bool:
        """
        Log a conversation between user and AI.
        
        Args:
            user_id: The user's unique identifier
            platform: Platform source ('discord', 'telegram', 'web', etc.)
            user_message: The user's message
            ai_response: The AI's response
            metadata: Additional platform-specific data
            
        Returns:
            True if successful, False otherwise
        """
        conversation_data = {
            'message_content': user_message,
            'response_content': ai_response,
            'timestamp': datetime.utcnow(),
            'metadata': metadata or {}
        }
        
        return self.user_store.add_conversation(user_id, platform, conversation_data)
    
    def get_conversation_context(self, user_id: str, limit: int = 10) -> str:
        """
        Get recent conversation history formatted as context for AI.
        
        Args:
            user_id: The user's unique identifier
            limit: Number of recent conversations to include
            
        Returns:
            Formatted string containing conversation history
        """
        conversations = self.user_store.get_recent_conversations(user_id, limit)
        
        if not conversations:
            return "No previous conversation history available."
        
        context_lines = ["Recent conversation history:"]
        for conv in reversed(conversations):  # Show oldest first for context
            platform = conv.get('platform', 'unknown').upper()
            timestamp = conv.get('timestamp', datetime.min).strftime('%Y-%m-%d %H:%M')
            user_msg = conv.get('message_content', '')[:100]
            ai_msg = conv.get('response_content', '')[:100]
            
            context_lines.append(f"[{timestamp}] {platform}")
            context_lines.append(f"User: {user_msg}...")
            context_lines.append(f"AI: {ai_msg}...")
            context_lines.append("")
        
        return "\n".join(context_lines)
    
    def get_recent_conversations(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get recent conversations for a user.
        
        Args:
            user_id: The user's unique identifier
            limit: Maximum number of conversations to retrieve
            
        Returns:
            List of conversation dictionaries
        """
        return self.user_store.get_recent_conversations(user_id, limit)
    
    # ==========================================
    # USER MEMORY METHODS
    # ==========================================
    
    def remember_user_info(self, user_id: str, category: str, info: Dict[str, Any]) -> bool:
        """
        Remember information about a user in a specific category.
        
        Args:
            user_id: The user's unique identifier
            category: Memory category ('personal', 'preferences', 'context', etc.)
            info: Information to remember
            
        Returns:
            True if successful, False otherwise
        """
        memory_data = {category: info}
        return self.user_store.add_memory(user_id, memory_data)
    
    def update_user_info(self, user_id: str, memory_key: str, value: Any) -> bool:
        """
        Update specific user information.
        
        Args:
            user_id: The user's unique identifier
            memory_key: Key path for the memory (e.g., 'personal.name')
            value: New value to store
            
        Returns:
            True if successful, False otherwise
        """
        return self.user_store.update_memory(user_id, memory_key, value)
    
    def get_user_context(self, user_id: str) -> str:
        """
        Get user memory formatted as context for AI.
        
        Args:
            user_id: The user's unique identifier
            
        Returns:
            Formatted string containing user context
        """
        memory = self.user_store.get_user_memory(user_id)
        
        if not memory:
            return "No user context available."
        
        context_lines = ["User context and preferences:"]
        
        for category, data in memory.items():
            if category in ['last_updated', 'updated_by']:  # Skip metadata
                continue
                
            context_lines.append(f"\n{category.upper()}:")
            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, list):
                        value = ", ".join(str(v) for v in value)
                    context_lines.append(f"  {key}: {value}")
            else:
                context_lines.append(f"  {data}")
        
        return "\n".join(context_lines)
    
    def get_user_memory(self, user_id: str) -> Dict[str, Any]:
        """
        Get all user memory data.
        
        Args:
            user_id: The user's unique identifier
            
        Returns:
            Dictionary containing user memory data
        """
        return self.user_store.get_user_memory(user_id)
    
    # ==========================================
    # USER INSTRUCTIONS METHODS
    # ==========================================
    
    def get_user_instructions_for_agent(self, user_id: str, agent_type: str = 'global') -> str:
        """
        Get user instructions for a specific agent type.
        
        Args:
            user_id: The user's unique identifier
            agent_type: Type of agent ('global', 'chat', 'coding', 'finance', 'math')
            
        Returns:
            User instructions string, or empty string if none found
        """
        instructions = self.user_store.get_user_instructions(user_id)
        
        # Try to get specific instructions for the agent type
        if agent_type in instructions:
            return instructions[agent_type]
        
        # Fall back to global instructions
        if 'global' in instructions:
            return instructions['global']
        
        return ""
    
    def set_user_instructions(self, user_id: str, instructions: Dict[str, Any]) -> bool:
        """
        Set user instructions for AI behavior.
        
        Args:
            user_id: The user's unique identifier
            instructions: Dictionary containing instructions for different contexts
            
        Returns:
            True if successful, False otherwise
        """
        return self.user_store.set_user_instructions(user_id, instructions)
    
    def get_all_user_instructions(self, user_id: str) -> Dict[str, Any]:
        """
        Get all user instructions.
        
        Args:
            user_id: The user's unique identifier
            
        Returns:
            Dictionary containing all user instructions
        """
        return self.user_store.get_user_instructions(user_id)
    
    def delete_user_instructions(self, user_id: str) -> bool:
        """
        Delete all user instructions.
        
        Args:
            user_id: The user's unique identifier
            
        Returns:
            True if successful, False otherwise
        """
        return self.user_store.delete_user_instructions(user_id)
    
    # ==========================================
    # COMBINED CONTEXT METHODS
    # ==========================================
    
    def get_full_user_context(self, user_id: str, agent_type: str = 'global', 
                             conversation_limit: int = 5) -> str:
        """
        Get comprehensive user context including memory, instructions, and recent conversations.
        
        Args:
            user_id: The user's unique identifier
            agent_type: Type of agent for instructions
            conversation_limit: Number of recent conversations to include
            
        Returns:
            Formatted string containing full user context
        """
        context_parts = []
        
        # User instructions
        instructions = self.get_user_instructions_for_agent(user_id, agent_type)
        if instructions:
            context_parts.append(f"USER INSTRUCTIONS:\n{instructions}")
        
        # User memory/context
        user_context = self.get_user_context(user_id)
        if user_context != "No user context available.":
            context_parts.append(user_context)
        
        # Recent conversations
        conversation_context = self.get_conversation_context(user_id, conversation_limit)
        if conversation_context != "No previous conversation history available.":
            context_parts.append(conversation_context)
        
        if not context_parts:
            return "No user context available."
        
        return "\n\n" + "="*50 + "\n\n".join(context_parts)
    
    def close(self):
        """Close the database connection."""
        if self.user_store:
            self.user_store.close()

# Convenience functions for quick access
def log_conversation(user_id: str, platform: str, user_message: str, ai_response: str, 
                    metadata: Dict[str, Any] = None, mongodb_uri: str = None) -> bool:
    """Quick function to log a conversation."""
    dm = DataManager(mongodb_uri=mongodb_uri)
    try:
        return dm.log_conversation(user_id, platform, user_message, ai_response, metadata)
    finally:
        dm.close()

def get_user_context_for_agent(user_id: str, agent_type: str = 'global', 
                              mongodb_uri: str = None) -> str:
    """Quick function to get user context for an agent."""
    dm = DataManager(mongodb_uri=mongodb_uri)
    try:
        return dm.get_full_user_context(user_id, agent_type)
    finally:
        dm.close()
