#!/usr/bin/env python3
"""
Example usage of the new data management capabilities in UserStore.

This script demonstrates how to use:
1. Cross-Platform Conversation History
2. User Memory Storage  
3. User Instructions Storage

Run this script to see examples of all three features in action.
"""

import os
import sys
from datetime import datetime

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from agent.database.user_store import UserStore

def demonstrate_conversation_history():
    """Demonstrate cross-platform conversation history management."""
    print("\n" + "="*60)
    print("CROSS-PLATFORM CONVERSATION HISTORY DEMO")
    print("="*60)
    
    # Initialize UserStore
    user_store = UserStore(debug=True)
    test_user_id = "demo_user_123"
    
    # Add conversations from different platforms
    print("\n1. Adding conversations from different platforms...")
    
    # Discord conversation
    discord_conversation = {
        'message_content': 'How do I deploy a Python app?',
        'response_content': 'You can deploy a Python app using services like Heroku, AWS, or DigitalOcean...',
        'timestamp': datetime.utcnow(),
        'metadata': {'channel_id': '123456789', 'guild_id': '987654321'}
    }
    user_store.add_conversation(test_user_id, 'discord', discord_conversation)
    
    # Telegram conversation
    telegram_conversation = {
        'message_content': 'What are the best Python libraries for data science?',
        'response_content': 'The most popular Python libraries for data science include pandas, numpy, scikit-learn...',
        'timestamp': datetime.utcnow(),
        'metadata': {'chat_id': '-1001234567890', 'message_id': 42}
    }
    user_store.add_conversation(test_user_id, 'telegram', telegram_conversation)
    
    # Web conversation (future)
    web_conversation = {
        'message_content': 'Can you help me debug this JavaScript code?',
        'response_content': 'Sure! I can see the issue in your code. The problem is...',
        'timestamp': datetime.utcnow(),
        'metadata': {'session_id': 'web_session_abc123', 'ip_address': '***********'}
    }
    user_store.add_conversation(test_user_id, 'web', web_conversation)
    
    # Retrieve recent conversations
    print("\n2. Retrieving recent conversations...")
    recent_conversations = user_store.get_recent_conversations(test_user_id, limit=10)
    
    for i, conv in enumerate(recent_conversations, 1):
        print(f"\nConversation {i} ({conv['platform']}):")
        print(f"  User: {conv['message_content'][:50]}...")
        print(f"  AI: {conv['response_content'][:50]}...")
        print(f"  Time: {conv['timestamp']}")
    
    user_store.close()

def demonstrate_user_memory():
    """Demonstrate user memory storage and retrieval."""
    print("\n" + "="*60)
    print("USER MEMORY STORAGE DEMO")
    print("="*60)
    
    # Initialize UserStore
    user_store = UserStore(debug=True)
    test_user_id = "demo_user_123"
    
    # Add structured memory data
    print("\n1. Adding user memory data...")
    
    memory_data = {
        'personal': {
            'name': 'John Doe',
            'location': 'New York City',
            'occupation': 'Software Developer',
            'interests': ['Python', 'AI', 'Machine Learning']
        },
        'preferences': {
            'communication_style': 'concise',
            'code_style': 'pythonic',
            'explanation_level': 'intermediate',
            'preferred_examples': 'practical'
        },
        'context': {
            'current_project': 'AI Chat Assistant',
            'learning_goals': ['FastAPI', 'Docker', 'Kubernetes'],
            'recent_topics': ['database optimization', 'API design']
        },
        'work_info': {
            'company': 'Tech Startup Inc.',
            'role': 'Senior Developer',
            'tech_stack': ['Python', 'React', 'PostgreSQL']
        }
    }
    
    user_store.add_memory(test_user_id, memory_data)
    
    # Update specific memory entries
    print("\n2. Updating specific memory entries...")
    user_store.update_memory(test_user_id, 'personal.location', 'San Francisco')
    user_store.update_memory(test_user_id, 'context.current_project', 'Multi-Agent AI Platform')
    user_store.update_memory(test_user_id, 'preferences.communication_style', 'detailed')
    
    # Retrieve user memory
    print("\n3. Retrieving user memory...")
    user_memory = user_store.get_user_memory(test_user_id)
    
    print("User Memory Data:")
    for category, data in user_memory.items():
        if category not in ['last_updated', 'updated_by']:  # Skip metadata
            print(f"\n  {category.upper()}:")
            if isinstance(data, dict):
                for key, value in data.items():
                    print(f"    {key}: {value}")
            else:
                print(f"    {data}")
    
    user_store.close()

def demonstrate_user_instructions():
    """Demonstrate user instructions storage and management."""
    print("\n" + "="*60)
    print("USER INSTRUCTIONS STORAGE DEMO")
    print("="*60)
    
    # Initialize UserStore
    user_store = UserStore(debug=True)
    test_user_id = "demo_user_123"
    
    # Set user instructions
    print("\n1. Setting user instructions...")
    
    instructions = {
        'global': 'Always be helpful, accurate, and concise. Use examples when explaining concepts.',
        'chat': 'Use a friendly, conversational tone. Feel free to use emojis occasionally.',
        'coding': 'Provide detailed explanations with code examples. Always include comments in code.',
        'finance': 'Focus on practical, actionable advice. Always mention risks and disclaimers.',
        'math': 'Show step-by-step solutions. Explain the reasoning behind each step.',
        'custom_context': {
            'debugging': 'When helping with debugging, ask for error messages and relevant code context first.',
            'architecture': 'Focus on scalability, maintainability, and best practices.',
            'learning': 'Adapt explanations to my intermediate level. Provide additional resources for deeper learning.'
        }
    }
    
    user_store.set_user_instructions(test_user_id, instructions)
    
    # Retrieve user instructions
    print("\n2. Retrieving user instructions...")
    user_instructions = user_store.get_user_instructions(test_user_id)
    
    print("User Instructions:")
    for context, instruction in user_instructions.items():
        if context not in ['last_updated', 'updated_by']:  # Skip metadata
            print(f"\n  {context.upper()}:")
            if isinstance(instruction, dict):
                for key, value in instruction.items():
                    print(f"    {key}: {value}")
            else:
                print(f"    {instruction}")
    
    # Update specific instruction
    print("\n3. Updating specific instruction...")
    updated_instructions = user_instructions.copy()
    updated_instructions['chat'] = 'Use a professional but warm tone. Avoid emojis in serious discussions.'
    user_store.set_user_instructions(test_user_id, updated_instructions)
    
    print("Updated chat instruction successfully!")
    
    user_store.close()

def main():
    """Run all demonstrations."""
    print("🚀 UserStore Data Management Features Demo")
    print("This demo shows the three new data storage capabilities:")
    print("1. Cross-Platform Conversation History")
    print("2. User Memory Storage")
    print("3. User Instructions Storage")
    
    try:
        demonstrate_conversation_history()
        demonstrate_user_memory()
        demonstrate_user_instructions()
        
        print("\n" + "="*60)
        print("✅ ALL DEMOS COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("\nNote: These examples use MongoDB fallback during the transition period.")
        print("Once PostgreSQL/Prisma integration is implemented, the same methods")
        print("will automatically use the PostgreSQL database instead.")
        
    except Exception as e:
        print(f"\n❌ Error during demo: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
