import os
import json
import functools
import traceback
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List

import requests
from dotenv import load_dotenv

# --- Configuration ---
# Load environment variables from agent/.env
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(dotenv_path=dotenv_path)

current_dir = os.path.dirname(os.path.abspath(__file__))
DB_PATH = os.path.join(current_dir, '..', 'data', 'user_database.json')
MEMORY_PATH = os.path.join(current_dir, '..', 'data', 'integration_memory.json')

# GitHub API configuration
GITHUB_API_BASE = "https://api.github.com"

# --- Core Utilities ---

def load_github_token_from_postgresql(user_id: str) -> Optional[Dict[str, Any]]:
    """
    PLACEHOLDER: Load GitHub OAuth token from PostgreSQL database via Prisma.
    
    This function will be implemented to retrieve OAuth tokens from the external
    NextAuth website's PostgreSQL database using Prisma ORM.
    
    Args:
        user_id: The user's unique identifier
        
    Returns:
        Dict containing GitHub OAuth token data, or None if not found
        
    TODO: Implement PostgreSQL/Prisma integration
    - Connect to PostgreSQL database
    - Query OAuth tokens table for user_id and provider='github'
    - Return token data in the expected format:
      {
          'access_token': 'github_access_token',
          'token_type': 'bearer',
          'scope': 'repo,user',
          'expires_at': timestamp_in_ms (if applicable)
      }
    """
    # PLACEHOLDER IMPLEMENTATION - Replace with actual PostgreSQL/Prisma code
    print(f"[POSTGRESQL PLACEHOLDER] Would fetch GitHub token for user: {user_id}")
    return None

def load_github_token(user_id: str) -> Optional[Dict[str, Any]]:
    """
    Loads a user's GitHub token object from the database.
    
    This function first attempts to load from PostgreSQL (via NextAuth website),
    then falls back to the legacy JSON file system during the transition period.
    """
    # STEP 1: Try to load from PostgreSQL database (NextAuth integration)
    # TODO: Uncomment this line once PostgreSQL integration is implemented
    # postgresql_token = load_github_token_from_postgresql(user_id)
    # if postgresql_token:
    #     print(f"✅ Loaded GitHub token from PostgreSQL for user: {user_id}")
    #     return postgresql_token
    
    # STEP 2: Fallback to legacy JSON file system (temporary during transition)
    print(f"[FALLBACK] Loading GitHub token from JSON file for user: {user_id}")
    if not os.path.exists(DB_PATH):
        print(f"Token database not found at {DB_PATH}")
        return None
    try:
        with open(DB_PATH, "r") as f:
            data = json.load(f)
        user_data = data.get(user_id, {})
        # GitHub tokens stored under the 'github' key
        return user_data.get("github")
    except (json.JSONDecodeError, IOError) as e:
        print(f"Error reading token database: {e}")
        return None

def update_github_token_in_postgresql(user_id: str, new_token_data: Dict[str, Any]) -> bool:
    """
    PLACEHOLDER: Update GitHub OAuth token in PostgreSQL database via Prisma.
    
    This function will be implemented to store OAuth tokens in the external
    NextAuth website's PostgreSQL database using Prisma ORM.
    
    Args:
        user_id: The user's unique identifier
        new_token_data: Token data to store
        
    Returns:
        True if successful, False otherwise
        
    TODO: Implement PostgreSQL/Prisma integration
    - Connect to PostgreSQL database
    - Upsert token data in OAuth tokens table
    - Handle token encryption/security as needed
    - Return success/failure status
    """
    # PLACEHOLDER IMPLEMENTATION - Replace with actual PostgreSQL/Prisma code
    print(f"[POSTGRESQL PLACEHOLDER] Would update GitHub token for user: {user_id}")
    print(f"[POSTGRESQL PLACEHOLDER] Token data keys: {list(new_token_data.keys())}")
    return False  # Return False until actual implementation

def update_github_token_in_db(user_id: str, new_token_data: Dict[str, Any]):
    """
    Updates a user's GitHub token data in the database.
    
    This function first attempts to store in PostgreSQL (via NextAuth website),
    then falls back to the legacy JSON file system during the transition period.
    """
    # STEP 1: Try to update in PostgreSQL database (NextAuth integration)
    # TODO: Uncomment these lines once PostgreSQL integration is implemented
    # postgresql_success = update_github_token_in_postgresql(user_id, new_token_data)
    # if postgresql_success:
    #     print(f"✅ Updated GitHub token in PostgreSQL for user: {user_id}")
    #     return
    
    # STEP 2: Fallback to legacy JSON file system (temporary during transition)
    print(f"[FALLBACK] Updating GitHub token in JSON file for user: {user_id}")
    db_data = {}
    if os.path.exists(DB_PATH):
        with open(DB_PATH, "r") as f:
            try:
                db_data = json.load(f)
            except json.JSONDecodeError:
                db_data = {} # Handle empty or malformed file
    
    if user_id not in db_data:
        db_data[user_id] = {}
        
    # Store the new token data under the 'github' key
    db_data[user_id]["github"] = new_token_data

    try:
        with open(DB_PATH, "w") as f:
            json.dump(db_data, f, indent=2)
        print("✅ GitHub token updated in user_database.json.")
    except IOError as e:
        print(f"Error writing to token database: {e}")

def log_to_memory(log_entry: Dict[str, Any]):
    """Appends a log entry to the integration_memory.json file."""
    if not os.path.exists(MEMORY_PATH):
        with open(MEMORY_PATH, "w") as f:
            json.dump([], f)
    
    with open(MEMORY_PATH, "r+") as f:
        try:
            memory = json.load(f)
        except json.JSONDecodeError:
            memory = []
        memory.append(log_entry)
        f.seek(0)
        f.truncate()
        json.dump(memory, f, indent=2)

def handle_github_token_refresh(func):
    """Decorator to automatically handle GitHub token validation and retries."""
    @functools.wraps(func)
    def wrapper(user_id: str, *args, **kwargs):
        token_data = load_github_token(user_id)
        
        if not token_data:
            raise ValueError("No GitHub token found for user. Please connect your GitHub account via the website.")
        
        access_token = token_data.get('access_token')
        if not access_token:
            raise ValueError("Invalid GitHub token data. Please reconnect your GitHub account via the website.")

        # GitHub tokens typically don't expire, but we can validate them
        headers = {
            'Authorization': f'token {access_token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'AI-Agent-Platform'
        }
        
        # Test the token by making a simple API call
        try:
            test_response = requests.get(f"{GITHUB_API_BASE}/user", headers=headers)
            if test_response.status_code == 401:
                raise ValueError("GitHub token is invalid or expired. Please reconnect your GitHub account via the website.")
        except requests.RequestException as e:
            print(f"Error validating GitHub token: {e}")
            raise ValueError("Could not validate GitHub token. Please check your connection and try again.")

        # At this point, we should have a valid token
        try:
            return func(user_id, access_token, *args, **kwargs)
        except requests.HTTPError as e:
            print(f"GitHub API Error: {e.response.status_code} - {e.response.text}")
            raise
            
    return wrapper

# --- GitHub Service Functions ---

@handle_github_token_refresh
def get_user_repos(user_id: str, access_token: str, per_page: int = 10) -> List[Dict[str, Any]]:
    """Get user's GitHub repositories."""
    headers = {
        'Authorization': f'token {access_token}',
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'AI-Agent-Platform'
    }
    
    response = requests.get(f"{GITHUB_API_BASE}/user/repos", 
                          headers=headers, 
                          params={'per_page': per_page, 'sort': 'updated'})
    response.raise_for_status()
    return response.json()

@handle_github_token_refresh
def get_repo_issues(user_id: str, access_token: str, owner: str, repo: str, state: str = 'open') -> List[Dict[str, Any]]:
    """Get issues for a specific repository."""
    headers = {
        'Authorization': f'token {access_token}',
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'AI-Agent-Platform'
    }
    
    response = requests.get(f"{GITHUB_API_BASE}/repos/{owner}/{repo}/issues", 
                          headers=headers, 
                          params={'state': state})
    response.raise_for_status()
    return response.json()

@handle_github_token_refresh
def create_repo_issue(user_id: str, access_token: str, owner: str, repo: str, title: str, body: str = "") -> Dict[str, Any]:
    """Create a new issue in a repository."""
    headers = {
        'Authorization': f'token {access_token}',
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'AI-Agent-Platform'
    }
    
    data = {
        'title': title,
        'body': body
    }
    
    response = requests.post(f"{GITHUB_API_BASE}/repos/{owner}/{repo}/issues", 
                           headers=headers, 
                           json=data)
    response.raise_for_status()
    return response.json()

@handle_github_token_refresh
def get_user_profile(user_id: str, access_token: str) -> Dict[str, Any]:
    """Get user's GitHub profile information."""
    headers = {
        'Authorization': f'token {access_token}',
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'AI-Agent-Platform'
    }
    
    response = requests.get(f"{GITHUB_API_BASE}/user", headers=headers)
    response.raise_for_status()
    return response.json()

# Placeholder functions for future implementation
def search_repositories(*args, **kwargs): 
    print("Function not implemented"); 
    return {}

def get_repo_contents(*args, **kwargs): 
    print("Function not implemented"); 
    return {}

def create_repository(*args, **kwargs): 
    print("Function not implemented"); 
    return {}
