import os
import json
import functools
import traceback
import re
import base64
from email.mime.text import MIMEText
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List

import demjson3
import requests
from dotenv import load_dotenv
from google.auth.exceptions import RefreshError
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload
from google.auth.transport.requests import Request

from .fetch_google_token import fetch_google_oauth_token

# --- Configuration ---
# Load environment variables from agent/.env
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(dotenv_path=dotenv_path)

current_dir = os.path.dirname(os.path.abspath(__file__))
DB_PATH = os.path.join(current_dir, '..', 'data', 'user_database.json')
MEMORY_PATH = os.path.join(current_dir, '..', 'data', 'integration_memory.json')

GOOGLE_SERVICES = {
    "drive": "v3",
    "docs": "v1",
    "sheets": "v4",
    "calendar": "v3",
    "slides": "v1",
    "gmail": "v1",
}

# --- Core Utilities ---

def load_token_from_postgresql(user_id: str) -> Optional[Dict[str, Any]]:
    """
    PLACEHOLDER: Load Google OAuth token from PostgreSQL database via Prisma.

    This function will be implemented to retrieve OAuth tokens from the external
    NextAuth website's PostgreSQL database using Prisma ORM.

    Args:
        user_id: The user's unique identifier

    Returns:
        Dict containing Google OAuth token data, or None if not found

    TODO: Implement PostgreSQL/Prisma integration
    - Connect to PostgreSQL database
    - Query OAuth tokens table for user_id and provider='google'
    - Return token data in the expected format:
      {
          'token': 'access_token',
          'refresh_token': 'refresh_token',
          'scopes': ['scope1', 'scope2'],
          'expires_at': timestamp_in_ms
      }
    """
    # PLACEHOLDER IMPLEMENTATION - Replace with actual PostgreSQL/Prisma code
    print(f"[POSTGRESQL PLACEHOLDER] Would fetch Google token for user: {user_id}")
    return None

def load_token(user_id: str) -> Optional[Dict[str, Any]]:
    """
    Loads a user's full Google token object from the database.

    This function first attempts to load from PostgreSQL (via NextAuth website),
    then falls back to the legacy JSON file system during the transition period.
    """
    # STEP 1: Try to load from PostgreSQL database (NextAuth integration)
    # TODO: Uncomment this line once PostgreSQL integration is implemented
    # postgresql_token = load_token_from_postgresql(user_id)
    # if postgresql_token:
    #     print(f"✅ Loaded Google token from PostgreSQL for user: {user_id}")
    #     return postgresql_token

    # STEP 2: Fallback to legacy JSON file system (temporary during transition)
    print(f"[FALLBACK] Loading Google token from JSON file for user: {user_id}")
    if not os.path.exists(DB_PATH):
        print(f"Token database not found at {DB_PATH}")
        return None
    try:
        with open(DB_PATH, "r") as f:
            data = json.load(f)
        user_data = data.get(user_id, {})
        # Clerk stores the token under the 'google' key
        return user_data.get("google")
    except (json.JSONDecodeError, IOError) as e:
        print(f"Error reading token database: {e}")
        return None

def update_token_in_db(user_id: str, new_token_data: Dict[str, Any]):
    """Updates a user's Google token data in the database."""
    db_data = {}
    if os.path.exists(DB_PATH):
        with open(DB_PATH, "r") as f:
            try:
                db_data = json.load(f)
            except json.JSONDecodeError:
                db_data = {} # Handle empty or malformed file
    
    if user_id not in db_data:
        db_data[user_id] = {}
        
    # Store the new token data under the 'google' key
    db_data[user_id]["google"] = new_token_data

    try:
        with open(DB_PATH, "w") as f:
            json.dump(db_data, f, indent=2)
        print("✅ Token updated in user_database.json.")
    except IOError as e:
        print(f"Error writing to token database: {e}")


def log_to_memory(log_entry: Dict[str, Any]):
    """Appends a log entry to the integration_memory.json file."""
    if not os.path.exists(MEMORY_PATH):
        with open(MEMORY_PATH, "w") as f:
            json.dump([], f)
    
    with open(MEMORY_PATH, "r+") as f:
        try:
            memory = json.load(f)
        except json.JSONDecodeError:
            memory = []
        memory.append(log_entry)
        f.seek(0)
        f.truncate()
        json.dump(memory, f, indent=2)

def handle_token_refresh(func):
    """Decorator to automatically handle token expiration and retries."""
    @functools.wraps(func)
    def wrapper(user_id: str, *args, **kwargs):
        token_data = load_token(user_id)
        
        creds = None
        # Attempt to load credentials from the stored token data
        if token_data:
            # The token from Clerk doesn't include client_id/secret, so we add them
            # This is necessary for the refresh flow
            token_data['client_id'] = os.getenv("GOOGLE_CLIENT_ID")
            token_data['client_secret'] = os.getenv("GOOGLE_CLIENT_SECRET")
            token_data['token_uri'] = "https://oauth2.googleapis.com/token"
            creds = Credentials.from_authorized_user_info(info=token_data)

        # If credentials are not valid (missing, expired, etc.)
        if not creds or not creds.valid:
            # If creds exist but are expired and have a refresh token, try to refresh
            if creds and creds.expired and creds.refresh_token:
                print("Token is expired, attempting to refresh...")
                try:
                    creds.refresh(Request())
                    # If refresh is successful, persist the new token details
                    new_token_data = {
                        "token": creds.token,
                        "refresh_token": creds.refresh_token,
                        "scopes": creds.scopes,
                        "expires_at": int(creds.expiry.timestamp() * 1000) if creds.expiry else None,
                    }
                    update_token_in_db(user_id, new_token_data)
                    print("✅ Token refreshed and saved successfully.")
                except RefreshError as e:
                    print(f"RefreshError: {e}. Token is likely revoked. Fetching a new one from Clerk.")
                    creds = None # Nullify creds to trigger re-fetch
            
            # If no creds or refresh failed, fetch a new token from Clerk
            if not creds:
                print("No valid local token or refresh failed. Fetching new token from Clerk...")
                fetched_data = fetch_google_oauth_token(user_id)
                if fetched_data:
                    # Persist the newly fetched token data from Clerk
                    update_token_in_db(user_id, fetched_data)
                    # Now create credentials from this new data
                    fetched_data['client_id'] = os.getenv("GOOGLE_CLIENT_ID")
                    fetched_data['client_secret'] = os.getenv("GOOGLE_CLIENT_SECRET")
                    fetched_data['token_uri'] = "https://oauth2.googleapis.com/token"
                    creds = Credentials.from_authorized_user_info(info=fetched_data)
                    print("✅ New token fetched and saved.")
                else:
                    raise ValueError("Fatal: Could not fetch Google OAuth token from Clerk.")

        # At this point, we should have valid credentials
        try:
            return func(user_id, creds, *args, **kwargs)
        except HttpError as e:
            print(f"HttpError in wrapped function: {e.resp.status} - {e.reason}")
            raise
            
    return wrapper

def get_google_service(service_name: str, creds: Credentials):
    return build(service_name, GOOGLE_SERVICES[service_name], credentials=creds)

# --- Service-Specific Functions ---

@handle_token_refresh
def create_google_doc(user_id: str, creds: Credentials, title: str, content: Optional[str] = None) -> Dict[str, Any]:
    """Creates a new Google Doc, then fetches its Drive metadata for the webViewLink."""
    docs_service = get_google_service("docs", creds)
    drive_service = get_google_service("drive", creds)

    # 1. Create the Google Doc
    doc = docs_service.documents().create(body={"title": title}).execute()
    document_id = doc.get('documentId')

    if not document_id:
        return {"error": "Failed to create document or get its ID."}

    # 2. If content is provided, insert it into the new document
    if content:
        # This function is also decorated, but it will reuse the valid `creds`
        insert_text_into_doc(user_id, creds=creds, document_id=document_id, text=content)

    # 3. Fetch the full file metadata from Google Drive to get the webViewLink
    try:
        file_metadata = drive_service.files().get(
            fileId=document_id,
            fields="id, name, webViewLink"
        ).execute()
        return file_metadata
    except Exception as e:
        print(f"Error fetching Drive metadata for doc {document_id}: {e}")
        # Fallback to returning basic info if the Drive API call fails
        return {
            "id": document_id,
            "name": title,
            "webViewLink": f"https://docs.google.com/document/d/{document_id}/edit"
        }

@handle_token_refresh
def insert_text_into_doc(user_id: str, creds: Credentials, document_id: str, text: str) -> Dict[str, Any]:
    """Inserts text at the beginning of a Google Doc."""
    service = get_google_service("docs", creds)
    requests = [{"insertText": {"location": {"index": 1}, "text": text}}]
    result = service.documents().batchUpdate(documentId=document_id, body={"requests": requests}).execute()
    return result

@handle_token_refresh
def update_google_doc(user_id: str, creds: Credentials, document_id: str, text: str) -> Dict[str, Any]:
    """Appends text to the end of a Google Doc."""
    service = get_google_service("docs", creds)
    requests = [{"insertText": {"endOfSegmentLocation": {"segmentId": ""}, "text": f"\n\n{text}"}}]
    result = service.documents().batchUpdate(documentId=document_id, body={"requests": requests}).execute()
    print(f"✅ Appended text to document.")
    return result

@handle_token_refresh
def create_calendar_event(user_id: str, creds: Credentials, summary: str, start_time: str, duration_minutes: int = 60) -> Dict[str, Any]:
    service = get_google_service("calendar", creds)
    start_dt = datetime.fromisoformat(start_time)
    end_dt = start_dt + timedelta(minutes=duration_minutes)
    event_body = {
        'summary': summary,
        'start': {'dateTime': start_dt.isoformat(), 'timeZone': 'Asia/Dubai'},
        'end': {'dateTime': end_dt.isoformat(), 'timeZone': 'Asia/Dubai'},
    }
    created_event = service.events().insert(calendarId='primary', body=event_body).execute()
    return created_event

@handle_token_refresh
def search_drive_files(user_id: str, creds: Credentials, query: str) -> List[Dict[str, Any]]:
    service = get_google_service("drive", creds)
    response = service.files().list(q=query, fields="files(id, name, webViewLink)").execute()
    return response.get("files", [])

@handle_token_refresh
def send_gmail(user_id: str, creds: Credentials, to: str, subject: str, body: str) -> Dict[str, Any]:
    service = get_google_service("gmail", creds)
    message = MIMEText(body)
    message["to"] = to
    message["subject"] = subject
    raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
    message_body = {"raw": raw_message}
    sent_message = service.users().messages().send(userId="me", body=message_body).execute()
    return sent_message

# (Stubs for other functions to be implemented)
def update_calendar_event(*args, **kwargs): print("Function not implemented"); return {}
def read_spreadsheet(*args, **kwargs): print("Function not implemented"); return {}

@handle_token_refresh
def get_credentials(user_id: str, creds: Credentials) -> Optional[Credentials]:
    """
    This function now primarily serves to be decorated by handle_token_refresh.
    It returns the valid credentials object provided by the decorator.
    """
    return creds

# --- LLM and Main Application Logic ---

# [REMOVED] The placeholder functions call_openrouter, generate_content_from_prompt, and the main function have been removed.
