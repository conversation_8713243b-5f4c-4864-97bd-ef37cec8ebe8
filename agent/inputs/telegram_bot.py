import asyncio
import logging
import os
import io
import json
import sys
import traceback
import re
from typing import Optional, Dict, Any, List

# Add project root to sys.path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

# Load environment variables
from dotenv import load_dotenv
# Load .env from the 'agent' directory
dotenv_path = os.path.join(project_root, 'agent', '.env')
load_dotenv(dotenv_path=dotenv_path)


from googleapiclient.errors import HttpError
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, InputFile
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes, CallbackQueryHandler

from agent.agents.chat_agent import ChatAgent
from agent.agents.coding_agent import CodingAgent
from agent.agents.finance_agent import FinanceAgent
from agent.agents.math_agent import MathAgent
from agent.agents.web_search import WebSearchAgent
from agent.database.user_store import UserStore
from agent.integrations import google_services
from agent.model_core.model_router import ModelRouter, TaskType
from agent.utils.helpers import setup_logger, update_unified_history


class TelegramBot:
    """
    Telegram Bot for interfacing with the multi-agent AI platform.

    This bot handles:
    - User authentication and session management via a centralized user store.
    - Command routing to appropriate agents
    - Message processing and response generation
    - User preferences and settings
    """

    # Set up logger
    logger = setup_logger(__name__)

    # Command descriptions for the /help command
    COMMANDS = {
        "start": "Start the bot and link your account",
        "help": "Show this help message",
        "link": "Link your Telegram account with your central user account. Usage: /link <token>",
        "chat": "Chat with the AI assistant",
        "code": "Get help with coding questions",
        "finance": "Get financial information and analysis",
        "math": "Solve mathematical problems",
        "search": "Search the web for information",
        "google": "Use Google services (Docs, Calendar, etc.)",
        "web": "Toggle web search on/off",
        "model": "Select a specific AI model to use",
        "settings": "Configure your preferences",
    }

    def __init__(self, token: str, mongodb_uri: Optional[str] = None, debug: bool = False):
        """
        Initialize the TelegramBot.

        Args:
            token: Telegram Bot API token from BotFather
            mongodb_uri: MongoDB connection URI (optional, defaults to localhost)
            debug: Enable debug logging
        """
        self.token = token
        self.debug = debug

        # Set up logging if debug is enabled
        if debug:
            self.logger = setup_logger(__name__, logging.DEBUG)
            self.logger.debug("TelegramBot initialized with debug logging")

        # Initialize the user store
        mongodb_uri = mongodb_uri or "mongodb://localhost:27017/"
        self.user_store = UserStore(connection_string=mongodb_uri, debug=debug)

        # Initialize agents
        self.chat_agent = ChatAgent(debug=debug)
        self.coding_agent = CodingAgent(debug=debug)

        # Get Binance API credentials from environment variables
        binance_api_key = os.environ.get("BINANCE_API_KEY")
        binance_api_secret = os.environ.get("BINANCE_API_SECRET")

        self.finance_agent = FinanceAgent(
            binance_api_key=binance_api_key,
            binance_api_secret=binance_api_secret,
            debug=debug
        )

        self.math_agent = MathAgent(debug=debug)
        self.web_search_agent = WebSearchAgent(debug=debug)

        # Initialize model router
        self.model_router = ModelRouter(debug=debug)

        # Get OpenRouter API key for Google Services integration
        self.openrouter_api_key = os.environ.get("OPENROUTER_API_KEY")
        if not self.openrouter_api_key:
            self.logger.warning("OPENROUTER_API_KEY not found. Google Tools integration will not work.")

        # Initialize user states
        self.user_states: Dict[int, Dict[str, Any]] = {}

    async def _get_central_user_id(self, telegram_user_id: str) -> Optional[str]:
        """
        Retrieves the central user ID from the telegram user ID.
        This method assumes UserStore has a method to handle this mapping.
        """
        # This is a hypothetical method you'll need to implement in UserStore
        return self.user_store.get_central_user_id_from_telegram_id(telegram_user_id)

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle the /start command."""
        user = update.effective_user
        telegram_id = str(user.id)
        central_user_id = await self._get_central_user_id(telegram_id)

        if central_user_id:
            welcome_text = (
                f"👋 Welcome back, {user.first_name}!\n\n"
                f"Your account is linked and ready to go.\n"
                f"Use /help to see all available commands."
            )
        else:
            welcome_text = (
                f"👋 Hello {user.first_name}! Welcome.\n\n"
                f"To use my services, you first need to link your account.\n\n"
                f"1. Please register or log in on our main website.\n"
                f"2. Find the unique token on your profile page.\n"
                f"3. Use the command `/link <your_token>` here in the chat."
            )

        # Create keyboard with main functions
        keyboard = [
            [
                InlineKeyboardButton("💬 Chat", callback_data="mode_chat"),
                InlineKeyboardButton("💻 Code", callback_data="mode_code"),
            ],
            [
                InlineKeyboardButton("📊 Finance", callback_data="mode_finance"),
                InlineKeyboardButton("🔢 Math", callback_data="mode_math"),
            ],
            [
                InlineKeyboardButton("🔍 Web Search", callback_data="mode_search"),
                InlineKeyboardButton("🛠️ Google Tools", callback_data="mode_google"),
            ],
            [
                InlineKeyboardButton("⚙️ Settings", callback_data="settings"),
            ],
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        await update.message.reply_text(welcome_text, reply_markup=reply_markup)

    async def link_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Links a telegram account to a central user account using a token."""
        telegram_id = update.effective_user.id
        user = update.effective_user
        
        # If token is provided directly, process it
        if context.args:
            token = context.args[0]
            success = self.user_store.link_account_by_token("telegram", str(telegram_id), token)
            if success:
                welcome_message = (
                    f"🚀 Welcome aboard, {user.first_name}! Your account is now linked.\n\n"
                    f"You can start chatting with me right away. Try saying 'hello'!\n"
                    f"For a list of all commands, just type /help."
                )
                await update.message.reply_text(welcome_message)
            else:
                await update.message.reply_text("❌ Linking failed. The token might be invalid or expired.")
            
            # Clear any pending state
            if telegram_id in self.user_states:
                self.user_states[telegram_id].pop('state', None)
            return

        # If no token, set state to await the token
        if telegram_id not in self.user_states:
            self.user_states[telegram_id] = {}
        self.user_states[telegram_id]['state'] = 'awaiting_link_token'
        await update.message.reply_text("Please send me the linking token you received from the website.")

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Handle the /help command.

        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        help_text = "Here are the available commands:\n\n"
        for command, description in self.COMMANDS.items():
            help_text += f"/{command} - {description}\n"

        await update.message.reply_text(help_text)

    async def settings_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Handle the /settings command.

        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        telegram_id = str(update.effective_user.id)
        user_id = await self._get_central_user_id(telegram_id)
        if not user_id:
            await update.message.reply_text("Please link your account with `/link <token>` to manage settings.")
            return

        # Get current settings
        web_search_enabled = self.user_store.get_preference(user_id, "web_search_enabled", False)

        # Create settings keyboard
        keyboard = [
            [
                InlineKeyboardButton(
                    f"Web Search: {'✅ ON' if web_search_enabled else '❌ OFF'}",
                    callback_data=f"toggle_web_search_{not web_search_enabled}"
                ),
            ],
            [
                InlineKeyboardButton("🔗 Connect Google", callback_data="connect_google"),
                InlineKeyboardButton("🔗 Connect GitHub", callback_data="connect_github"),
            ],
            [
                InlineKeyboardButton("Back to Main Menu", callback_data="back_to_main"),
            ],
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text("⚙️ Settings:", reply_markup=reply_markup)

    async def set_mode_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Sets the conversation mode via a command like /chat, /code, etc."""
        # Split only on the first space to separate the command from potential arguments
        command_parts = update.message.text.lstrip('/').split(' ', 1)
        command = command_parts[0]
        
        supported_modes = ["chat", "code", "finance", "math", "search", "google"]

        if command in supported_modes:
            user_id = update.effective_user.id
            if user_id not in self.user_states:
                self.user_states[user_id] = {}
            
            self.user_states[user_id]["mode"] = command
            self.user_store.set_preference(str(user_id), "active_mode", command)
            
            await update.message.reply_text(
                f"✅ Mode set to **{command.capitalize()}**. You can now send your requests.",
                parse_mode="Markdown"
            )
        else:
            await update.message.reply_text("Unknown command. Use /help to see available modes.")

    async def web_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Handle the /web command to toggle web search on/off.

        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        telegram_id = str(update.effective_user.id)
        user_id = await self._get_central_user_id(telegram_id)
        if not user_id:
            await update.message.reply_text("Please link your account with `/link <token>` to manage settings.")
            return

        # Get current web search setting
        web_search_enabled = self.user_store.get_preference(user_id, "web_search_enabled", False)

        # Toggle web search
        new_setting = not web_search_enabled

        self.user_store.set_preference(user_id, "web_search_enabled", new_setting)

        # Inform the user
        status = "enabled" if new_setting else "disabled"
        await update.message.reply_text(f"🌐 Web search is now {status}.")

    async def model_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Handle the /model command to select a specific AI model.

        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        telegram_id = str(update.effective_user.id)
        user_id = await self._get_central_user_id(telegram_id)
        if not user_id:
            await update.message.reply_text("Please link your account with `/link <token>` to manage settings.")
            return

        # Get available models
        available_models = self.model_router.get_available_models()

        # Get current model if set
        current_model = self.user_store.get_preference(user_id, "selected_model", None)

        # Create keyboard with model options
        keyboard = []
        for model in available_models:
            button_text = f"✅ {model}" if model == current_model else model
            keyboard.append([InlineKeyboardButton(button_text, callback_data=f"select_model_{model}")])

        keyboard.append([InlineKeyboardButton("Back to Main Menu", callback_data="back_to_main")])
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text("Select a model:", reply_markup=reply_markup)

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Handle callback queries from inline keyboards.

        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        query = update.callback_query
        await query.answer()

        telegram_id = str(query.from_user.id)
        user_id = await self._get_central_user_id(telegram_id)

        # For some callbacks, we might not need a user_id yet (like setting mode)
        # But for others (like settings), we do.
        
        data = query.data

        # Mode selection
        if data.startswith("mode_"):
            mode = data.split("_")[1]
            self.user_states[query.from_user.id] = {"mode": mode}
            await query.edit_message_text(text=f"Mode set to {mode.capitalize()}.")

        # Settings toggle
        elif data.startswith("toggle_web_search_"):
            if not user_id:
                await query.edit_message_text(text="Please link your account first with `/link <token>`.")
                return
            new_setting_str = data.split("_")[-1]
            new_setting = new_setting_str.lower() == "true"

            self.user_store.set_preference(user_id, "web_search_enabled", new_setting)
            
            # Re-show the settings view after an edit
            # Creating a fake update object to call settings_command
            fake_update = type('FakeUpdate', (), {'message': query.message, 'effective_user': query.from_user})()
            await self.settings_command(fake_update, context)
            await query.edit_message_text(text=f"Web search {'enabled' if new_setting else 'disabled'}.")


        # Model selection
        elif data.startswith("select_model_"):
            if not user_id:
                await query.edit_message_text(text="Please link your account first with `/link <token>`.")
                return
            model = data.split("_")[-1]
            self.user_store.set_preference(user_id, "selected_model", model)
            await query.edit_message_text(text=f"Model set to {model}.")

        # Settings menu
        elif data == "settings":
            if not user_id:
                await query.edit_message_text(text="Please link your account first with `/link <token>`.")
                return

            # Create a fake update object to call settings_command
            fake_update = type('FakeUpdate', (), {'message': query.message, 'effective_user': query.from_user})()
            await self.settings_command(fake_update, context)

        # OAuth connection buttons
        elif data == "connect_google":
            await query.edit_message_text(
                "🔗 **Connect Google Account**\n\n"
                "To connect your Google account and access Google services (Docs, Calendar, Gmail, etc.), "
                "please visit our website and authenticate there.\n\n"
                "Your Google account connection will enable features like:\n"
                "• Creating and editing Google Docs\n"
                "• Managing Calendar events\n"
                "• Sending emails via Gmail\n"
                "• Accessing Drive files\n\n"
                "**Connect via website** to get started!",
                parse_mode="Markdown"
            )

        elif data == "connect_github":
            await query.edit_message_text(
                "🔗 **Connect GitHub Account**\n\n"
                "To connect your GitHub account and access GitHub services (repositories, issues, etc.), "
                "please visit our website and authenticate there.\n\n"
                "Your GitHub account connection will enable features like:\n"
                "• Viewing your repositories\n"
                "• Managing issues and pull requests\n"
                "• Creating new repositories\n"
                "• Accessing repository contents\n\n"
                "**Connect via website** to get started!",
                parse_mode="Markdown"
            )

        # Back to main menu
        elif data == "back_to_main":
            # Re-create the main menu keyboard
            keyboard = [
                [
                    InlineKeyboardButton("💬 Chat", callback_data="mode_chat"),
                    InlineKeyboardButton("💻 Code", callback_data="mode_code"),
                ],
                [
                    InlineKeyboardButton("📊 Finance", callback_data="mode_finance"),
                    InlineKeyboardButton("🔢 Math", callback_data="mode_math"),
                ],
                [
                    InlineKeyboardButton("🔍 Web Search", callback_data="mode_search"),
                    InlineKeyboardButton("🛠️ Google Tools", callback_data="mode_google"),
                ],
                [
                    InlineKeyboardButton("⚙️ Settings", callback_data="settings"),
                ],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            await query.edit_message_text("Select an option:", reply_markup=reply_markup)

    async def send_long_message(self, update: Update, text: str) -> None:

        if not text or not text.strip():
            await update.message.reply_text("⚠️ Sorry, I didn't get a meaningful response.")
            return

        """
        Send a message that might be longer than Telegram's message length limit.
        Splits the message into smaller chunks if necessary.

        Args:
            update: The update object from Telegram
            text: The text message to send
        """
        TELEGRAM_LIMIT = 4096

        # Define a template for headers and footers to calculate their maximum possible length.
        # This assumes the number of chunks will not exceed 99.
        max_header = "(Continued 99/99)..."
        footer = "(Continued in next message...)"
        separators = "\n\n"

        # Calculate the maximum overhead for a message part that is in the middle of a sequence
        # (i.e., has both a header and a footer).
        max_overhead = len(max_header) + len(separators) + len(footer)

        # Determine a safe chunk size by subtracting the max overhead from the Telegram limit.
        CHUNK_SIZE = TELEGRAM_LIMIT - max_overhead

        # Split the text into chunks of the calculated safe size.
        chunks = [text[i:i + CHUNK_SIZE] for i in range(0, len(text), CHUNK_SIZE)]
        num_chunks = len(chunks)

        # If the text fits into a single chunk, send it as one message.
        if num_chunks == 1:
            await update.message.reply_text(text)
            return

        # Iterate through the chunks and send them as separate messages.
        for i, chunk in enumerate(chunks):
            message_parts = []

            # Add a header to all chunks except the first one.
            if i > 0:
                message_parts.append(f"(Continued {i + 1}/{num_chunks})...")

            # Add the text chunk.
            message_parts.append(chunk)

            # Add a footer to all chunks except the last one.
            if i < num_chunks - 1:
                message_parts.append("(Continued in next message...)")

            # Join the parts into a single message string.
            message = "\n\n".join(message_parts)

            # As a final safeguard, truncate the message if it somehow still exceeds the limit.
            if len(message) > TELEGRAM_LIMIT:
                message = message[:TELEGRAM_LIMIT - 4] + "..."

            await update.message.reply_text(message)

            # Add a small delay between messages to ensure they are delivered in the correct order.
            if i < num_chunks - 1:
                await asyncio.sleep(0.5)

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Handle incoming messages and route to appropriate agents.
        Implements a hybrid messaging strategy for detailed financial analysis.

        Args:
            update: The update object from Telegram
            context: The context object from Telegram
        """
        user = update.effective_user
        telegram_id = user.id
        message_text = update.message.text

        # Check for state-based actions first
        user_state_info = self.user_states.get(telegram_id, {})
        current_state = user_state_info.get('state')

        if current_state == 'awaiting_link_token':
            token = message_text.strip()
            success = self.user_store.link_account_by_token("telegram", str(telegram_id), token)
            if success:
                welcome_message = (
                    f"🚀 Welcome aboard, {user.first_name}! Your account is now linked.\n\n"
                    f"You can start chatting with me right away. Try saying 'hello'!\n"
                    f"For a list of all commands, just type /help."
                )
                await update.message.reply_text(welcome_message)
            else:
                await update.message.reply_text("❌ Linking failed. The token might be invalid or expired. Please try again or use `/link` to restart.")
            
            # Clear the state regardless of outcome
            user_state_info.pop('state', None)
            return
        
        user_id = await self._get_central_user_id(str(telegram_id))
        if not user_id:
            await update.message.reply_text("Please link your account first. Use the `/link <token>` command.")
            return

        # Indicate typing
        await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")

        # Get preferences
        web_search_enabled = self.user_store.get_preference(user_id, "web_search_enabled", False)
        selected_model = self.user_store.get_preference(user_id, "selected_model", None)
        active_mode = self.user_store.get_preference(user_id, "active_mode", "chat")

        if user.id in self.user_states:
            active_mode = self.user_states[user.id].get("mode", active_mode)

        web_search = {"enable": True} if web_search_enabled or active_mode == "search" else None

        response = "I couldn't process your request. Please try again."
        message_sent_manually = False

        try:
            # Map active_mode to TaskType for model selection
            task_type_mapping = {
                "chat": TaskType.CHAT,
                "code": TaskType.CODING,
                "finance": TaskType.FINANCE,
                "math": TaskType.MATHS,
                "search": TaskType.CHAT,  # Search uses a chat model
                "google": TaskType.CHAT   # Google tools also use a chat model
            }
            task_type = task_type_mapping.get(active_mode, TaskType.CHAT)

            # Determine the model to use
            default_model = self.model_router.get_model_for_task(task_type)
            model_to_use = selected_model or default_model
            self.logger.debug(f"Determined model for this request: {model_to_use}")

            if not web_search_enabled and active_mode != "search" and "search" in message_text.lower():
                response = "It looks like you might be asking for information that requires web search. " \
                           "Web search is currently disabled. Use /web command to enable web search."

            elif active_mode == "chat":
                response = self.chat_agent.run(message_text, web_search=web_search, user_id=user_id, model=model_to_use, update_history=False, clear_context=True)

            elif active_mode == "code":
                response = self.coding_agent.run(message_text, web_search=web_search, user_id=user_id, model=model_to_use, update_history=False, clear_context=True)

            elif active_mode == "finance":
                # Ensure we request HTML charts
                finance_result = self.finance_agent.run(
                    message_text, web_search=web_search, platform="web", user_id=user_id, model=model_to_use, clear_context=True
                )

                if not isinstance(finance_result, dict):
                    await update.message.reply_text("❌ Finance agent returned unexpected result.")
                    return

                ticker = finance_result.get("ticker")
                full_report = finance_result.get("insight", "").strip()
                price = finance_result.get("price", "N/A")
                recommendation = finance_result.get("recommendation", "N/A")

                if not ticker or ticker == message_text:
                    await update.message.reply_text(
                        "❌ Failed to resolve a valid ticker symbol from your query. Please try again.")
                    return

                if not full_report or full_report == "No analysis available.":
                    await update.message.reply_text("❌ No valid financial analysis was returned.")
                    return

                response = full_report  # For chat history

                # Compose summary message
                summary_section = full_report.split("####")[1].strip() if "####" in full_report else full_report[:500]
                
                # Sanitize the summary to remove characters that might break Markdown parsing
                sanitized_summary = re.sub(r'([_*`\[\]\(\)])', '', summary_section)

                summary_caption = (
                    f"📈 *{ticker} Summary*\n"
                    f"Price: ${price}\n"
                    f"Recommendation: *{recommendation.capitalize()}*\n\n"
                    f"{sanitized_summary}...\n\n"
                    f"_Full analysis and chart are attached below._"
                )
                await update.message.reply_text(summary_caption, parse_mode="Markdown")

                # Send analysis .txt file
                file_stream = io.BytesIO(full_report.encode('utf-8'))
                file_stream.name = f"{ticker}_analysis.txt"
                await update.message.reply_document(
                    document=file_stream,
                    filename=file_stream.name,
                    caption="📄 Full financial analysis"
                )

                # Send chart HTML file if available
                chart_data = finance_result.get("chart")
                chart_type = finance_result.get("chart_type")

                if chart_type == "html" and chart_data:
                    html_bytes = chart_data.encode("utf-8")
                    html_stream = io.BytesIO(html_bytes)
                    html_stream.name = f"{ticker}_chart.html"

                    await update.message.reply_document(
                        document=html_stream,
                        filename=html_stream.name,
                        caption="📊 Interactive chart (HTML)"
                    )

                message_sent_manually = True

            elif active_mode == "math":
                response = self.math_agent.run(message_text, web_search=web_search, user_id=user_id, model=model_to_use, update_history=False, clear_context=True)

            elif active_mode == "search":
                response = self.web_search_agent.run(message_text, user_id=user_id, model=model_to_use, update_history=False, clear_context=True)
            
            elif active_mode == "google":
                self.logger.debug("Entered Google mode block.")
                if not self.openrouter_api_key:
                    response = "Google integration is not configured (missing OPENROUTER_API_KEY). Please contact the administrator."
                else:
                    try:
                        if user.id not in self.user_states: self.user_states[user.id] = {}
                        if 'google_session_context' not in self.user_states[user.id]:
                            self.user_states[user.id]['google_session_context'] = {"last_doc_id": None}
                        session_context = self.user_states[user.id]['google_session_context']

                        # 1. First LLM Call: Determine user's intent with a clearer prompt
                        intent_system_prompt = f"""You are an expert at routing a user's request to a function.
                        Based on the user's request, choose ONE action and its parameters.
                        Your response MUST be a valid JSON object with an "action" key and a "parameters" key.
                        Do not include any reasoning, explanations, or other text. Your response should only be the JSON object.

                        # Actions
                        - generate_content: (title, content_prompt)
                        - create_google_doc: (title, content)
                        - update_google_doc: (document_id, text)
                        - create_calendar_event: (summary, start_time, duration_minutes)
                        - search_drive_files: (query)
                        - send_gmail: (to, subject, body)
                        
                        # Context
                        {json.dumps(session_context)}
                        """
                        intent_messages = [
                            {"role": "system", "content": intent_system_prompt},
                            {"role": "user", "content": message_text}
                        ]
                        
                        # Give the model more tokens to ensure it can respond fully
                        intent_response_json = self.chat_agent.model_config.client.call_model(
                            messages=intent_messages, model=model_to_use, max_tokens=2048
                        )
                        
                        # Robustly extract and parse the response from the model
                        json_response_str = None
                        if intent_response_json.get('choices'):
                            message = intent_response_json['choices'][0].get('message', {})
                            json_response_str = message.get('content')

                        if not json_response_str:
                            self.logger.error(f"LLM did not return content for intent parsing. Full response: {intent_response_json}")
                            response = "I'm sorry, I had trouble understanding your request. Could you please rephrase it?"
                        else:
                            try:
                                # Extract JSON from markdown code block if present
                                if '```' in json_response_str:
                                    # Find the start and end of the JSON block
                                    start = json_response_str.find('{')
                                    end = json_response_str.rfind('}')
                                    if start != -1 and end != -1:
                                        json_response_str = json_response_str[start:end+1]

                                parsed_json = json.loads(json_response_str)
                                self.logger.debug(f"🤖 LLM suggested intent: {json.dumps(parsed_json, indent=2)}")

                                action = parsed_json.get("action")
                                params = parsed_json.get("parameters", {})
                                
                                if action == "generate_content":
                                    await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")
                                    doc_title = params.get("title", "Untitled Document")
                                    content_prompt = params.get("content_prompt", message_text)

                                    generation_messages = [
                                        {"role": "system", "content": "You are a helpful writing assistant."},
                                        {"role": "user", "content": content_prompt}
                                    ]
                                    
                                    generation_response_json = self.chat_agent.model_config.client.call_model(
                                        messages=generation_messages, model=model_to_use
                                    )
                                    generated_text = generation_response_json['choices'][0]['message']['content']
                                    
                                    result = google_services.create_google_doc(user_id, title=doc_title, content=generated_text)
                                    
                                    doc_id = result.get('id')
                                    if doc_id:
                                        session_context['last_doc_id'] = doc_id
                                        doc_name = result.get('name', 'Untitled')
                                        doc_link = result.get('webViewLink', 'N/A')
                                        response = f"✅ Successfully created Google Doc: '{doc_name}'\nView it here: {doc_link}"
                                    else:
                                        response = f"❌ Failed to create Google Doc. Result: {result.get('error', 'Unknown error')}"

                                else:
                                    # This block handles direct actions like create_calendar_event, etc.
                                    action_map = {
                                        "create_calendar_event": google_services.create_calendar_event,
                                        "create_google_doc": google_services.create_google_doc,
                                        "update_google_doc": google_services.update_google_doc,
                                        "search_drive_files": google_services.search_drive_files,
                                        "send_gmail": google_services.send_gmail,
                                    }
                                    if action in action_map:
                                        if action == 'update_google_doc' and 'document_id' not in params:
                                            params['document_id'] = session_context.get('last_doc_id')
                                        
                                        if action == 'update_google_doc' and not params.get('document_id'):
                                            response = "❌ I don't know which document to update. Please create or search for one first."
                                        else:
                                            result = action_map[action](user_id, **params)
                                            response = f"✅ Action '{action}' successful."
                                            doc_id = result.get('id') or result.get('documentId')
                                            if doc_id:
                                                session_context['last_doc_id'] = doc_id
                                            if action == 'create_google_doc' and result.get('webViewLink'):
                                                response = f"✅ Successfully created Google Doc: '{result.get('name')}'\nView it here: {result.get('webViewLink')}"
                                            elif action == 'search_drive_files' and isinstance(result, list):
                                                if not result: response = "No files found matching your query."
                                                else:
                                                    response_lines = ["Found files:"] + [f"- {f['name']}: {f['webViewLink']}" for f in result]
                                                    response = "\n".join(response_lines)
                                    else:
                                        response = f"❓ Unknown action: '{action}'"
                            
                            except json.JSONDecodeError:
                                self.logger.error(f"Failed to decode JSON from LLM response: {json_response_str}")
                                response = "I'm sorry, I couldn't structure your request properly. Please try again."

                    except ValueError as e:
                        self.logger.error(f"ValueError in Google mode for user {user.id}: {str(e)}")
                        # This error now has a clear, user-actionable meaning.
                        response = "🔗 **Google Account Not Connected**\n\nTo use Google Tools, please connect your Google account via our website first. This will enable access to Google Docs, Calendar, Gmail, and Drive.\n\n**Visit our website to connect your Google account!**"
                    except HttpError as e:
                        self.logger.error(f"Google API HttpError: {e.resp.status} - {e.content}")
                        if e.resp.status in [401, 403]:
                            response = "🔗 **Google Authentication Error**\n\nYour Google account connection may have expired or been revoked. Please visit our website to reconnect your Google account.\n\n**Visit our website to refresh your Google connection!**"
                        else:
                            response = f"An error occurred while communicating with Google services: Status {e.resp.status}"
                    except Exception as e:
                        self.logger.error(f"Error processing Google command: {str(e)}")
                        self.logger.error(traceback.format_exc())
                        response = "Sorry, I encountered an error while processing your Google command."

        except Exception as e:
            self.logger.error(f"Error processing message: {str(e)}")
            self.logger.error(traceback.format_exc())
            response = "Sorry, I encountered an error while processing your request. Please try again later."

        # Save to chat history
        self.logger.debug(f"Final response to be sent: '{response[:200]}...'")
        chat_history = self.user_store.get_chat_history(user_id)
        chat_history.append({"role": "user", "content": message_text})
        chat_history.append({"role": "assistant", "content": response})

        if len(chat_history) > 20:
            chat_history = chat_history[-20:]

        self.user_store.save_chat_history(user_id, chat_history)

        # Also save chat history to JSON file
        self.save_chat_history_to_json(user_id, chat_history)

        # Update unified history with metadata
        update_unified_history(
            user_id=user_id,
            platform="telegram",
            message_text=message_text,
            response_text=response,
            model_used=model_to_use,
            agent_used=active_mode,
            web_search_used=(web_search is not None)
        )

        self.logger.debug(f"Message manually sent: {message_sent_manually}")
        if not message_sent_manually:
            await self.send_long_message(update, response)
        self.logger.debug("Finished handle_message.")

    def save_chat_history_to_json(self, user_id: str, chat_history: List[Dict[str, Any]]) -> None:
        """
        Save chat history to a JSON file.

        Args:
            user_id: Unique identifier for the user.
            chat_history: List of message dictionaries.
        """
        try:
            # Create data directory if it doesn't exist
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
            os.makedirs(data_dir, exist_ok=True)

            # Create user-specific JSON file
            file_path = os.path.join(data_dir, f'chat_history_{user_id}.json')

            # Write chat history to JSON file with pretty formatting
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(chat_history, f, indent=4, ensure_ascii=False)

            if self.debug:
                self.logger.debug(f"Saved chat history to JSON file for user {user_id}")
        except Exception as e:
            self.logger.error(f"Error saving chat history to JSON file for user {user_id}: {str(e)}")

    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Log Errors caused by Updates."""
        self.logger.error("Exception while handling an update:", exc_info=context.error)

    def run(self):
        """
        Run the Telegram bot.
        """
        self.logger.info("Starting Telegram bot...")
        app = Application.builder().token(self.token).build()

        # Add command handlers
        app.add_handler(CommandHandler("start", self.start_command))
        app.add_handler(CommandHandler("help", self.help_command))
        app.add_handler(CommandHandler("link", self.link_command))
        app.add_handler(CommandHandler("settings", self.settings_command))
        app.add_handler(CommandHandler("web", self.web_command))
        app.add_handler(CommandHandler("model", self.model_command))

        # Add command handlers for setting modes
        app.add_handler(CommandHandler("chat", self.set_mode_command))
        app.add_handler(CommandHandler("code", self.set_mode_command))
        app.add_handler(CommandHandler("finance", self.set_mode_command))
        app.add_handler(CommandHandler("math", self.set_mode_command))
        app.add_handler(CommandHandler("search", self.set_mode_command))
        app.add_handler(CommandHandler("google", self.set_mode_command))

        # Add message handler
        app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))

        # Add callback query handler
        app.add_handler(CallbackQueryHandler(self.handle_callback))

        # Add error handler
        app.add_error_handler(self.error_handler)

        # Run the bot
        app.run_polling()


if __name__ == '__main__':
    # Load environment variables from .env file
    from dotenv import load_dotenv
    import os

    # Construct path to .env file at agent/.env
    dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    load_dotenv(dotenv_path=dotenv_path)

    # Get credentials from environment
    telegram_token = os.environ.get("TELEGRAM_BOT_TOKEN")
    mongo_uri = os.environ.get("MONGODB_URI")

    if not telegram_token:
        raise ValueError("TELEGRAM_BOT_TOKEN not found in environment variables")

    # Start the bot
    bot = TelegramBot(token=telegram_token, mongodb_uri=mongo_uri, debug=True)
    bot.run()
