import asyncio
import logging
import os
import io
import json
import time
from typing import Optional, Dict, Any, List

import discord
from discord.ext import commands

from agent.agents.chat_agent import ChatAgent
from agent.agents.coding_agent import CodingAgent
from agent.agents.finance_agent import FinanceAgent
from agent.agents.math_agent import MathAgent
from agent.agents.web_search import WebSearchAgent
from agent.database.user_store import UserStore
from agent.model_core.model_router import ModelRouter, TaskType
from agent.utils.helpers import setup_logger, update_unified_history
from dotenv import load_dotenv
load_dotenv()


class DiscordBot:
    """
    Discord Bot for interfacing with the multi-agent AI platform.

    This bot handles:
    - User authentication and session management
    - Command routing to appropriate agents
    - Message processing and response generation
    - User preferences and settings
    """

    # Set up logger
    logger = setup_logger(__name__)

    # Command descriptions for the /help command
    COMMANDS = {
        "start": "Start the bot and get a welcome message",
        "help": "Show this help message",
        "chat": "Chat with the AI assistant",
        "code": "Get help with coding questions",
        "finance": "Get financial information and analysis",
        "math": "Solve mathematical problems",
        "search": "Search the web for information",
        "web": "Toggle web search on/off",
        "model": "Select a specific AI model to use",
        "settings": "Configure your preferences",
    }

    def __init__(self, token: str, mongodb_uri: Optional[str] = None, debug: bool = False,
                 dev_mode: bool = False, dev_guild_id: Optional[int] = None):
        """
        Initialize the DiscordBot.

        Args:
            token: Discord Bot API token
            mongodb_uri: MongoDB connection URI (optional, defaults to localhost)
            debug: Enable debug logging
            dev_mode: Enable development mode for testing
            dev_guild_id: Guild ID to sync commands to in development mode
        """
        self.token = token
        self.debug = debug
        self.dev_mode = dev_mode
        self.dev_guild_id = dev_guild_id

        # Set up logging if debug is enabled
        if debug:
            self.logger = setup_logger(__name__, logging.DEBUG)
            self.logger.debug("DiscordBot initialized with debug logging")

        # Initialize the user store
        mongodb_uri = mongodb_uri or "mongodb://localhost:27017/"
        self.user_store = UserStore(connection_string=mongodb_uri, debug=debug)

        # Initialize agents
        self.chat_agent = ChatAgent(debug=debug)
        self.coding_agent = CodingAgent(debug=debug)

        # Get Binance API credentials from environment variables
        binance_api_key = os.environ.get("BINANCE_API_KEY")
        binance_api_secret = os.environ.get("BINANCE_API_SECRET")

        self.finance_agent = FinanceAgent(
            binance_api_key=binance_api_key,
            binance_api_secret=binance_api_secret,
            debug=debug
        )

        self.math_agent = MathAgent(debug=debug)
        self.web_search_agent = WebSearchAgent(debug=debug)

        # Initialize model router
        self.model_router = ModelRouter(debug=debug)

        # Initialize user states
        self.user_states: Dict[int, Dict[str, Any]] = {}

        # Set up Discord client with intents
        intents = discord.Intents.default()
        intents.message_content = True
        intents.members = True

        self.client = commands.Bot(command_prefix='/', intents=intents)

        # Register event handlers
        self.client.event(self.on_ready)
        self.client.event(self.on_message)
        self.client.event(self.on_interaction)

        # Register commands
        self.setup_commands()

    async def on_ready(self):
        """Called when the bot is ready and connected to Discord."""
        self.logger.info(f"Logged in as {self.client.user.name} ({self.client.user.id})")

        # Sync commands with Discord
        try:
            if self.dev_mode and self.dev_guild_id:
                # In development mode, sync commands only to the specified guild
                # This avoids Discord's global sync cooldowns during testing
                guild = discord.Object(id=self.dev_guild_id)
                self.client.tree.copy_global_to(guild=guild)
                synced = await self.client.tree.sync(guild=guild)
                self.logger.info(f"Dev mode: Synced {len(synced)} command(s) to guild {self.dev_guild_id}")
            else:
                # In production mode, sync commands globally
                synced = await self.client.tree.sync()
                self.logger.info(f"Production mode: Synced {len(synced)} command(s) globally")
        except Exception as e:
            self.logger.error(f"Failed to sync commands: {e}")

        await self.client.wait_until_ready()
        self.client.add_view(self.MainMenuView(self))

    def setup_commands(self):
        """Register all slash commands."""

        # Command mapping for standard commands
        command_mapping = {
            "start": {"description": "Start the bot and get a welcome message", "callback": self.start_command},
            "help": {"description": "Show help message with available commands", "callback": self.help_command},
            "link": {"description": "Link your account with a token", "callback": self.link_command},
            "settings": {"description": "Configure your preferences", "callback": self.settings_command},
            "web": {"description": "Toggle web search on/off", "callback": self.web_command},
            "model": {"description": "Select a specific AI model to use", "callback": self.model_command},
        }

        # Mode commands mapping
        mode_commands = {
            "chat": "Chat with the AI assistant",
            "code": "Get help with coding questions",
            "finance": "Get financial information",
            "math": "Solve mathematical problems",
            "search": "Search the web for information"
        }

        # Register standard commands
        for cmd_name, cmd_info in command_mapping.items():
            if cmd_name == "link":
                @self.client.tree.command(name="link", description="Link your account with a token")
                async def link_cmd(interaction: discord.Interaction, token: str):
                    await self.link_command(interaction, token)
            else:
                @self.client.tree.command(name=cmd_name, description=cmd_info["description"])
                async def command_callback(interaction: discord.Interaction, _cmd_name: str = cmd_name):
                    await command_mapping[_cmd_name]["callback"](interaction)

        # Register mode commands
        for mode, description in mode_commands.items():
            @self.client.tree.command(name=mode, description=f"Set mode to {description.lower()}")
            async def mode_callback(interaction: discord.Interaction, _mode: str = mode):
                await self.set_mode(interaction, _mode)

    def create_main_menu_view(self) -> discord.ui.View:
        """
        Create a view with the main menu buttons.

        Returns:
            A Discord UI View with the main menu buttons
        """
        # Use the persistent view for consistency
        return self.MainMenuView(self)

    async def start_command(self, interaction: discord.Interaction) -> None:
        """
        Handle the /start command.

        Args:
            interaction: The interaction object from Discord
        """
        user = interaction.user
        user_id = str(user.id)

        # Create user in database if they don't exist
        if not self.user_store.user_exists(user_id):
            profile = {
                "username": user.name,
                "display_name": user.display_name,
                "discriminator": user.discriminator if hasattr(user, 'discriminator') else None,
            }
            self.user_store.create_user(user_id, profile)
            if self.debug:
                self.logger.debug(f"Created new user: {user_id}")

        # Send welcome message
        welcome_text = (
            f"👋 Hello {user.display_name}! I'm your AI assistant bot.\n\n"
            f"I can help you with various tasks:\n"
            f"• 💬 General chat and questions\n"
            f"• 💻 Coding assistance\n"
            f"• 📊 Financial information\n"
            f"• 🔢 Mathematical problems\n"
            f"• 🔍 Web searches\n\n"
            f"Use /help to see all available commands."
        )

        # Use the helper method to create the main menu view
        view = self.create_main_menu_view()

        await interaction.response.send_message(welcome_text, view=view)

    async def link_command(self, interaction: discord.Interaction, token: str):
        """
        Handle the /link command.

        Args:
            interaction: The interaction object from Discord
            token: The linking token from the web UI
        """
        user = interaction.user
        discord_id = str(user.id)
        
        if not token:
            await interaction.response.send_message("Please provide a token with the `/link` command.", ephemeral=True)
            return

        success = self.user_store.link_account_by_token("discord", discord_id, token)
        
        if success:
            welcome_message = (
                f"🚀 Welcome aboard, {user.display_name}! Your account is now linked.\n\n"
                f"You can start chatting with me right away. Try saying 'hello'!\n"
                f"For a list of all commands, just type /help."
            )
            await interaction.response.send_message(welcome_message, ephemeral=True)
        else:
            await interaction.response.send_message("❌ Linking failed. The token might be invalid or expired.", ephemeral=True)


    async def help_command(self, interaction: discord.Interaction) -> None:
        """
        Handle the /help command.

        Args:
            interaction: The interaction object from Discord
        """
        help_text = "Here are the available commands:\n\n"
        for command, description in self.COMMANDS.items():
            help_text += f"/{command} - {description}\n"

        await interaction.response.send_message(help_text)

    class SettingsView(discord.ui.View):
        """Custom view for settings with dropdown menus and toggles."""

        def __init__(self, bot_instance, user_id):
            super().__init__(timeout=300)  # 5 minute timeout
            self.bot = bot_instance
            self.user_id = user_id

            # Add settings components
            self._add_web_search_toggle()
            self._add_model_selector()
            self._add_oauth_buttons()
            self._add_back_button()

        def _add_web_search_toggle(self):
            """Add web search toggle button."""
            web_search_enabled = self.bot.user_store.get_preference(self.user_id, "web_search_enabled", False)
            self.add_item(discord.ui.Button(
                label=f"Web Search: {'✅ ON' if web_search_enabled else '❌ OFF'}",
                custom_id=f"toggle_web_search_{not web_search_enabled}",
                style=discord.ButtonStyle.success if web_search_enabled else discord.ButtonStyle.danger,
                row=0
            ))

        def _add_model_selector(self):
            """Add model selector dropdown."""
            # Get available models and current selection
            available_models = self.bot.model_router.get_available_models()
            current_model = self.bot.user_store.get_preference(self.user_id, "selected_model", None)

            # Create select menu for models
            model_select = discord.ui.Select(
                placeholder="Select AI Model",
                custom_id="model_select",
                options=[
                    discord.SelectOption(
                        label=model,
                        value=model,
                        description=f"Use {model} for responses",
                        default=(model == current_model)
                    ) for model in available_models
                ],
                row=1
            )
            self.add_item(model_select)

        def _add_back_button(self):
            """Add back button."""
            self.add_item(discord.ui.Button(
                label="Back to Main Menu",
                custom_id="back_to_main",
                style=discord.ButtonStyle.secondary,
                row=2
            ))

    async def settings_command(self, interaction: discord.Interaction) -> None:
        """
        Handle the /settings command.

        Args:
            interaction: The interaction object from Discord
        """
        user_id = str(interaction.user.id)

        # Create enhanced settings view with dropdowns
        view = self.SettingsView(self, user_id)

        # Send settings message
        await interaction.response.send_message("⚙️ Settings:", view=view)

    async def web_command(self, interaction: discord.Interaction) -> None:
        """
        Handle the /web command to toggle web search on/off.

        Args:
            interaction: The interaction object from Discord
        """
        user_id = str(interaction.user.id)

        # Get current web search setting
        web_search_enabled = self.user_store.get_preference(user_id, "web_search_enabled", False)

        # Toggle web search
        new_setting = not web_search_enabled

        self.user_store.set_preference(user_id, "web_search_enabled", new_setting)

        # Inform the user
        status = "enabled" if new_setting else "disabled"
        await interaction.response.send_message(f"🌐 Web search is now {status}.")

    async def model_command(self, interaction: discord.Interaction) -> None:
        """
        Handle the /model command to select a specific AI model.

        Args:
            interaction: The interaction object from Discord
        """
        user_id = str(interaction.user.id)

        # Get available models
        available_models = self.model_router.get_available_models()

        # Get current model if set
        current_model = self.user_store.get_preference(user_id, "selected_model", None)

        # Create view with model options
        view = discord.ui.View()
        for model in available_models:
            button_text = f"✅ {model}" if model == current_model else model
            button = discord.ui.Button(label=button_text, custom_id=f"select_model_{model}")
            view.add_item(button)

        view.add_item(discord.ui.Button(label="Back to Main Menu", custom_id="back_to_main", style=discord.ButtonStyle.secondary))

        await interaction.response.send_message("Select a model:", view=view)

    async def _run_agent_async(self, agent_func, *args, **kwargs):
        """
        Run an agent function asynchronously.

        This helper method wraps synchronous agent calls to make them work with asyncio.create_task().

        Args:
            agent_func: The agent function to call
            *args: Positional arguments to pass to the agent function
            **kwargs: Keyword arguments to pass to the agent function

        Returns:
            The result from the agent function
        """
        # This async function should always be called from a context where an event loop is running.
        loop = asyncio.get_running_loop()

        # Use a loop executor to run the synchronous agent function without blocking
        return await loop.run_in_executor(
            None,
            lambda: agent_func(*args, **kwargs)
        )

    async def set_mode(self, interaction: discord.Interaction, mode: str) -> None:
        """
        Set the active mode for a user.

        Args:
            interaction: The interaction object from Discord
            mode: The mode to set (chat, code, finance, math, search)
        """
        user_id = str(interaction.user.id)

        # Update user state
        self.user_states[interaction.user.id] = {"mode": mode}

        # Also save to database for persistence
        self.user_store.set_preference(user_id, "active_mode", mode)

        await interaction.response.send_message(f"Mode set to {mode.capitalize()}. You can now send your questions directly.")

    async def on_message(self, message: discord.Message) -> None:
        """
        Handle incoming messages and route to appropriate agents.

        Args:
            message: The message object from Discord
        """
        # Ignore messages from the bot itself
        if message.author == self.client.user:
            return

        # Ignore commands (they're handled by the command system)
        if message.content.startswith('/'):
            return

        # Only ignore messages from the bot itself or other bots
        if message.author.bot:
            return

        # Remove mention from the message if present
        message_text = message.content
        is_mentioned = self.client.user in message.mentions
        if is_mentioned:
            message_text = message_text.replace(f'<@{self.client.user.id}>', '').strip()

        # Get user ID
        user_id = str(message.author.id)

        # Indicate typing
        async with message.channel.typing():
            # Get preferences
            web_search_enabled = self.user_store.get_preference(user_id, "web_search_enabled", False)
            selected_model = self.user_store.get_preference(user_id, "selected_model", None)
            active_mode = self.user_store.get_preference(user_id, "active_mode", "chat")

            if message.author.id in self.user_states:
                active_mode = self.user_states[message.author.id].get("mode", active_mode)

            web_search = {"enable": True} if web_search_enabled or active_mode == "search" else None

            response = "I couldn't process your request. Please try again."
            files_to_send = []

            try:
                if not web_search_enabled and active_mode != "search" and "search" in message_text.lower():
                    response = "It looks like you might be asking for information that requires web search. " \
                            "Web search is currently disabled. Use /web command to enable web search."

                elif active_mode == "chat":
                    # Create task for chat agent processing
                    response_task = asyncio.create_task(
                        self._run_agent_async(
                            self.chat_agent.run,
                            message_text,
                            web_search=web_search,
                            user_id=user_id,
                            update_history=False,
                            clear_context=True
                        )
                    )
                    response = await response_task

                elif active_mode == "code":
                    # Create task for coding agent processing
                    response_task = asyncio.create_task(
                        self._run_agent_async(
                            self.coding_agent.run,
                            message_text,
                            web_search=web_search,
                            user_id=user_id,
                            update_history=False,
                            clear_context=True
                        )
                    )
                    response = await response_task

                elif active_mode == "finance":
                    # Ensure we request HTML charts
                    finance_task = asyncio.create_task(
                        self._run_agent_async(
                            self.finance_agent.run,
                            message_text,
                            web_search=web_search,
                            platform="web",
                            user_id=user_id,
                            clear_context=True
                        )
                    )
                    finance_result = await finance_task
                    self.logger.debug(f"FinanceAgent returned: {finance_result}")
                    self.logger.debug(f"Active mode: {active_mode}, Message: {message_text}")

                    if not isinstance(finance_result, dict):
                        await message.reply("❌ Finance agent returned unexpected result.")
                        return

                    ticker = finance_result.get("ticker")
                    full_report = finance_result.get("insight", "").strip()
                    price = finance_result.get("price", "N/A")
                    recommendation = finance_result.get("recommendation", "N/A")

                    if not ticker or ticker == message_text:
                        await message.reply(
                            "❌ Failed to resolve a valid ticker symbol from your query. Please try again.")
                        return

                    if not full_report or full_report == "No analysis available.":
                        await message.reply("❌ No valid financial analysis was returned.")
                        return

                    response = full_report  # For chat history

                    # Compose summary message
                    summary_section = full_report.split("####")[1].strip() if "####" in full_report else full_report[:500]
                    summary_message = (
                        f"📈 **{ticker} Summary**\n"
                        f"Price: ${price}\n"
                        f"Recommendation: **{recommendation.capitalize()}**\n\n"
                        f"{summary_section[:1000]}...\n\n"
                        f"*Full analysis and chart are attached below.*"
                    )

                    # Create analysis text file
                    file_stream = io.BytesIO(full_report.encode('utf-8'))
                    files_to_send.append(discord.File(file_stream, filename=f"{ticker}_analysis.txt"))

                    # Create chart HTML file if available
                    chart_data = finance_result.get("chart")
                    chart_type = finance_result.get("chart_type")

                    if chart_type == "html" and chart_data:
                        html_bytes = chart_data.encode("utf-8")
                        html_stream = io.BytesIO(html_bytes)
                        files_to_send.append(discord.File(html_stream, filename=f"{ticker}_chart.html"))

                    # Send the summary and files
                    await message.reply(summary_message, files=files_to_send)

                    # Skip the normal response sending since we've already sent the message
                    files_to_send = None

                elif active_mode == "math":
                    # Create task for math agent processing
                    math_task = asyncio.create_task(
                        self._run_agent_async(
                            self.math_agent.run,
                            message_text,
                            web_search=web_search,
                            user_id=user_id,
                            update_history=False,
                            clear_context=True
                        )
                    )
                    response = await math_task

                elif active_mode == "search":
                    # Create task for web search agent processing
                    search_task = asyncio.create_task(
                        self._run_agent_async(
                            self.web_search_agent.run,
                            message_text,
                            user_id=user_id,
                            update_history=False,
                            clear_context=True
                        )
                    )
                    response = await search_task

            except Exception as e:
                self.logger.error(f"Error processing message: {str(e)}")
                response = "Sorry, I encountered an error while processing your request. Please try again later."

            # Save to chat history in MongoDB (primary storage)
            chat_history = self.user_store.get_chat_history(user_id)
            chat_history.append({"role": "user", "content": message_text})
            chat_history.append({"role": "assistant", "content": response})

            if len(chat_history) > 20:
                chat_history = chat_history[-20:]

            # Save to MongoDB
            self.user_store.save_chat_history(user_id, chat_history)

            # Save to JSON file (for unified chat history with Telegram bot)
            self.save_chat_history_to_json(user_id, chat_history)

            # Update unified history with metadata
            # Map active_mode to TaskType
            task_type_mapping = {
                "chat": TaskType.CHAT,
                "code": TaskType.CODING,
                "finance": TaskType.FINANCE,
                "math": TaskType.MATHS,
                "search": TaskType.CHAT  # Use CHAT for search as it's not a separate TaskType
            }
            task_type = task_type_mapping.get(active_mode, TaskType.CHAT)

            # Get the actual model used based on the task type
            actual_model = self.model_router.get_model_for_task(task_type)

            update_unified_history(
                user_id=user_id,
                platform="discord",
                message_text=message_text,
                response_text=response,
                model_used=actual_model,
                agent_used=active_mode,
                web_search_used=(web_search is not None)
            )

            # Create a backup to file system every 10 messages
            message_count = len(chat_history)
            if message_count % 10 == 0:
                asyncio.create_task(self.backup_chat_history(user_id, chat_history))

            # Send the response if we haven't already
            if files_to_send is not None:
                await self.send_long_message(message, response)

    async def send_long_message(self, message: discord.Message, text: str) -> None:
        """
        Send a message that might be longer than Discord's message length limit.
        Splits the message into smaller chunks if necessary.

        Args:
            message: The message object from Discord
            text: The text message to send
        """
        if not text or not text.strip():
            await message.reply("⚠️ Sorry, I didn't get a meaningful response.")
            return

        DISCORD_LIMIT = 2000

        # Define a template for headers and footers to calculate their maximum possible length.
        max_header = "(Continued 99/99)..."
        footer = "(Continued in next message...)"
        separators = "\n\n"

        # Calculate the maximum overhead for a message part that is in the middle of a sequence
        max_overhead = len(max_header) + len(separators) + len(footer)

        # Determine a safe chunk size by subtracting the max overhead from the Discord limit.
        CHUNK_SIZE = DISCORD_LIMIT - max_overhead

        # Split the text into chunks of the calculated safe size.
        chunks = [text[i:i + CHUNK_SIZE] for i in range(0, len(text), CHUNK_SIZE)]
        num_chunks = len(chunks)

        # If the text fits into a single chunk, send it as one message.
        if num_chunks == 1:
            await message.reply(text)
            return

        # Iterate through the chunks and send them as separate messages.
        for i, chunk in enumerate(chunks):
            message_parts = []

            # Add a header to all chunks except the first one.
            if i > 0:
                message_parts.append(f"(Continued {i + 1}/{num_chunks})...")

            # Add the text chunk.
            message_parts.append(chunk)

            # Add a footer to all chunks except the last one.
            if i < num_chunks - 1:
                message_parts.append("(Continued in next message...)")

            # Join the parts into a single message string.
            response = "\n\n".join(message_parts)

            # As a final safeguard, truncate the message if it somehow still exceeds the limit.
            if len(response) > DISCORD_LIMIT:
                response = response[:DISCORD_LIMIT - 4] + "..."

            if i == 0:
                # Reply to the original message for the first chunk
                sent_message = await message.reply(response)
            else:
                # Send follow-up messages as regular messages in the same channel
                sent_message = await message.channel.send(response)

            # Add a small delay between messages to ensure they are delivered in the correct order.
            if i < num_chunks - 1:
                await asyncio.sleep(0.5)

    def save_chat_history_to_json(self, user_id: str, chat_history: List[Dict[str, Any]]) -> None:
        """
        Save chat history to a JSON file.

        Args:
            user_id: Unique identifier for the user.
            chat_history: List of message dictionaries.
        """
        try:
            # Create data directory if it doesn't exist
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
            os.makedirs(data_dir, exist_ok=True)

            # Create user-specific JSON file
            file_path = os.path.join(data_dir, f'chat_history_{user_id}.json')

            # Write chat history to JSON file with pretty formatting
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(chat_history, f, indent=4, ensure_ascii=False)

            if self.debug:
                self.logger.debug(f"Saved chat history to JSON file for user {user_id}")
        except Exception as e:
            self.logger.error(f"Error saving chat history to JSON file for user {user_id}: {str(e)}")

    async def backup_chat_history(self, user_id: str, chat_history: List[Dict[str, Any]]) -> None:
        """
        Backup chat history to a JSON file asynchronously.

        This is used for periodic backups rather than primary storage.

        Args:
            user_id: Unique identifier for the user.
            chat_history: List of message dictionaries.
        """
        try:
            # Create data directory if it doesn't exist
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
            os.makedirs(data_dir, exist_ok=True)

            # Create user-specific JSON file with timestamp
            timestamp = int(time.time())
            file_path = os.path.join(data_dir, f'discord_chat_backup_{user_id}_{timestamp}.json')

            # Use async file operations to avoid blocking
            try:
                # Try to get the running loop
                loop = asyncio.get_running_loop()
            except RuntimeError:
                # If no running loop, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Prepare the JSON data
            json_data = json.dumps(chat_history, indent=4, ensure_ascii=False)

            # Write to file using run_in_executor to make it non-blocking
            await loop.run_in_executor(
                None,
                lambda: self._write_backup_file(file_path, json_data, user_id)
            )

            if self.debug:
                self.logger.debug(f"Created backup of chat history for user {user_id}")
        except Exception as e:
            self.logger.error(f"Error backing up chat history for user {user_id}: {str(e)}")

    def _write_backup_file(self, file_path: str, json_data: str, user_id: str) -> None:
        """
        Helper method to write backup data to a file.

        Args:
            file_path: Path to the backup file
            json_data: JSON string to write
            user_id: User ID for logging purposes
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(json_data)
        except Exception as e:
            self.logger.error(f"Error writing backup file for user {user_id}: {str(e)}")

    class MainMenuView(discord.ui.View):
        """Persistent view for the main menu."""

        def __init__(self, bot_instance):
            super().__init__(timeout=None)
            self.bot = bot_instance
            self.add_item(self.ChatButton(self.bot))
            self.add_item(self.CodeButton(self.bot))
            self.add_item(self.FinanceButton(self.bot))
            self.add_item(self.MathButton(self.bot))
            self.add_item(self.SearchButton(self.bot))
            self.add_item(self.SettingsButton(self.bot))

        class ChatButton(discord.ui.Button):
            def __init__(self, bot):
                super().__init__(label="💬 Chat", custom_id="mode_chat", style=discord.ButtonStyle.primary, row=0)
                self.bot = bot

            async def callback(self, interaction: discord.Interaction):
                await self.bot.set_mode(interaction, "chat")

        class CodeButton(discord.ui.Button):
            def __init__(self, bot):
                super().__init__(label="💻 Code", custom_id="mode_code", style=discord.ButtonStyle.primary, row=0)
                self.bot = bot

            async def callback(self, interaction: discord.Interaction):
                await self.bot.set_mode(interaction, "code")

        class FinanceButton(discord.ui.Button):
            def __init__(self, bot):
                super().__init__(label="📊 Finance", custom_id="mode_finance", style=discord.ButtonStyle.primary, row=1)
                self.bot = bot

            async def callback(self, interaction: discord.Interaction):
                await self.bot.set_mode(interaction, "finance")

        class MathButton(discord.ui.Button):
            def __init__(self, bot):
                super().__init__(label="🔢 Math", custom_id="mode_math", style=discord.ButtonStyle.primary, row=1)
                self.bot = bot

            async def callback(self, interaction: discord.Interaction):
                await self.bot.set_mode(interaction, "math")

        class SearchButton(discord.ui.Button):
            def __init__(self, bot):
                super().__init__(label="🔍 Web Search", custom_id="mode_search", style=discord.ButtonStyle.primary,
                                 row=2)
                self.bot = bot

            async def callback(self, interaction: discord.Interaction):
                await self.bot.set_mode(interaction, "search")

        class SettingsButton(discord.ui.Button):
            def __init__(self, bot):
                super().__init__(label="⚙️ Settings", custom_id="settings", style=discord.ButtonStyle.secondary, row=2)
                self.bot = bot

            async def callback(self, interaction: discord.Interaction):
                await self.bot.settings_command(interaction)

    async def handle_interaction(self, interaction: discord.Interaction) -> None:
        """
        Handle all interactions from Discord UI components (buttons and select menus).

        Args:
            interaction: The interaction object from Discord
        """
        user_id = str(interaction.user.id)

        # Handle different interaction types
        if interaction.type == discord.InteractionType.component:
            component_type = interaction.data.get("component_type", 0)
            custom_id = interaction.data.get("custom_id", "")

            # Button interactions (type 2)
            if component_type == 2:
                await self._handle_button_interaction(interaction, user_id, custom_id)

            # Select menu interactions (type 3)
            elif component_type == 3:
                await self._handle_select_interaction(interaction, user_id, custom_id)

    async def _handle_button_interaction(self, interaction, user_id, custom_id):
        """Handle button interactions."""
        # Mode selection
        if custom_id.startswith("mode_"):
            mode = custom_id.split("_")[1]
            self.user_states[interaction.user.id] = {"mode": mode}
            self.user_store.set_preference(user_id, "active_mode", mode)
            await interaction.response.send_message(f"Mode set to {mode.capitalize()}.")

        # Settings button
        elif custom_id == "settings":
            # Show settings view
            await self.settings_command(interaction)

        # Settings toggle
        elif custom_id.startswith("toggle_web_search_"):
            new_setting_str = custom_id.split("_")[-1]
            new_setting = new_setting_str.lower() == "true"

            self.user_store.set_preference(user_id, "web_search_enabled", new_setting)
            await interaction.response.send_message(f"Web search {'enabled' if new_setting else 'disabled'}.")

            # To show the updated settings view
            await self.settings_command(interaction)

        # Back to main menu
        elif custom_id == "back_to_main":
            # Use the helper method to create the main menu view
            view = self.create_main_menu_view()
            await interaction.response.send_message("Select an option:", view=view)

        # Other button types can be handled here
        else:
            # Model selection buttons (legacy support)
            if custom_id.startswith("select_model_"):
                model = custom_id.split("_")[-1]
                self.user_store.set_preference(user_id, "selected_model", model)
                await interaction.response.send_message(f"Model set to {model}.")

    async def _handle_select_interaction(self, interaction, user_id, custom_id):
        """Handle select menu interactions."""
        # Get the selected values
        values = interaction.data.get("values", [])
        if not values:
            return

        selected_value = values[0]

        # Model selection dropdown
        if custom_id == "model_select":
            self.user_store.set_preference(user_id, "selected_model", selected_value)
            await interaction.response.send_message(f"Model set to {selected_value}.")

        # Other select menu types can be handled here

    async def run(self):
        """Run the bot asynchronously."""
        async with self.client:
            await self.client.start(self.token)

    async def on_interaction(self, interaction: discord.Interaction):
        await self.handle_interaction(interaction)


def validate_environment():
    """
    Validate that all required environment variables are set.

    Returns:
        dict: A dictionary containing validated environment variables

    Raises:
        ValueError: If any required environment variables are missing
    """
    # Define required environment variables
    required_vars = {
        "DISCORD_BOT_TOKEN": "Discord bot token is required for bot operation",
    }

    # Define optional environment variables with their descriptions
    optional_vars = {
        "MONGODB_URI": "MongoDB connection URI (will use localhost if not provided)",
        "MONGO_URI": "Alternative MongoDB connection URI (will use localhost if not provided)",
        "OPENAI_API_KEY": "OpenAI API key for AI model access",
        "SERPER_API_KEY": "Serper API key for web search functionality",
        "TAVILY_API_KEY": "Tavily API key for enhanced search capabilities",
        "BINANCE_API_KEY": "Binance API key for finance agent functionality",
        "BINANCE_API_SECRET": "Binance API secret for finance agent functionality",
    }

    # Collect environment variables
    env_vars = {}
    missing_vars = []

    # Check required variables
    for var_name, description in required_vars.items():
        value = os.environ.get(var_name)
        if not value:
            missing_vars.append(f"{var_name}: {description}")
        else:
            env_vars[var_name] = value

    # Raise error if any required variables are missing
    if missing_vars:
        raise ValueError("Missing required environment variables:\n" + "\n".join(missing_vars))

    # Collect optional variables (without raising error)
    for var_name, description in optional_vars.items():
        value = os.environ.get(var_name)
        if value:
            env_vars[var_name] = value

    return env_vars

    # ✅ Main block starts here — at global level
if __name__ == "__main__":
    env_vars = validate_environment()
    token = env_vars["DISCORD_BOT_TOKEN"]

    bot = DiscordBot(
        token=token,
        mongodb_uri=env_vars.get("MONGODB_URI") or env_vars.get("MONGO_URI"),
        debug=True,
        dev_mode=False,
        dev_guild_id=None  # Optional: set your test server ID here if needed
    )

    asyncio.run(bot.run())
