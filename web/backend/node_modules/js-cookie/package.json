{"name": "js-cookie", "version": "3.0.1", "description": "A simple, lightweight JavaScript API for handling cookies", "browser": "dist/js.cookie.js", "module": "dist/js.cookie.mjs", "unpkg": "dist/js.cookie.min.js", "jsdelivr": "dist/js.cookie.min.js", "exports": {".": {"import": "./dist/js.cookie.mjs", "require": "./dist/js.cookie.js"}, "./package.json": "./package.json"}, "directories": {"test": "test"}, "keywords": ["cookie", "cookies", "browser", "amd", "commonjs", "client", "js-cookie", "browserify"], "scripts": {"test": "grunt test", "format": "grunt exec:format", "dist": "rm -rf dist/* && rollup -c", "release": "release-it"}, "repository": {"type": "git", "url": "git://github.com/js-cookie/js-cookie.git"}, "files": ["index.js", "dist/**/*"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"browserstack-runner": "^0.9.0", "eslint": "^7.31.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-html": "^6.0.0", "eslint-plugin-markdown": "^2.2.0", "grunt": "^1.0.4", "grunt-compare-size": "^0.4.2", "grunt-contrib-connect": "^3.0.0", "grunt-contrib-nodeunit": "^3.0.0", "grunt-contrib-qunit": "^3.1.0", "grunt-contrib-watch": "^1.1.0", "grunt-exec": "^3.0.0", "gzip-js": "^0.3.2", "prettier": "^2.3.2", "qunit": "^2.9.3", "release-it": "^14.10.0", "rollup": "^2.0.0", "rollup-plugin-filesize": "^9.1.1", "rollup-plugin-license": "^2.5.0", "rollup-plugin-terser": "^7.0.2", "standard": "^16.0.3"}, "engines": {"node": ">=12"}}