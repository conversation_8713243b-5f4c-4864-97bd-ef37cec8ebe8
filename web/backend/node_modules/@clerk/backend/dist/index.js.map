{"version": 3, "sources": ["../src/index.ts", "../src/api/endpoints/AbstractApi.ts", "../src/util/path.ts", "../src/api/endpoints/AllowlistIdentifierApi.ts", "../src/api/endpoints/ClientApi.ts", "../src/api/endpoints/DomainApi.ts", "../src/api/endpoints/EmailAddressApi.ts", "../src/api/endpoints/EmailApi.ts", "../src/util/shared.ts", "../src/api/endpoints/InterstitialApi.ts", "../src/api/endpoints/InvitationApi.ts", "../src/api/endpoints/OrganizationApi.ts", "../src/runtime/index.ts", "../src/api/endpoints/PhoneNumberApi.ts", "../src/api/endpoints/RedirectUrlApi.ts", "../src/api/endpoints/SessionApi.ts", "../src/api/endpoints/SignInTokenApi.ts", "../src/api/endpoints/SMSMessageApi.ts", "../src/api/endpoints/UserApi.ts", "../src/api/request.ts", "../src/constants.ts", "../src/util/assertValidSecretKey.ts", "../src/api/resources/AllowlistIdentifier.ts", "../src/api/resources/Session.ts", "../src/api/resources/Client.ts", "../src/api/resources/DeletedObject.ts", "../src/api/resources/Email.ts", "../src/api/resources/IdentificationLink.ts", "../src/api/resources/Verification.ts", "../src/api/resources/EmailAddress.ts", "../src/api/resources/ExternalAccount.ts", "../src/api/resources/Invitation.ts", "../src/api/resources/JSON.ts", "../src/api/resources/OauthAccessToken.ts", "../src/api/resources/Organization.ts", "../src/api/resources/OrganizationInvitation.ts", "../src/api/resources/OrganizationMembership.ts", "../src/api/resources/PhoneNumber.ts", "../src/api/resources/RedirectUrl.ts", "../src/api/resources/SignInTokens.ts", "../src/api/resources/SMSMessage.ts", "../src/api/resources/Token.ts", "../src/api/resources/Web3Wallet.ts", "../src/api/resources/User.ts", "../src/api/resources/Deserializer.ts", "../src/api/factory.ts", "../src/tokens/authObjects.ts", "../src/tokens/errors.ts", "../src/tokens/interstitial.ts", "../src/util/IsomorphicRequest.ts", "../src/utils.ts", "../src/tokens/authStatus.ts", "../src/util/request.ts", "../src/tokens/jwt/verifyJwt.ts", "../src/util/rfc4648.ts", "../src/tokens/jwt/algorithms.ts", "../src/tokens/jwt/assertions.ts", "../src/tokens/jwt/cryptoKeys.ts", "../src/tokens/jwt/signJwt.ts", "../src/tokens/keys.ts", "../src/tokens/verify.ts", "../src/tokens/interstitialRule.ts", "../src/tokens/request.ts", "../src/tokens/factory.ts", "../src/redirections.ts"], "sourcesContent": ["import { deprecatedObjectProperty } from '@clerk/shared/deprecated';\n\nimport type { CreateBackendApiOptions } from './api';\nimport { createBackendApiClient } from './api';\nimport type { CreateAuthenticateRequestOptions } from './tokens';\nimport { createAuthenticateRequest } from './tokens';\n\nexport { createIsomorphicRequest } from './util/IsomorphicRequest';\n\nexport * from './api/resources';\nexport * from './tokens';\nexport * from './tokens/jwt';\nexport * from './tokens/verify';\nexport { constants } from './constants';\nexport { redirect } from './redirections';\nexport { buildRequestUrl } from './utils';\n\nexport type ClerkOptions = CreateBackendApiOptions &\n  Partial<\n    Pick<\n      CreateAuthenticateRequestOptions['options'],\n      'audience' | 'jwtKey' | 'proxyUrl' | 'secretKey' | 'publishableKey' | 'domain' | 'isSatellite'\n    >\n  >;\n\nexport function Clerk(options: ClerkOptions) {\n  const opts = { ...options };\n  const apiClient = createBackendApiClient(opts);\n  const requestState = createAuthenticateRequest({ options: opts, apiClient });\n\n  const clerkInstance = {\n    ...apiClient,\n    ...requestState,\n    /**\n     * @deprecated This prop has been deprecated and will be removed in the next major release.\n     */\n    __unstable_options: opts,\n  };\n\n  // The __unstable_options is not being used internally and\n  // it's only being set in packages/sdk-node/src/clerkClient.ts#L86\n  deprecatedObjectProperty(\n    clerkInstance,\n    '__unstable_options',\n    'Use `createClerkClient({...})` to create a new clerk instance instead.',\n  );\n\n  return clerkInstance;\n}\n", "import type { RequestFunction } from '../request';\n\nexport abstract class AbstractAPI {\n  constructor(protected request: RequestFunction) {}\n\n  protected requireId(id: string) {\n    if (!id) {\n      throw new Error('A valid resource ID is required.');\n    }\n  }\n}\n", "const SEPARATOR = '/';\nconst MULTIPLE_SEPARATOR_REGEX = new RegExp('(?<!:)' + SEPARATOR + '{1,}', 'g');\n\ntype PathString = string | null | undefined;\n\nexport function joinPaths(...args: PathString[]): string {\n  return args\n    .filter(p => p)\n    .join(SEPARATOR)\n    .replace(MULTIPLE_SEPARATOR_REGEX, SEPARATOR);\n}\n", "import { joinPaths } from '../../util/path';\nimport type { AllowlistIdentifier } from '../resources/AllowlistIdentifier';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/allowlist_identifiers';\n\ntype AllowlistIdentifierCreateParams = {\n  identifier: string;\n  notify: boolean;\n};\n\nexport class AllowlistIdentifierAPI extends AbstractAPI {\n  public async getAllowlistIdentifierList() {\n    return this.request<AllowlistIdentifier[]>({\n      method: 'GET',\n      path: basePath,\n    });\n  }\n\n  public async createAllowlistIdentifier(params: AllowlistIdentifierCreateParams) {\n    return this.request<AllowlistIdentifier>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async deleteAllowlistIdentifier(allowlistIdentifierId: string) {\n    this.requireId(allowlistIdentifierId);\n    return this.request<AllowlistIdentifier>({\n      method: 'DELETE',\n      path: joinPaths(basePath, allowlistIdentifierId),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { Client } from '../resources/Client';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/clients';\n\nexport class ClientAPI extends AbstractAPI {\n  public async getClientList() {\n    return this.request<Client[]>({\n      method: 'GET',\n      path: basePath,\n    });\n  }\n\n  public async getClient(clientId: string) {\n    this.requireId(clientId);\n    return this.request<Client>({\n      method: 'GET',\n      path: joinPaths(basePath, clientId),\n    });\n  }\n\n  public verifyClient(token: string) {\n    return this.request<Client>({\n      method: 'POST',\n      path: joinPaths(basePath, 'verify'),\n      bodyParams: { token },\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { DeletedObject } from '../resources/DeletedObject';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/domains';\n\nexport class DomainAPI extends AbstractAPI {\n  public async deleteDomain(id: string) {\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, id),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { DeletedObject, EmailAddress } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/email_addresses';\n\ntype CreateEmailAddressParams = {\n  userId: string;\n  emailAddress: string;\n  verified?: boolean;\n  primary?: boolean;\n};\n\ntype UpdateEmailAddressParams = {\n  verified?: boolean;\n  primary?: boolean;\n};\n\nexport class EmailAddressAPI extends AbstractAPI {\n  public async getEmailAddress(emailAddressId: string) {\n    this.requireId(emailAddressId);\n\n    return this.request<EmailAddress>({\n      method: 'GET',\n      path: joinPaths(basePath, emailAddressId),\n    });\n  }\n\n  public async createEmailAddress(params: CreateEmailAddressParams) {\n    return this.request<EmailAddress>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async updateEmailAddress(emailAddressId: string, params: UpdateEmailAddressParams = {}) {\n    this.requireId(emailAddressId);\n\n    return this.request<EmailAddress>({\n      method: 'PATCH',\n      path: joinPaths(basePath, emailAddressId),\n      bodyParams: params,\n    });\n  }\n\n  public async deleteEmailAddress(emailAddressId: string) {\n    this.requireId(emailAddressId);\n\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, emailAddressId),\n    });\n  }\n}\n", "import { deprecated } from '@clerk/shared/deprecated';\n\nimport type { Email } from '../resources/Email';\nimport { AbstractAPI } from './AbstractApi';\n\ntype EmailParams = {\n  fromEmailName: string;\n  emailAddressId: string;\n  subject: string;\n  body: string;\n};\n\nconst basePath = '/emails';\n\n/**\n * @deprecated This endpoint is no longer available and the function will be removed in the next major version.\n */\nexport class EmailAPI extends AbstractAPI {\n  /**\n   * @deprecated This endpoint is no longer available and the function will be removed in the next major version.\n   */\n  public async createEmail(params: EmailParams) {\n    deprecated(\n      'EmailAPI.createEmail',\n      'This endpoint is no longer available and the function will be removed in the next major version.',\n    );\n    return this.request<Email>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n}\n", "export { addClerkPrefix, getScriptUrl, getClerkJsMajorVersionOrTag } from '@clerk/shared/url';\nexport { callWithRetry } from '@clerk/shared/callWithRetry';\nexport { isDevelopmentFromApiKey, isProductionFromApiKey, parsePublishableKey } from '@clerk/shared/keys';\nexport { deprecated, deprecatedProperty } from '@clerk/shared/deprecated';\n\nimport { buildErrorThrower } from '@clerk/shared/error';\n// TODO: replace packageName with `${PACKAGE_NAME}@${PACKAGE_VERSION}` from tsup.config.ts\nexport const errorThrower = buildErrorThrower({ packageName: '@clerk/backend' });\n\nimport { createDevOrStagingUrlCache } from '@clerk/shared/keys';\nexport const { isDevOrStagingUrl } = createDevOrStagingUrlCache();\n", "import { deprecated } from '../../util/shared';\nimport { AbstractAPI } from './AbstractApi';\n/**\n * @deprecated Switch to the public interstitial endpoint from Clerk Backend API.\n */\nexport class InterstitialAPI extends AbstractAPI {\n  public async getInterstitial() {\n    deprecated(\n      'getInterstitial()',\n      'Switch to `Clerk(...).localInterstitial(...)` from `import { Clerk } from \"@clerk/backend\"`.',\n    );\n\n    return this.request<string>({\n      path: 'internal/interstitial',\n      method: 'GET',\n      headerParams: {\n        'Content-Type': 'text/html',\n      },\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { Invitation } from '../resources/Invitation';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/invitations';\n\ntype CreateParams = {\n  emailAddress: string;\n  redirectUrl?: string;\n  publicMetadata?: UserPublicMetadata;\n  notify?: boolean;\n  ignoreExisting?: boolean;\n};\n\ntype GetInvitationListParams = {\n  /**\n   * Filters invitations based on their status(accepted, pending, revoked).\n   *\n   * @example\n   * get all revoked invitations\n   *\n   * import Clerk from '@clerk/backend';\n   * const clerkClient = Clerk(...)\n   * await clerkClient.invitations.getInvitationList({ status: 'revoked })\n   *\n   */\n  status?: 'accepted' | 'pending' | 'revoked';\n};\n\nexport class InvitationAPI extends AbstractAPI {\n  public async getInvitationList(params: GetInvitationListParams = {}) {\n    return this.request<Invitation[]>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async createInvitation(params: CreateParams) {\n    return this.request<Invitation>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async revokeInvitation(invitationId: string) {\n    this.requireId(invitationId);\n    return this.request<Invitation>({\n      method: 'POST',\n      path: joinPaths(basePath, invitationId, 'revoke'),\n    });\n  }\n}\n", "import { deprecated } from '@clerk/shared/deprecated';\n\nimport runtime from '../../runtime';\nimport { joinPaths } from '../../util/path';\nimport type {\n  Organization,\n  OrganizationInvitation,\n  OrganizationInvitationStatus,\n  OrganizationMembership,\n} from '../resources';\nimport type { OrganizationMembershipRole } from '../resources/Enums';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/organizations';\n\ntype MetadataParams<TPublic = OrganizationPublicMetadata, TPrivate = OrganizationPrivateMetadata> = {\n  publicMetadata?: TPublic;\n  privateMetadata?: TPrivate;\n};\n\ntype GetOrganizationListParams = {\n  limit?: number;\n  offset?: number;\n  includeMembersCount?: boolean;\n  query?: string;\n};\n\ntype CreateParams = {\n  name: string;\n  slug?: string;\n  /* The User id for the user creating the organization. The user will become an administrator for the organization. */\n  createdBy: string;\n  maxAllowedMemberships?: number;\n} & MetadataParams;\n\ntype GetOrganizationParams = { organizationId: string } | { slug: string };\n\ntype UpdateParams = {\n  name?: string;\n  slug?: string;\n  maxAllowedMemberships?: number;\n} & MetadataParams;\n\ntype UpdateLogoParams = {\n  file: Blob | File;\n  uploaderUserId: string;\n};\n\ntype UpdateMetadataParams = MetadataParams;\n\ntype GetOrganizationMembershipListParams = {\n  organizationId: string;\n  limit?: number;\n  offset?: number;\n};\n\ntype CreateOrganizationMembershipParams = {\n  organizationId: string;\n  userId: string;\n  role: OrganizationMembershipRole;\n};\n\ntype UpdateOrganizationMembershipParams = CreateOrganizationMembershipParams;\n\ntype UpdateOrganizationMembershipMetadataParams = {\n  organizationId: string;\n  userId: string;\n} & MetadataParams<OrganizationMembershipPublicMetadata>;\n\ntype DeleteOrganizationMembershipParams = {\n  organizationId: string;\n  userId: string;\n};\n\ntype CreateOrganizationInvitationParams = {\n  organizationId: string;\n  inviterUserId: string;\n  emailAddress: string;\n  role: OrganizationMembershipRole;\n  redirectUrl?: string;\n  publicMetadata?: OrganizationInvitationPublicMetadata;\n};\n\ntype GetOrganizationInvitationListParams = {\n  organizationId: string;\n  status?: OrganizationInvitationStatus[];\n  limit?: number;\n  offset?: number;\n};\n\ntype GetPendingOrganizationInvitationListParams = {\n  organizationId: string;\n  limit?: number;\n  offset?: number;\n};\n\ntype GetOrganizationInvitationParams = {\n  organizationId: string;\n  invitationId: string;\n};\n\ntype RevokeOrganizationInvitationParams = {\n  organizationId: string;\n  invitationId: string;\n  requestingUserId: string;\n};\n\nexport class OrganizationAPI extends AbstractAPI {\n  public async getOrganizationList(params?: GetOrganizationListParams) {\n    return this.request<Organization[]>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async createOrganization(params: CreateParams) {\n    return this.request<Organization>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async getOrganization(params: GetOrganizationParams) {\n    const organizationIdOrSlug = 'organizationId' in params ? params.organizationId : params.slug;\n    this.requireId(organizationIdOrSlug);\n\n    return this.request<Organization>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationIdOrSlug),\n    });\n  }\n\n  public async updateOrganization(organizationId: string, params: UpdateParams) {\n    this.requireId(organizationId);\n    return this.request<Organization>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId),\n      bodyParams: params,\n    });\n  }\n\n  public async updateOrganizationLogo(organizationId: string, params: UpdateLogoParams) {\n    this.requireId(organizationId);\n\n    const formData = new runtime.FormData();\n    formData.append('file', params?.file);\n    formData.append('uploader_user_id', params?.uploaderUserId);\n\n    return this.request<Organization>({\n      method: 'PUT',\n      path: joinPaths(basePath, organizationId, 'logo'),\n      formData,\n    });\n  }\n\n  public async deleteOrganizationLogo(organizationId: string) {\n    this.requireId(organizationId);\n\n    return this.request<Organization>({\n      method: 'DELETE',\n      path: joinPaths(basePath, organizationId, 'logo'),\n    });\n  }\n\n  public async updateOrganizationMetadata(organizationId: string, params: UpdateMetadataParams) {\n    this.requireId(organizationId);\n\n    return this.request<Organization>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId, 'metadata'),\n      bodyParams: params,\n    });\n  }\n\n  public async deleteOrganization(organizationId: string) {\n    return this.request<Organization>({\n      method: 'DELETE',\n      path: joinPaths(basePath, organizationId),\n    });\n  }\n\n  public async getOrganizationMembershipList(params: GetOrganizationMembershipListParams) {\n    const { organizationId, limit, offset } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationMembership[]>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'memberships'),\n      queryParams: { limit, offset },\n    });\n  }\n\n  public async createOrganizationMembership(params: CreateOrganizationMembershipParams) {\n    const { organizationId, userId, role } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationMembership>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'memberships'),\n      bodyParams: {\n        userId,\n        role,\n      },\n    });\n  }\n\n  public async updateOrganizationMembership(params: UpdateOrganizationMembershipParams) {\n    const { organizationId, userId, role } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationMembership>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId, 'memberships', userId),\n      bodyParams: {\n        role,\n      },\n    });\n  }\n\n  public async updateOrganizationMembershipMetadata(params: UpdateOrganizationMembershipMetadataParams) {\n    const { organizationId, userId, publicMetadata, privateMetadata } = params;\n\n    return this.request<OrganizationMembership>({\n      method: 'PATCH',\n      path: joinPaths(basePath, organizationId, 'memberships', userId, 'metadata'),\n      bodyParams: {\n        publicMetadata,\n        privateMetadata,\n      },\n    });\n  }\n\n  public async deleteOrganizationMembership(params: DeleteOrganizationMembershipParams) {\n    const { organizationId, userId } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationMembership>({\n      method: 'DELETE',\n      path: joinPaths(basePath, organizationId, 'memberships', userId),\n    });\n  }\n\n  public async getOrganizationInvitationList(params: GetOrganizationInvitationListParams) {\n    const { organizationId, status, limit, offset } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationInvitation[]>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'invitations'),\n      queryParams: { status, limit, offset },\n    });\n  }\n\n  /**\n   * @deprecated  Use `getOrganizationInvitationList` instead along with the status parameter.\n   */\n  public async getPendingOrganizationInvitationList(params: GetPendingOrganizationInvitationListParams) {\n    deprecated('getPendingOrganizationInvitationList', 'Use `getOrganizationInvitationList` instead.');\n\n    const { organizationId, limit, offset } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationInvitation[]>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'invitations', 'pending'),\n      queryParams: { limit, offset },\n    });\n  }\n\n  public async createOrganizationInvitation(params: CreateOrganizationInvitationParams) {\n    const { organizationId, ...bodyParams } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationInvitation>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'invitations'),\n      bodyParams: { ...bodyParams },\n    });\n  }\n\n  public async getOrganizationInvitation(params: GetOrganizationInvitationParams) {\n    const { organizationId, invitationId } = params;\n    this.requireId(organizationId);\n    this.requireId(invitationId);\n\n    return this.request<OrganizationInvitation>({\n      method: 'GET',\n      path: joinPaths(basePath, organizationId, 'invitations', invitationId),\n    });\n  }\n\n  public async revokeOrganizationInvitation(params: RevokeOrganizationInvitationParams) {\n    const { organizationId, invitationId, requestingUserId } = params;\n    this.requireId(organizationId);\n\n    return this.request<OrganizationInvitation>({\n      method: 'POST',\n      path: joinPaths(basePath, organizationId, 'invitations', invitationId, 'revoke'),\n      bodyParams: {\n        requestingUserId,\n      },\n    });\n  }\n}\n", "/**\n * This file exports APIs that vary across runtimes (i.e. Node & Browser - V8 isolates)\n * as a singleton object.\n *\n * Runtime polyfills are written in VanillaJS for now to avoid TS complication. Moreover,\n * due to this issue https://github.com/microsoft/TypeScript/issues/44848, there is not a good way\n * to tell Typescript which conditional import to use during build type.\n *\n * The Runtime type definition ensures type safety for now.\n * Runtime js modules are copied into dist folder with bash script.\n *\n * TODO: Support TS runtime modules\n */\n\n// @ts-ignore - These are package subpaths\nimport crypto from '#crypto';\n// @ts-ignore - These are package subpaths\nimport * as fetchApisPolyfill from '#fetch';\n\nconst {\n  RuntimeFetch,\n  RuntimeAbortController,\n  RuntimeBlob,\n  RuntimeFormData,\n  RuntimeHeaders,\n  RuntimeRequest,\n  RuntimeResponse,\n} = fetchApisPolyfill;\n\ntype Runtime = {\n  crypto: Crypto;\n  fetch: typeof global.fetch;\n  AbortController: typeof global.AbortController;\n  Blob: typeof global.Blob;\n  FormData: typeof global.FormData;\n  Headers: typeof global.Headers;\n  Request: typeof global.Request;\n  Response: typeof global.Response;\n};\n\n// Invoking the global.fetch without binding it first to the globalObject fails in\n// Cloudflare Workers with an \"Illegal Invocation\" error.\n//\n// The globalThis object is supported for Node >= 12.0.\n//\n// https://github.com/supabase/supabase/issues/4417\nconst globalFetch = RuntimeFetch.bind(globalThis);\n// DO NOT CHANGE: Runtime needs to be imported as a default export so that we can stub its dependencies with Sinon.js\n// For more information refer to https://sinonjs.org/how-to/stub-dependency/\nconst runtime: Runtime = {\n  crypto,\n  fetch: globalFetch,\n  AbortController: RuntimeAbortController,\n  Blob: RuntimeBlob,\n  FormData: RuntimeFormData,\n  Headers: RuntimeHeaders,\n  Request: RuntimeRequest,\n  Response: RuntimeResponse,\n};\n\nexport default runtime;\n", "import { joinPaths } from '../../util/path';\nimport type { DeletedObject, PhoneNumber } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/phone_numbers';\n\ntype CreatePhoneNumberParams = {\n  userId: string;\n  phoneNumber: string;\n  verified?: boolean;\n  primary?: boolean;\n};\n\ntype UpdatePhoneNumberParams = {\n  verified?: boolean;\n  primary?: boolean;\n};\n\nexport class PhoneNumberAPI extends AbstractAPI {\n  public async getPhoneNumber(phoneNumberId: string) {\n    this.requireId(phoneNumberId);\n\n    return this.request<PhoneNumber>({\n      method: 'GET',\n      path: joinPaths(basePath, phoneNumberId),\n    });\n  }\n\n  public async createPhoneNumber(params: CreatePhoneNumberParams) {\n    return this.request<PhoneNumber>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async updatePhoneNumber(phoneNumberId: string, params: UpdatePhoneNumberParams = {}) {\n    this.requireId(phoneNumberId);\n\n    return this.request<PhoneNumber>({\n      method: 'PATCH',\n      path: joinPaths(basePath, phoneNumberId),\n      bodyParams: params,\n    });\n  }\n\n  public async deletePhoneNumber(phoneNumberId: string) {\n    this.requireId(phoneNumberId);\n\n    return this.request<DeletedObject>({\n      method: 'DELETE',\n      path: joinPaths(basePath, phoneNumberId),\n    });\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { RedirectUrl } from '../resources/RedirectUrl';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/redirect_urls';\n\ntype CreateRedirectUrlParams = {\n  url: string;\n};\n\nexport class RedirectUrl<PERSON>I extends AbstractAPI {\n  public async getRedirectUrlList() {\n    return this.request<RedirectUrl[]>({\n      method: 'GET',\n      path: basePath,\n    });\n  }\n\n  public async getRedirectUrl(redirectUrlId: string) {\n    this.requireId(redirectUrlId);\n    return this.request<RedirectUrl>({\n      method: 'GET',\n      path: joinPaths(basePath, redirectUrlId),\n    });\n  }\n\n  public async createRedirectUrl(params: CreateRedirectUrlParams) {\n    return this.request<RedirectUrl>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async deleteRedirectUrl(redirectUrlId: string) {\n    this.requireId(redirectUrlId);\n    return this.request<RedirectUrl>({\n      method: 'DELETE',\n      path: joinPaths(basePath, redirectUrlId),\n    });\n  }\n}\n", "import type { SessionStatus } from '@clerk/types';\n\nimport { joinPaths } from '../../util/path';\nimport type { Session } from '../resources/Session';\nimport type { Token } from '../resources/Token';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/sessions';\n\ntype QueryParams = {\n  clientId?: string;\n  userId?: string;\n  status?: SessionStatus;\n};\n\nexport class SessionAPI extends AbstractAPI {\n  public async getSessionList(queryParams?: QueryParams) {\n    return this.request<Session[]>({\n      method: 'GET',\n      path: basePath,\n      queryParams: queryParams,\n    });\n  }\n\n  public async getSession(sessionId: string) {\n    this.requireId(sessionId);\n    return this.request<Session>({\n      method: 'GET',\n      path: joinPaths(basePath, sessionId),\n    });\n  }\n\n  public async revokeSession(sessionId: string) {\n    this.requireId(sessionId);\n    return this.request<Session>({\n      method: 'POST',\n      path: joinPaths(basePath, sessionId, 'revoke'),\n    });\n  }\n\n  public async verifySession(sessionId: string, token: string) {\n    this.requireId(sessionId);\n    return this.request<Session>({\n      method: 'POST',\n      path: joinPaths(basePath, sessionId, 'verify'),\n      bodyParams: { token },\n    });\n  }\n\n  public async getToken(sessionId: string, template: string) {\n    this.requireId(sessionId);\n    return (\n      (await this.request<Token>({\n        method: 'POST',\n        path: joinPaths(basePath, sessionId, 'tokens', template || ''),\n      })) as any\n    ).jwt;\n  }\n}\n", "import { joinPaths } from '../../util/path';\nimport type { SignInToken } from '../resources/SignInTokens';\nimport { AbstractAPI } from './AbstractApi';\n\ntype CreateSignInTokensParams = {\n  userId: string;\n  expiresInSeconds: number;\n};\n\nconst basePath = '/sign_in_tokens';\n\nexport class SignInTokenAPI extends AbstractAPI {\n  public async createSignInToken(params: CreateSignInTokensParams) {\n    return this.request<SignInToken>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async revokeSignInToken(signInTokenId: string) {\n    this.requireId(signInTokenId);\n    return this.request<SignInToken>({\n      method: 'POST',\n      path: joinPaths(basePath, signInTokenId, 'revoke'),\n    });\n  }\n}\n", "import { deprecated } from '@clerk/shared/deprecated';\n\nimport type { SMSMessage } from '../resources/SMSMessage';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/sms_messages';\n\ntype SMSParams = {\n  phoneNumberId: string;\n  message: string;\n};\n\n/**\n * @deprecated This endpoint is no longer available and the function will be removed in the next major version.\n */\nexport class SMSMessageAPI extends AbstractAPI {\n  /**\n   * @deprecated This endpoint is no longer available and the function will be removed in the next major version.\n   */\n  public async createSMSMessage(params: SMSParams) {\n    deprecated(\n      'SMSMessageAPI.createSMSMessage',\n      'This endpoint is no longer available and the function will be removed in the next major version.',\n    );\n    return this.request<SMSMessage>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n}\n", "import type { OAuthProvider } from '@clerk/types';\n\nimport runtime from '../../runtime';\nimport { joinPaths } from '../../util/path';\nimport type { OauthAccessToken, OrganizationMembership, User } from '../resources';\nimport { AbstractAPI } from './AbstractApi';\n\nconst basePath = '/users';\n\ntype UserCountParams = {\n  emailAddress?: string[];\n  phoneNumber?: string[];\n  username?: string[];\n  web3Wallet?: string[];\n  query?: string;\n  userId?: string[];\n  externalId?: string[];\n};\n\ntype UserListParams = UserCountParams & {\n  limit?: number;\n  offset?: number;\n  orderBy?: 'created_at' | 'updated_at' | '+created_at' | '+updated_at' | '-created_at' | '-updated_at';\n};\n\ntype UserMetadataParams = {\n  publicMetadata?: UserPublicMetadata;\n  privateMetadata?: UserPrivateMetadata;\n  unsafeMetadata?: UserUnsafeMetadata;\n};\n\ntype PasswordHasher =\n  | 'argon2i'\n  | 'argon2id'\n  | 'bcrypt'\n  | 'md5'\n  | 'pbkdf2_sha256'\n  | 'pbkdf2_sha256_django'\n  | 'pbkdf2_sha1'\n  | 'scrypt_firebase'\n  | 'scrypt_werkzeug'\n  | 'sha256';\n\ntype UserPasswordHashingParams = {\n  passwordDigest: string;\n  passwordHasher: PasswordHasher;\n};\n\ntype CreateUserParams = {\n  externalId?: string;\n  emailAddress?: string[];\n  phoneNumber?: string[];\n  username?: string;\n  password?: string;\n  firstName?: string;\n  lastName?: string;\n  skipPasswordChecks?: boolean;\n  skipPasswordRequirement?: boolean;\n  totpSecret?: string;\n  backupCodes?: string[];\n  createdAt?: Date;\n} & UserMetadataParams &\n  (UserPasswordHashingParams | object);\n\ntype UpdateUserParams = {\n  firstName?: string;\n  lastName?: string;\n  username?: string;\n  password?: string;\n  skipPasswordChecks?: boolean;\n  signOutOfOtherSessions?: boolean;\n  primaryEmailAddressID?: string;\n  primaryPhoneNumberID?: string;\n  primaryWeb3WalletID?: string;\n  profileImageID?: string;\n  totpSecret?: string;\n  backupCodes?: string[];\n  externalId?: string;\n  createdAt?: Date;\n  createOrganizationEnabled?: boolean;\n} & UserMetadataParams &\n  (UserPasswordHashingParams | object);\n\ntype GetOrganizationMembershipListParams = {\n  userId: string;\n  limit?: number;\n  offset?: number;\n};\n\ntype VerifyPasswordParams = {\n  userId: string;\n  password: string;\n};\n\ntype VerifyTOTPParams = {\n  userId: string;\n  code: string;\n};\n\nexport class UserAPI extends AbstractAPI {\n  public async getUserList(params: UserListParams = {}) {\n    return this.request<User[]>({\n      method: 'GET',\n      path: basePath,\n      queryParams: params,\n    });\n  }\n\n  public async getUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'GET',\n      path: joinPaths(basePath, userId),\n    });\n  }\n\n  public async createUser(params: CreateUserParams) {\n    return this.request<User>({\n      method: 'POST',\n      path: basePath,\n      bodyParams: params,\n    });\n  }\n\n  public async updateUser(userId: string, params: UpdateUserParams = {}) {\n    this.requireId(userId);\n\n    return this.request<User>({\n      method: 'PATCH',\n      path: joinPaths(basePath, userId),\n      bodyParams: params,\n    });\n  }\n\n  public async updateUserProfileImage(userId: string, params: { file: Blob | File }) {\n    this.requireId(userId);\n\n    const formData = new runtime.FormData();\n    formData.append('file', params?.file);\n\n    return this.request<User>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'profile_image'),\n      formData,\n    });\n  }\n\n  public async updateUserMetadata(userId: string, params: UserMetadataParams) {\n    this.requireId(userId);\n\n    return this.request<User>({\n      method: 'PATCH',\n      path: joinPaths(basePath, userId, 'metadata'),\n      bodyParams: params,\n    });\n  }\n\n  public async deleteUser(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId),\n    });\n  }\n\n  public async getCount(params: UserListParams = {}) {\n    return this.request<number>({\n      method: 'GET',\n      path: joinPaths(basePath, 'count'),\n      queryParams: params,\n    });\n  }\n\n  public async getUserOauthAccessToken(userId: string, provider: `oauth_${OAuthProvider}`) {\n    this.requireId(userId);\n    return this.request<OauthAccessToken[]>({\n      method: 'GET',\n      path: joinPaths(basePath, userId, 'oauth_access_tokens', provider),\n    });\n  }\n\n  public async disableUserMFA(userId: string) {\n    this.requireId(userId);\n    return this.request<User>({\n      method: 'DELETE',\n      path: joinPaths(basePath, userId, 'mfa'),\n    });\n  }\n\n  public async getOrganizationMembershipList(params: GetOrganizationMembershipListParams) {\n    const { userId, limit, offset } = params;\n    this.requireId(userId);\n\n    return this.request<OrganizationMembership[]>({\n      method: 'GET',\n      path: joinPaths(basePath, userId, 'organization_memberships'),\n      queryParams: { limit, offset },\n    });\n  }\n\n  public async verifyPassword(params: VerifyPasswordParams) {\n    const { userId, password } = params;\n    this.requireId(userId);\n\n    return this.request<{ verified: true }>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'verify_password'),\n      bodyParams: { password },\n    });\n  }\n\n  public async verifyTOTP(params: VerifyTOTPParams) {\n    const { userId, code } = params;\n    this.requireId(userId);\n\n    return this.request<{ verified: true; code_type: 'totp' }>({\n      method: 'POST',\n      path: joinPaths(basePath, userId, 'verify_totp'),\n      bodyParams: { code },\n    });\n  }\n}\n", "import { ClerkAPIResponseError } from '@clerk/shared/error';\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON>, ClerkAPIErrorJSON } from '@clerk/types';\nimport deepmerge from 'deepmerge';\nimport snakecaseKeys from 'snakecase-keys';\n\nimport { API_URL, API_VERSION, constants, USER_AGENT } from '../constants';\n// DO NOT CHANGE: Runtime needs to be imported as a default export so that we can stub its dependencies with Sinon.js\n// For more information refer to https://sinonjs.org/how-to/stub-dependency/\nimport runtime from '../runtime';\nimport { assertValidSecretKey } from '../util/assertValidSecretKey';\nimport { joinPaths } from '../util/path';\nimport { deprecated } from '../util/shared';\nimport type { CreateBackendApiOptions } from './factory';\nimport { deserialize } from './resources/Deserializer';\n\nexport type ClerkBackendApiRequestOptions = {\n  method: 'GET' | 'POST' | 'PATCH' | 'DELETE' | 'PUT';\n  queryParams?: Record<string, unknown>;\n  headerParams?: Record<string, string>;\n  bodyParams?: object;\n  formData?: FormData;\n} & (\n  | {\n      url: string;\n      path?: string;\n    }\n  | {\n      url?: string;\n      path: string;\n    }\n);\n\nexport type ClerkBackendApiResponse<T> =\n  | {\n      data: T;\n      errors: null;\n    }\n  | {\n      data: null;\n      errors: ClerkAPIError[];\n      clerkTraceId?: string;\n    };\n\nexport type RequestFunction = ReturnType<typeof buildRequest>;\ntype LegacyRequestFunction = <T>(requestOptions: ClerkBackendApiRequestOptions) => Promise<T>;\n\n/**\n * Switching to the { data, errors } format is a breaking change, so we will skip it for now\n * until we release v5 of the related SDKs.\n * This HOF wraps the request helper and transforms the new return to the legacy return.\n * TODO: Simply remove this wrapper and the ClerkAPIResponseError before the v5 release.\n */\nconst withLegacyReturn =\n  (cb: (...args: any) => Promise<ClerkBackendApiResponse<any>>): LegacyRequestFunction =>\n  async (...args) => {\n    const response = await cb(...args);\n    if (response.errors === null) {\n      return response.data;\n    } else {\n      const { errors, clerkTraceId } = response;\n      // TODO: To be removed with withLegacyReturn\n      const { status, statusText } = response as any;\n      const error = new ClerkAPIResponseError(statusText || '', {\n        data: [],\n        status: status || '',\n        clerkTraceId,\n      });\n      error.errors = errors;\n      throw error;\n    }\n  };\n\nexport function buildRequest(options: CreateBackendApiOptions) {\n  const request = async <T>(requestOptions: ClerkBackendApiRequestOptions): Promise<ClerkBackendApiResponse<T>> => {\n    const {\n      apiKey,\n      secretKey,\n      httpOptions,\n      apiUrl = API_URL,\n      apiVersion = API_VERSION,\n      userAgent = USER_AGENT,\n    } = options;\n    if (apiKey) {\n      deprecated('apiKey', 'Use `secretKey` instead.');\n    }\n    if (httpOptions) {\n      deprecated(\n        'httpOptions',\n        'This option has been deprecated and will be removed with the next major release.\\nA RequestInit init object used by the `request` method.',\n      );\n    }\n\n    const { path, method, queryParams, headerParams, bodyParams, formData } = requestOptions;\n    const key = secretKey || apiKey;\n\n    assertValidSecretKey(key);\n\n    const url = joinPaths(apiUrl, apiVersion, path);\n\n    // Build final URL with search parameters\n    const finalUrl = new URL(url);\n\n    if (queryParams) {\n      // Snakecase query parameters\n      const snakecasedQueryParams = snakecaseKeys({ ...queryParams });\n\n      // Support array values for queryParams such as { foo: [42, 43] }\n      for (const [key, val] of Object.entries(snakecasedQueryParams)) {\n        if (val) {\n          [val].flat().forEach(v => finalUrl.searchParams.append(key, v as string));\n        }\n      }\n    }\n\n    // Build headers\n    const headers: Record<string, any> = {\n      Authorization: `Bearer ${key}`,\n      'User-Agent': userAgent,\n      ...headerParams,\n    };\n\n    let res: Response | undefined = undefined;\n    try {\n      if (formData) {\n        res = await runtime.fetch(finalUrl.href, {\n          ...httpOptions,\n          method,\n          headers,\n          body: formData,\n        });\n      } else {\n        // Enforce application/json for all non form-data requests\n        headers['Content-Type'] = 'application/json';\n        // Build body\n        const hasBody = method !== 'GET' && bodyParams && Object.keys(bodyParams).length > 0;\n        const body = hasBody ? { body: JSON.stringify(snakecaseKeys(bodyParams, { deep: false })) } : null;\n\n        res = await runtime.fetch(\n          finalUrl.href,\n          deepmerge(httpOptions || {}, {\n            method,\n            headers,\n            ...body,\n          }),\n        );\n      }\n\n      // TODO: Parse JSON or Text response based on a response header\n      const isJSONResponse =\n        res?.headers && res.headers?.get(constants.Headers.ContentType) === constants.ContentTypes.Json;\n      const data = await (isJSONResponse ? res.json() : res.text());\n\n      if (!res.ok) {\n        throw data;\n      }\n\n      return {\n        data: deserialize(data),\n        errors: null,\n      };\n    } catch (err) {\n      if (err instanceof Error) {\n        return {\n          data: null,\n          errors: [\n            {\n              code: 'unexpected_error',\n              message: err.message || 'Unexpected error',\n            },\n          ],\n          clerkTraceId: getTraceId(err, res?.headers),\n        };\n      }\n\n      return {\n        data: null,\n        errors: parseErrors(err),\n        // TODO: To be removed with withLegacyReturn\n        // @ts-expect-error\n        status: res?.status,\n        statusText: res?.statusText,\n        clerkTraceId: getTraceId(err, res?.headers),\n      };\n    }\n  };\n\n  return withLegacyReturn(request);\n}\n\n// Returns either clerk_trace_id if present in response json, otherwise defaults to CF-Ray header\n// If the request failed before receiving a response, returns undefined\nfunction getTraceId(data: unknown, headers?: Headers): string {\n  if (data && typeof data === 'object' && 'clerk_trace_id' in data && typeof data.clerk_trace_id === 'string') {\n    return data.clerk_trace_id;\n  }\n\n  const cfRay = headers?.get('cf-ray');\n  return cfRay || '';\n}\n\nfunction parseErrors(data: unknown): ClerkAPIError[] {\n  if (!!data && typeof data === 'object' && 'errors' in data) {\n    const errors = data.errors as ClerkAPIErrorJSON[];\n    return errors.length > 0 ? errors.map(parseError) : [];\n  }\n  return [];\n}\n\nfunction parseError(error: ClerkAPIErrorJSON): ClerkAPIError {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n    },\n  };\n}\n", "export const API_URL = 'https://api.clerk.dev';\nexport const API_VERSION = 'v1';\n\nexport const USER_AGENT = `${PACKAGE_NAME}@${PACKAGE_VERSION}`;\nexport const MAX_CACHE_LAST_UPDATED_AT_SECONDS = 5 * 60;\n\nconst Attributes = {\n  AuthToken: '__clerkAuthToken',\n  AuthStatus: '__clerkAuthStatus',\n  AuthReason: '__clerkAuthReason',\n  AuthMessage: '__clerkAuthMessage',\n} as const;\n\nconst Cookies = {\n  Session: '__session',\n  ClientUat: '__client_uat',\n} as const;\n\nconst Headers = {\n  AuthToken: 'x-clerk-auth-token',\n  AuthStatus: 'x-clerk-auth-status',\n  AuthReason: 'x-clerk-auth-reason',\n  AuthMessage: 'x-clerk-auth-message',\n  EnableDebug: 'x-clerk-debug',\n  ClerkRedirectTo: 'x-clerk-redirect-to',\n  CloudFrontForwardedProto: 'cloudfront-forwarded-proto',\n  Authorization: 'authorization',\n  ForwardedPort: 'x-forwarded-port',\n  ForwardedProto: 'x-forwarded-proto',\n  ForwardedHost: 'x-forwarded-host',\n  Referrer: 'referer',\n  UserAgent: 'user-agent',\n  Origin: 'origin',\n  Host: 'host',\n  ContentType: 'content-type',\n} as const;\n\nconst SearchParams = {\n  AuthStatus: Headers.AuthStatus,\n  AuthToken: Headers.AuthToken,\n} as const;\n\nconst ContentTypes = {\n  Json: 'application/json',\n} as const;\n\nexport const constants = {\n  Attributes,\n  Cookies,\n  Headers,\n  SearchParams,\n  ContentTypes,\n} as const;\n", "export function assertValidSecret<PERSON>ey(val: unknown): asserts val is string {\n  if (!val || typeof val !== 'string') {\n    throw Error(\n      'Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.',\n    );\n  }\n\n  //TODO: Check if the key is invalid and throw error\n}\n", "import type { AllowlistIdentifierJSON } from './JSON';\n\nexport class AllowlistIdentifier {\n  constructor(\n    readonly id: string,\n    readonly identifier: string,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    readonly invitationId?: string,\n  ) {}\n\n  static fromJSON(data: AllowlistIdentifierJSON): AllowlistIdentifier {\n    return new AllowlistIdentifier(data.id, data.identifier, data.created_at, data.updated_at, data.invitation_id);\n  }\n}\n", "import type { <PERSON><PERSON><PERSON><PERSON> } from './JSON';\n\nexport class Session {\n  constructor(\n    readonly id: string,\n    readonly clientId: string,\n    readonly userId: string,\n    readonly status: string,\n    readonly lastActiveAt: number,\n    readonly expireAt: number,\n    readonly abandonAt: number,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: SessionJSON): Session {\n    return new Session(\n      data.id,\n      data.client_id,\n      data.user_id,\n      data.status,\n      data.last_active_at,\n      data.expire_at,\n      data.abandon_at,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { <PERSON><PERSON><PERSON>SO<PERSON> } from './JSON';\nimport { Session } from './Session';\n\nexport class Client {\n  constructor(\n    readonly id: string,\n    readonly sessionIds: string[],\n    readonly sessions: Session[],\n    readonly signInId: string | null,\n    readonly signUpId: string | null,\n    readonly lastActiveSessionId: string | null,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: ClientJSON): Client {\n    return new Client(\n      data.id,\n      data.session_ids,\n      data.sessions.map(x => Session.fromJSON(x)),\n      data.sign_in_id,\n      data.sign_up_id,\n      data.last_active_session_id,\n      data.created_at,\n      data.updated_at,\n    );\n  }\n}\n", "import type { DeletedObjectJSON } from './JSON';\n\nexport class DeletedObject {\n  constructor(\n    readonly object: string,\n    readonly id: string | null,\n    readonly slug: string | null,\n    readonly deleted: boolean,\n  ) {}\n\n  static fromJSON(data: DeletedObjectJSON) {\n    return new DeletedObject(data.object, data.id || null, data.slug || null, data.deleted);\n  }\n}\n", "import type { EmailJSON } from './JSON';\n\nexport class Email {\n  constructor(\n    readonly id: string,\n    readonly fromEmailName: string,\n    readonly emailAddressId: string | null,\n    readonly toEmailAddress?: string,\n    readonly subject?: string,\n    readonly body?: string,\n    readonly bodyPlain?: string | null,\n    readonly status?: string,\n    readonly slug?: string | null,\n    readonly data?: Record<string, any> | null,\n    readonly deliveredByClerk?: boolean,\n  ) {}\n\n  static fromJSON(data: EmailJSON): Email {\n    return new Email(\n      data.id,\n      data.from_email_name,\n      data.email_address_id,\n      data.to_email_address,\n      data.subject,\n      data.body,\n      data.body_plain,\n      data.status,\n      data.slug,\n      data.data,\n      data.delivered_by_clerk,\n    );\n  }\n}\n", "import type { IdentificationLinkJSON } from './JSON';\n\nexport class IdentificationLink {\n  constructor(readonly id: string, readonly type: string) {}\n\n  static fromJSON(data: IdentificationLinkJSON): IdentificationLink {\n    return new IdentificationLink(data.id, data.type);\n  }\n}\n", "import type { VerificationJSO<PERSON> } from './JSON';\n\nexport class Verification {\n  constructor(\n    readonly status: string,\n    readonly strategy: string,\n    readonly externalVerificationRedirectURL: URL | null = null,\n    readonly attempts: number | null = null,\n    readonly expireAt: number | null = null,\n    readonly nonce: string | null = null,\n  ) {}\n\n  static fromJSON(data: VerificationJSON): Verification {\n    return new Verification(\n      data.status,\n      data.strategy,\n      data.external_verification_redirect_url ? new URL(data.external_verification_redirect_url) : null,\n      data.attempts,\n      data.expire_at,\n      data.nonce,\n    );\n  }\n}\n", "import { IdentificationLink } from './IdentificationLink';\nimport type { EmailAddressJSON } from './JSON';\nimport { Verification } from './Verification';\n\nexport class EmailAddress {\n  constructor(\n    readonly id: string,\n    readonly emailAddress: string,\n    readonly verification: Verification | null,\n    readonly linkedTo: IdentificationLink[],\n  ) {}\n\n  static fromJSON(data: EmailAddressJSON): EmailAddress {\n    return new EmailAddress(\n      data.id,\n      data.email_address,\n      data.verification && Verification.fromJSON(data.verification),\n      data.linked_to.map(link => IdentificationLink.fromJSON(link)),\n    );\n  }\n}\n", "import { deprecatedProperty } from '../../util/shared';\nimport type { ExternalAccountJSON } from './JSON';\nimport { Verification } from './Verification';\n\nexport class ExternalAccount {\n  constructor(\n    readonly id: string,\n    readonly provider: string,\n    readonly identificationId: string,\n    readonly externalId: string,\n    readonly approvedScopes: string,\n    readonly emailAddress: string,\n    readonly firstName: string,\n    readonly lastName: string,\n    /**\n     * @deprecated  Use `imageUrl` instead.\n     */\n    readonly picture: string,\n    readonly imageUrl: string,\n    readonly username: string | null,\n    readonly publicMetadata: Record<string, unknown> | null = {},\n    readonly label: string | null,\n    readonly verification: Verification | null,\n  ) {}\n\n  static fromJSON(data: ExternalAccountJSON): ExternalAccount {\n    return new ExternalAccount(\n      data.id,\n      data.provider,\n      data.identification_id,\n      data.provider_user_id,\n      data.approved_scopes,\n      data.email_address,\n      data.first_name,\n      data.last_name,\n      data.avatar_url,\n      data.image_url,\n      data.username,\n      data.public_metadata,\n      data.label,\n      data.verification && Verification.fromJSON(data.verification),\n    );\n  }\n}\n\ndeprecatedProperty(ExternalAccount, 'picture', 'Use `imageUrl` instead.');\n", "import type { InvitationStatus } from './Enums';\nimport type { InvitationJSON } from './JSON';\n\nexport class Invitation {\n  constructor(\n    readonly id: string,\n    readonly emailAddress: string,\n    readonly publicMetadata: Record<string, unknown> | null,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    readonly status: InvitationStatus,\n    readonly revoked?: boolean,\n  ) {}\n\n  static fromJSON(data: InvitationJSON): Invitation {\n    return new Invitation(\n      data.id,\n      data.email_address,\n      data.public_metadata,\n      data.created_at,\n      data.updated_at,\n      data.status,\n      data.revoked,\n    );\n  }\n}\n", "import type {\n  InvitationStatus,\n  OrganizationInvitationStatus,\n  OrganizationMembershipRole,\n  SignInStatus,\n  SignUpAttributeRequirements,\n  SignUpStatus,\n} from './Enums';\n\nexport enum ObjectType {\n  AllowlistIdentifier = 'allowlist_identifier',\n  Client = 'client',\n  Email = 'email',\n  EmailAddress = 'email_address',\n  ExternalAccount = 'external_account',\n  FacebookAccount = 'facebook_account',\n  GoogleAccount = 'google_account',\n  Invitation = 'invitation',\n  OauthAccessToken = 'oauth_access_token',\n  Organization = 'organization',\n  OrganizationInvitation = 'organization_invitation',\n  OrganizationMembership = 'organization_membership',\n  PhoneNumber = 'phone_number',\n  RedirectUrl = 'redirect_url',\n  Session = 'session',\n  SignInAttempt = 'sign_in_attempt',\n  SignInToken = 'sign_in_token',\n  SignUpAttempt = 'sign_up_attempt',\n  SmsMessage = 'sms_message',\n  User = 'user',\n  Web3Wallet = 'web3_wallet',\n  Token = 'token',\n  TotalCount = 'total_count',\n}\n\nexport interface ClerkResourceJSON {\n  object: ObjectType;\n  id: string;\n}\n\nexport interface TokenJSON {\n  object: ObjectType.Token;\n  jwt: string;\n}\n\nexport interface AllowlistIdentifierJSON extends ClerkResourceJSON {\n  object: ObjectType.AllowlistIdentifier;\n  identifier: string;\n  created_at: number;\n  updated_at: number;\n  invitation_id?: string;\n}\n\nexport interface ClientJSON extends ClerkResourceJSON {\n  object: ObjectType.Client;\n  session_ids: string[];\n  sessions: SessionJSON[];\n  sign_in_id: string | null;\n  sign_up_id: string | null;\n  last_active_session_id: string | null;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface EmailJSON extends ClerkResourceJSON {\n  object: ObjectType.Email;\n  from_email_name: string;\n  to_email_address?: string;\n  email_address_id: string | null;\n  subject?: string;\n  body?: string;\n  body_plain?: string | null;\n  status?: string;\n  slug?: string | null;\n  data?: Record<string, any> | null;\n  delivered_by_clerk?: boolean;\n}\n\nexport interface EmailAddressJSON extends ClerkResourceJSON {\n  object: ObjectType.EmailAddress;\n  email_address: string;\n  verification: VerificationJSON | null;\n  linked_to: IdentificationLinkJSON[];\n}\n\nexport interface ExternalAccountJSON extends ClerkResourceJSON {\n  object: ObjectType.ExternalAccount;\n  provider: string;\n  identification_id: string;\n  provider_user_id: string;\n  approved_scopes: string;\n  email_address: string;\n  first_name: string;\n  last_name: string;\n  /**\n   * @deprecated  Use `image_url` instead.\n   */\n  avatar_url: string;\n  image_url: string;\n  username: string | null;\n  public_metadata: Record<string, unknown> | null;\n  label: string | null;\n  verification: VerificationJSON | null;\n}\n\nexport interface IdentificationLinkJSON extends ClerkResourceJSON {\n  type: string;\n}\n\nexport interface InvitationJSON extends ClerkResourceJSON {\n  object: ObjectType.Invitation;\n  email_address: string;\n  public_metadata: Record<string, unknown> | null;\n  created_at: number;\n  updated_at: number;\n  status: InvitationStatus;\n  revoked?: boolean;\n}\n\nexport interface OauthAccessTokenJSON {\n  object: ObjectType.OauthAccessToken;\n  provider: string;\n  token: string;\n  public_metadata: Record<string, unknown>;\n  label: string;\n  // Only set in OAuth 2.0 tokens\n  scopes?: string[];\n  // Only set in OAuth 1.0 tokens\n  token_secret?: string;\n}\n\nexport interface OrganizationJSON extends ClerkResourceJSON {\n  object: ObjectType.Organization;\n  name: string;\n  slug: string | null;\n  /**\n   * @deprecated  Use `image_url` instead.\n   */\n  logo_url: string | null;\n  image_url: string;\n  has_image: boolean;\n  public_metadata: OrganizationPublicMetadata | null;\n  private_metadata?: OrganizationPrivateMetadata;\n  created_by: string;\n  created_at: number;\n  updated_at: number;\n  max_allowed_memberships: number;\n  admin_delete_enabled: boolean;\n  members_count?: number;\n}\n\nexport interface OrganizationInvitationJSON extends ClerkResourceJSON {\n  email_address: string;\n  organization_id: string;\n  public_metadata: OrganizationInvitationPublicMetadata;\n  private_metadata: OrganizationInvitationPrivateMetadata;\n  role: OrganizationMembershipRole;\n  status: OrganizationInvitationStatus;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface OrganizationMembershipJSON extends ClerkResourceJSON {\n  object: ObjectType.OrganizationMembership;\n  organization: OrganizationJSON;\n  public_metadata: OrganizationMembershipPublicMetadata;\n  private_metadata?: OrganizationMembershipPrivateMetadata;\n  public_user_data: OrganizationMembershipPublicUserDataJSON;\n  role: OrganizationMembershipRole;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface OrganizationMembershipPublicUserDataJSON {\n  identifier: string;\n  first_name: string | null;\n  last_name: string | null;\n  /**\n   * @deprecated  Use `image_url` instead.\n   */\n  profile_image_url: string;\n  image_url: string;\n  has_image: boolean;\n  user_id: string;\n}\n\nexport interface PhoneNumberJSON extends ClerkResourceJSON {\n  object: ObjectType.PhoneNumber;\n  phone_number: string;\n  reserved_for_second_factor: boolean;\n  default_second_factor: boolean;\n  linked_to: IdentificationLinkJSON[];\n  verification: VerificationJSON | null;\n}\n\nexport interface RedirectUrlJSON extends ClerkResourceJSON {\n  object: ObjectType.RedirectUrl;\n  url: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SessionJSON extends ClerkResourceJSON {\n  object: ObjectType.Session;\n  client_id: string;\n  user_id: string;\n  status: string;\n  last_active_at: number;\n  expire_at: number;\n  abandon_at: number;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SignInJSON extends ClerkResourceJSON {\n  object: ObjectType.SignInToken;\n  status: SignInStatus;\n  identifier: string;\n  created_session_id: string | null;\n}\n\nexport interface SignInTokenJSON extends ClerkResourceJSON {\n  user_id: string;\n  token: string;\n  status: 'pending' | 'accepted' | 'revoked';\n  url: string;\n  created_at: number;\n  updated_at: number;\n}\n\nexport interface SignUpJSON extends ClerkResourceJSON {\n  object: ObjectType.SignUpAttempt;\n  status: SignUpStatus;\n  attribute_requirements: SignUpAttributeRequirements;\n  username: string | null;\n  email_address: string | null;\n  phone_number: string | null;\n  web3_wallet: string | null;\n  web3_wallet_verification: VerificationJSON | null;\n  external_account: any;\n  has_password: boolean;\n  name_full: string | null;\n  created_session_id: string | null;\n  created_user_id: string | null;\n  abandon_at: number | null;\n}\n\nexport interface SMSMessageJSON extends ClerkResourceJSON {\n  object: ObjectType.SmsMessage;\n  from_phone_number: string;\n  to_phone_number: string;\n  phone_number_id: string | null;\n  message: string;\n  status: string;\n  data?: Record<string, any> | null;\n}\n\nexport interface UserJSON extends ClerkResourceJSON {\n  object: ObjectType.User;\n  username: string | null;\n  first_name: string;\n  last_name: string;\n  gender: string;\n  birthday: string;\n  /**\n   * @deprecated  Use `image_url` instead.\n   */\n  profile_image_url: string;\n  image_url: string;\n  has_image: boolean;\n  primary_email_address_id: string;\n  primary_phone_number_id: string | null;\n  primary_web3_wallet_id: string | null;\n  password_enabled: boolean;\n  totp_enabled: boolean;\n  backup_code_enabled: boolean;\n  two_factor_enabled: boolean;\n  banned: boolean;\n  email_addresses: EmailAddressJSON[];\n  phone_numbers: PhoneNumberJSON[];\n  web3_wallets: Web3WalletJSON[];\n  external_accounts: ExternalAccountJSON[];\n  external_id: string | null;\n  last_sign_in_at: number | null;\n  public_metadata: UserPublicMetadata;\n  private_metadata: UserPrivateMetadata;\n  unsafe_metadata: UserUnsafeMetadata;\n  created_at: number;\n  updated_at: number;\n  create_organization_enabled: boolean;\n}\n\nexport interface VerificationJSON extends ClerkResourceJSON {\n  attempts?: number | null;\n  expire_at: number | null;\n  external_verification_redirect_url?: string;\n  nonce?: string | null;\n  status: string;\n  strategy: string;\n  verified_at_client?: string;\n  // error?\n}\n\nexport interface Web3WalletJSON extends ClerkResourceJSON {\n  object: ObjectType.Web3Wallet;\n  web3_wallet: string;\n  verification: VerificationJSON | null;\n}\n\nexport interface DeletedObjectJSON {\n  object: string;\n  id?: string;\n  slug?: string;\n  deleted: boolean;\n}\n", "import type { OauthAccessTokenJSON } from './JSON';\n\nexport class OauthAccessToken {\n  constructor(\n    readonly provider: string,\n    readonly token: string,\n    readonly publicMetadata: Record<string, unknown> = {},\n    readonly label: string,\n    readonly scopes?: string[],\n    readonly tokenSecret?: string,\n  ) {}\n\n  static fromJSON(data: OauthAccessTokenJSON) {\n    return new OauthAccessToken(\n      data.provider,\n      data.token,\n      data.public_metadata,\n      data.label,\n      data.scopes,\n      data.token_secret,\n    );\n  }\n}\n", "import { deprecatedProperty } from '../../util/shared';\nimport type { OrganizationJSON } from './JSON';\n\nexport class Organization {\n  constructor(\n    readonly id: string,\n    readonly name: string,\n    readonly slug: string | null,\n    /**\n     * @deprecated  Use `imageUrl` instead.\n     */\n    readonly logoUrl: string | null,\n    readonly imageUrl: string,\n    readonly hasImage: boolean,\n    readonly createdBy: string,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    readonly publicMetadata: OrganizationPublicMetadata | null = {},\n    readonly privateMetadata: OrganizationPrivateMetadata = {},\n    readonly maxAllowedMemberships: number,\n    readonly adminDeleteEnabled: boolean,\n    /**\n     * @deprecated  Use `membersCount` instead.\n     */\n    readonly members_count?: number,\n    readonly membersCount?: number,\n  ) {}\n\n  static fromJSON(data: OrganizationJSON): Organization {\n    return new Organization(\n      data.id,\n      data.name,\n      data.slug,\n      data.logo_url,\n      data.image_url,\n      data.has_image,\n      data.created_by,\n      data.created_at,\n      data.updated_at,\n      data.public_metadata,\n      data.private_metadata,\n      data.max_allowed_memberships,\n      data.admin_delete_enabled,\n      data.members_count,\n      data.members_count,\n    );\n  }\n}\n\ndeprecatedProperty(Organization, 'logoUrl', 'Use `imageUrl` instead.');\ndeprecatedProperty(Organization, 'members_count', 'Use `membersCount` instead.');\n", "import type { OrganizationInvitationStatus, OrganizationMembershipRole } from './Enums';\nimport type { OrganizationInvitationJSON } from './JSON';\n\nexport class OrganizationInvitation {\n  constructor(\n    readonly id: string,\n    readonly emailAddress: string,\n    readonly role: OrganizationMembershipRole,\n    readonly organizationId: string,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    readonly status?: OrganizationInvitationStatus,\n    readonly publicMetadata: OrganizationInvitationPublicMetadata = {},\n    readonly privateMetadata: OrganizationInvitationPrivateMetadata = {},\n  ) {}\n\n  static fromJSON(data: OrganizationInvitationJSON) {\n    return new OrganizationInvitation(\n      data.id,\n      data.email_address,\n      data.role,\n      data.organization_id,\n      data.created_at,\n      data.updated_at,\n      data.status,\n      data.public_metadata,\n      data.private_metadata,\n    );\n  }\n}\n", "import { deprecatedProperty } from '../../util/shared';\nimport { Organization } from '../resources';\nimport type { OrganizationMembershipRole } from './Enums';\nimport type { OrganizationMembershipJSON, OrganizationMembershipPublicUserDataJSON } from './JSON';\n\nexport class OrganizationMembership {\n  constructor(\n    readonly id: string,\n    readonly role: OrganizationMembershipRole,\n    readonly publicMetadata: OrganizationMembershipPublicMetadata = {},\n    readonly privateMetadata: OrganizationMembershipPrivateMetadata = {},\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    readonly organization: Organization,\n    readonly publicUserData?: OrganizationMembershipPublicUserData | null,\n  ) {}\n\n  static fromJSON(data: OrganizationMembershipJSON) {\n    return new OrganizationMembership(\n      data.id,\n      data.role,\n      data.public_metadata,\n      data.private_metadata,\n      data.created_at,\n      data.updated_at,\n      Organization.fromJSON(data.organization),\n      OrganizationMembershipPublicUserData.fromJSON(data.public_user_data),\n    );\n  }\n}\n\nexport class OrganizationMembershipPublicUserData {\n  constructor(\n    readonly identifier: string,\n    readonly firstName: string | null,\n    readonly lastName: string | null,\n    /**\n     * @deprecated  Use `imageUrl` instead.\n     */\n    readonly profileImageUrl: string,\n    readonly imageUrl: string,\n    readonly hasImage: boolean,\n    readonly userId: string,\n  ) {}\n\n  static fromJSON(data: OrganizationMembershipPublicUserDataJSON) {\n    return new OrganizationMembershipPublicUserData(\n      data.identifier,\n      data.first_name,\n      data.last_name,\n      data.profile_image_url,\n      data.image_url,\n      data.has_image,\n      data.user_id,\n    );\n  }\n}\n\ndeprecatedProperty(OrganizationMembershipPublicUserData, 'profileImageUrl', 'Use `imageUrl` instead.');\n", "import { IdentificationLink } from './IdentificationLink';\nimport type { PhoneNumberJSON } from './JSON';\nimport { Verification } from './Verification';\n\nexport class PhoneNumber {\n  constructor(\n    readonly id: string,\n    readonly phoneNumber: string,\n    readonly reservedForSecondFactor: boolean,\n    readonly defaultSecondFactor: boolean,\n    readonly verification: Verification | null,\n    readonly linkedTo: IdentificationLink[],\n  ) {}\n\n  static fromJSON(data: PhoneNumberJSON): PhoneNumber {\n    return new PhoneNumber(\n      data.id,\n      data.phone_number,\n      data.reserved_for_second_factor,\n      data.default_second_factor,\n      data.verification && Verification.fromJSON(data.verification),\n      data.linked_to.map(link => IdentificationLink.fromJSON(link)),\n    );\n  }\n}\n", "import type { RedirectUrlJSON } from './JSON';\n\nexport class RedirectUrl {\n  constructor(readonly id: string, readonly url: string, readonly createdAt: number, readonly updatedAt: number) {}\n\n  static fromJSON(data: RedirectUrlJSON): RedirectUrl {\n    return new RedirectUrl(data.id, data.url, data.created_at, data.updated_at);\n  }\n}\n", "import type { SignInTokenJSON } from './JSON';\n\nexport class SignInToken {\n  constructor(\n    readonly id: string,\n    readonly userId: string,\n    readonly token: string,\n    readonly status: string,\n    readonly url: string,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n  ) {}\n\n  static fromJSON(data: SignInTokenJSON): SignInToken {\n    return new SignInToken(data.id, data.user_id, data.token, data.status, data.url, data.created_at, data.updated_at);\n  }\n}\n", "import type { SMSMessageJSON } from './JSON';\n\nexport class SMSMessage {\n  constructor(\n    readonly id: string,\n    readonly fromPhoneNumber: string,\n    readonly toPhoneNumber: string,\n    readonly message: string,\n    readonly status: string,\n    readonly phoneNumberId: string | null,\n    readonly data?: Record<string, any> | null,\n  ) {}\n\n  static fromJSON(data: SMSMessageJSON): SMSMessage {\n    return new SMSMessage(\n      data.id,\n      data.from_phone_number,\n      data.to_phone_number,\n      data.message,\n      data.status,\n      data.phone_number_id,\n      data.data,\n    );\n  }\n}\n", "import type { TokenJSON } from './JSON';\n\nexport class Token {\n  constructor(readonly jwt: string) {}\n\n  static fromJSON(data: TokenJSON): Token {\n    return new Token(data.jwt);\n  }\n}\n", "import type { Web3WalletJSON } from './JSON';\nimport { Verification } from './Verification';\n\nexport class Web3Wallet {\n  constructor(readonly id: string, readonly web3Wallet: string, readonly verification: Verification | null) {}\n\n  static fromJSON(data: Web3WalletJSON): Web3Wallet {\n    return new Web3Wallet(data.id, data.web3_wallet, data.verification && Verification.fromJSON(data.verification));\n  }\n}\n", "import { deprecatedProperty } from '../../util/shared';\nimport { EmailAddress } from './EmailAddress';\nimport { ExternalAccount } from './ExternalAccount';\nimport type { ExternalAccountJSON, UserJSON } from './JSON';\nimport { PhoneNumber } from './PhoneNumber';\nimport { Web3Wallet } from './Web3Wallet';\n\nexport class User {\n  constructor(\n    readonly id: string,\n    readonly passwordEnabled: boolean,\n    readonly totpEnabled: boolean,\n    readonly backupCodeEnabled: boolean,\n    readonly twoFactorEnabled: boolean,\n    readonly banned: boolean,\n    readonly createdAt: number,\n    readonly updatedAt: number,\n    /**\n     * @deprecated  Use `imageUrl` instead.\n     */\n    readonly profileImageUrl: string,\n    readonly imageUrl: string,\n    readonly hasImage: boolean,\n    readonly gender: string,\n    readonly birthday: string,\n    readonly primaryEmailAddressId: string | null,\n    readonly primaryPhoneNumberId: string | null,\n    readonly primaryWeb3WalletId: string | null,\n    readonly lastSignInAt: number | null,\n    readonly externalId: string | null,\n    readonly username: string | null,\n    readonly firstName: string | null,\n    readonly lastName: string | null,\n    readonly publicMetadata: UserPublicMetadata = {},\n    readonly privateMetadata: UserPrivateMetadata = {},\n    readonly unsafeMetadata: UserUnsafeMetadata = {},\n    readonly emailAddresses: EmailAddress[] = [],\n    readonly phoneNumbers: PhoneNumber[] = [],\n    readonly web3Wallets: Web3Wallet[] = [],\n    readonly externalAccounts: ExternalAccount[] = [],\n    readonly createOrganizationEnabled: boolean,\n  ) {}\n\n  static fromJSON(data: UserJSON): User {\n    return new User(\n      data.id,\n      data.password_enabled,\n      data.totp_enabled,\n      data.backup_code_enabled,\n      data.two_factor_enabled,\n      data.banned,\n      data.created_at,\n      data.updated_at,\n      data.profile_image_url,\n      data.image_url,\n      data.has_image,\n      data.gender,\n      data.birthday,\n      data.primary_email_address_id,\n      data.primary_phone_number_id,\n      data.primary_web3_wallet_id,\n      data.last_sign_in_at,\n      data.external_id,\n      data.username,\n      data.first_name,\n      data.last_name,\n      data.public_metadata,\n      data.private_metadata,\n      data.unsafe_metadata,\n      (data.email_addresses || []).map(x => EmailAddress.fromJSON(x)),\n      (data.phone_numbers || []).map(x => PhoneNumber.fromJSON(x)),\n      (data.web3_wallets || []).map(x => Web3Wallet.fromJSON(x)),\n      (data.external_accounts || []).map((x: ExternalAccountJSON) => ExternalAccount.fromJSON(x)),\n      data.create_organization_enabled,\n    );\n  }\n}\n\ndeprecatedProperty(User, 'profileImageUrl', 'Use `imageUrl` instead.');\n", "import {\n  AllowlistIdentifier,\n  Client,\n  DeletedObject,\n  Email,\n  EmailAddress,\n  Invitation,\n  OauthAccessToken,\n  Organization,\n  OrganizationInvitation,\n  OrganizationMembership,\n  PhoneNumber,\n  RedirectUrl,\n  Session,\n  SignInToken,\n  SMSMessage,\n  Token,\n  User,\n} from '.';\nimport { ObjectType } from './JSON';\n\n// FIXME don't return any\nexport function deserialize(payload: any): any {\n  if (Array.isArray(payload)) {\n    return payload.map(item => jsonToObject(item));\n  } else if (isPaginated(payload)) {\n    return payload.data.map(item => jsonToObject(item));\n  } else {\n    return jsonToObject(payload);\n  }\n}\n\ntype PaginatedResponse = {\n  data: object[];\n};\n\nfunction isPaginated(payload: any): payload is PaginatedResponse {\n  return Array.isArray(payload.data) && <PaginatedResponse>payload.data !== undefined;\n}\n\nfunction getCount(item: { total_count: number }) {\n  return item.total_count;\n}\n\n// TODO: Revise response deserialization\nfunction jsonToObject(item: any): any {\n  // Special case: DeletedObject\n  // TODO: Improve this check\n  if (typeof item !== 'string' && 'object' in item && 'deleted' in item) {\n    return DeletedObject.fromJSON(item);\n  }\n\n  switch (item.object) {\n    case ObjectType.AllowlistIdentifier:\n      return AllowlistIdentifier.fromJSON(item);\n    case ObjectType.Client:\n      return Client.fromJSON(item);\n    case ObjectType.EmailAddress:\n      return EmailAddress.fromJSON(item);\n    case ObjectType.Email:\n      return Email.fromJSON(item);\n    case ObjectType.Invitation:\n      return Invitation.fromJSON(item);\n    case ObjectType.OauthAccessToken:\n      return OauthAccessToken.fromJSON(item);\n    case ObjectType.Organization:\n      return Organization.fromJSON(item);\n    case ObjectType.OrganizationInvitation:\n      return OrganizationInvitation.fromJSON(item);\n    case ObjectType.OrganizationMembership:\n      return OrganizationMembership.fromJSON(item);\n    case ObjectType.PhoneNumber:\n      return PhoneNumber.fromJSON(item);\n    case ObjectType.RedirectUrl:\n      return RedirectUrl.fromJSON(item);\n    case ObjectType.SignInToken:\n      return SignInToken.fromJSON(item);\n    case ObjectType.Session:\n      return Session.fromJSON(item);\n    case ObjectType.SmsMessage:\n      return SMSMessage.fromJSON(item);\n    case ObjectType.Token:\n      return Token.fromJSON(item);\n    case ObjectType.TotalCount:\n      return getCount(item);\n    case ObjectType.User:\n      return User.fromJSON(item);\n    default:\n      return item;\n  }\n}\n", "import {\n  AllowlistIdentifierAPI,\n  ClientAPI,\n  DomainAPI,\n  EmailAddressAPI,\n  EmailAPI,\n  InterstitialAPI,\n  InvitationAPI,\n  OrganizationAPI,\n  PhoneNumberAPI,\n  RedirectUrlAPI,\n  SessionAPI,\n  SignInTokenAPI,\n  SMSMessageAPI,\n  UserAPI,\n} from './endpoints';\nimport { buildRequest } from './request';\n\nexport type CreateBackendApiOptions = {\n  /**\n   * Backend API key\n   * @deprecated Use `secretKey` instead.\n   */\n  apiKey?: string;\n  /* Secret Key */\n  secretKey?: string;\n  /* Backend API URL */\n  apiUrl?: string;\n  /* Backend API version */\n  apiVersion?: string;\n  /* Library/SDK name */\n  userAgent?: string;\n  /**\n   * @deprecated This option has been deprecated and will be removed with the next major release.\n   * A RequestInit init object used by the `request` method.\n   */\n  httpOptions?: RequestInit;\n};\n\nexport type ApiClient = ReturnType<typeof createBackendApiClient>;\n\nexport function createBackendApiClient(options: CreateBackendApiOptions) {\n  const request = buildRequest(options);\n\n  return {\n    allowlistIdentifiers: new AllowlistIdentifierAPI(request),\n    clients: new ClientAPI(request),\n    emailAddresses: new EmailAddressAPI(request),\n    emails: new EmailAPI(request),\n    interstitial: new InterstitialAPI(request),\n    invitations: new InvitationAPI(request),\n    organizations: new OrganizationAPI(request),\n    phoneNumbers: new PhoneNumberAPI(request),\n    redirectUrls: new RedirectUrlAPI(request),\n    sessions: new SessionAPI(request),\n    signInTokens: new SignInTokenAPI(request),\n    smsMessages: new SMSMessageAPI(request),\n    users: new UserAPI(request),\n    domains: new DomainAPI(request),\n  };\n}\n", "import { deprecated } from '@clerk/shared/deprecated';\nimport type {\n  ActClaim,\n  CheckAuthorizationWithCustomPermissions,\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  ServerGetToken,\n  ServerGetTokenOptions,\n} from '@clerk/types';\n\nimport type { Organization, Session, User } from '../api';\nimport { createBackendApiClient } from '../api';\nimport type { RequestState } from './authStatus';\nimport type { AuthenticateRequestOptions } from './request';\n\ntype AuthObjectDebugData = Partial<AuthenticateRequestOptions & RequestState>;\ntype CreateAuthObjectDebug = (data?: Record<string, unknown>) => AuthObjectDebug;\ntype AuthObjectDebug = () => unknown;\n\nexport type SignedInAuthObjectOptions = {\n  /**\n   * @deprecated Use `secretKey` instead.\n   */\n  apiKey?: string;\n  secretKey?: string;\n  apiUrl: string;\n  apiVersion: string;\n  token: string;\n  session?: Session;\n  user?: User;\n  organization?: Organization;\n};\n\nexport type SignedInAuthObject = {\n  sessionClaims: JwtPayload;\n  sessionId: string;\n  session: Session | undefined;\n  actor: ActClaim | undefined;\n  userId: string;\n  user: User | undefined;\n  orgId: string | undefined;\n  orgRole: OrganizationCustomRoleKey | undefined;\n  orgSlug: string | undefined;\n  orgPermissions: OrganizationCustomPermissionKey[] | undefined;\n  organization: Organization | undefined;\n  getToken: ServerGetToken;\n  has: CheckAuthorizationWithCustomPermissions;\n  debug: AuthObjectDebug;\n};\n\nexport type SignedOutAuthObject = {\n  sessionClaims: null;\n  sessionId: null;\n  session: null;\n  actor: null;\n  userId: null;\n  user: null;\n  orgId: null;\n  orgRole: null;\n  orgSlug: null;\n  orgPermissions: null;\n  organization: null;\n  getToken: ServerGetToken;\n  has: CheckAuthorizationWithCustomPermissions;\n  debug: AuthObjectDebug;\n};\n\nexport type AuthObject = SignedInAuthObject | SignedOutAuthObject;\n\nconst createDebug: CreateAuthObjectDebug = data => {\n  return () => {\n    const res = { ...data } || {};\n    res.apiKey = ((res.apiKey as string) || '').substring(0, 7);\n    res.secretKey = ((res.secretKey as string) || '').substring(0, 7);\n    res.jwtKey = ((res.jwtKey as string) || '').substring(0, 7);\n    return { ...res };\n  };\n};\n\nexport function signedInAuthObject(\n  sessionClaims: JwtPayload,\n  options: SignedInAuthObjectOptions,\n  debugData?: AuthObjectDebugData,\n): SignedInAuthObject {\n  const {\n    act: actor,\n    sid: sessionId,\n    org_id: orgId,\n    org_role: orgRole,\n    org_slug: orgSlug,\n    org_permissions: orgPermissions,\n    sub: userId,\n  } = sessionClaims;\n  const { apiKey, secretKey, apiUrl, apiVersion, token, session, user, organization } = options;\n\n  if (apiKey) {\n    deprecated('apiKey', 'Use `secretKey` instead.');\n  }\n\n  const { sessions } = createBackendApiClient({\n    apiKey,\n    secretKey,\n    apiUrl,\n    apiVersion,\n  });\n\n  const getToken = createGetToken({\n    sessionId,\n    sessionToken: token,\n    fetcher: (...args) => sessions.getToken(...args),\n  });\n\n  return {\n    actor,\n    sessionClaims,\n    sessionId,\n    session,\n    userId,\n    user,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    organization,\n    getToken,\n    has: createHasAuthorization({ orgId, orgRole, orgPermissions, userId }),\n    debug: createDebug({ ...options, ...debugData }),\n  };\n}\n\nexport function signedOutAuthObject(debugData?: AuthObjectDebugData): SignedOutAuthObject {\n  if (debugData?.apiKey) {\n    deprecated('apiKey', 'Use `secretKey` instead.');\n  }\n\n  return {\n    sessionClaims: null,\n    sessionId: null,\n    session: null,\n    userId: null,\n    user: null,\n    actor: null,\n    orgId: null,\n    orgRole: null,\n    orgSlug: null,\n    orgPermissions: null,\n    organization: null,\n    getToken: () => Promise.resolve(null),\n    has: () => false,\n    debug: createDebug(debugData),\n  };\n}\n\nexport function prunePrivateMetadata(\n  resource?:\n    | {\n        private_metadata: any;\n      }\n    | {\n        privateMetadata: any;\n      }\n    | null,\n) {\n  // Delete sensitive private metadata from resource before rendering in SSR\n  if (resource) {\n    // @ts-ignore\n    delete resource['privateMetadata'];\n    // @ts-ignore\n    delete resource['private_metadata'];\n  }\n\n  return resource;\n}\n\nexport function sanitizeAuthObject<T extends Record<any, any>>(authObject: T): T {\n  const user = authObject.user ? { ...authObject.user } : authObject.user;\n  const organization = authObject.organization ? { ...authObject.organization } : authObject.organization;\n\n  prunePrivateMetadata(user);\n  prunePrivateMetadata(organization);\n\n  return { ...authObject, user, organization };\n}\n\n/**\n * Auth objects moving through the server -> client boundary need to be serializable\n * as we need to ensure that they can be transferred via the network as pure strings.\n * Some frameworks like Remix or Next (/pages dir only) handle this serialization by simply\n * ignoring any non-serializable keys, however Nextjs /app directory is stricter and\n * throws an error if a non-serializable value is found.\n */\nexport const makeAuthObjectSerializable = <T extends Record<string, unknown>>(obj: T): T => {\n  // remove any non-serializable props from the returned object\n\n  const { debug, getToken, has, ...rest } = obj as unknown as AuthObject;\n  return rest as unknown as T;\n};\n\ntype TokenFetcher = (sessionId: string, template: string) => Promise<string>;\n\ntype CreateGetToken = (params: { sessionId: string; sessionToken: string; fetcher: TokenFetcher }) => ServerGetToken;\n\nconst createGetToken: CreateGetToken = params => {\n  const { fetcher, sessionToken, sessionId } = params || {};\n\n  return async (options: ServerGetTokenOptions = {}) => {\n    if (!sessionId) {\n      return null;\n    }\n\n    if (options.template) {\n      return fetcher(sessionId, options.template);\n    }\n\n    return sessionToken;\n  };\n};\n\nconst createHasAuthorization =\n  ({\n    orgId,\n    orgRole,\n    userId,\n    orgPermissions,\n  }: {\n    userId: string;\n    orgId: string | undefined;\n    orgRole: string | undefined;\n    orgPermissions: string[] | undefined;\n  }): CheckAuthorizationWithCustomPermissions =>\n  params => {\n    if (!params?.permission && !params?.role) {\n      throw new Error(\n        'Missing parameters. `has` from `auth` or `getAuth` requires a permission or role key to be passed. Example usage: `has({permission: \"org:posts:edit\"`',\n      );\n    }\n\n    if (!orgId || !userId || !orgRole || !orgPermissions) {\n      return false;\n    }\n\n    if (params.permission) {\n      return orgPermissions.includes(params.permission);\n    }\n\n    if (params.role) {\n      return orgRole === params.role;\n    }\n\n    return false;\n  };\n", "export type TokenCarrier = 'header' | 'cookie';\n\nexport enum TokenVerificationErrorCode {\n  InvalidSecretKey = 'clerk_key_invalid',\n}\n\nexport enum TokenVerificationErrorReason {\n  TokenExpired = 'token-expired',\n  TokenInvalid = 'token-invalid',\n  TokenInvalidAlgorithm = 'token-invalid-algorithm',\n  TokenInvalidAuthorizedParties = 'token-invalid-authorized-parties',\n  TokenInvalidIssuer = 'token-invalid-issuer',\n  TokenInvalidSignature = 'token-invalid-signature',\n  TokenNotActiveYet = 'token-not-active-yet',\n  TokenVerificationFailed = 'token-verification-failed',\n  InvalidSecretKey = 'secret-key-invalid',\n\n  LocalJWKMissing = 'jwk-local-missing',\n\n  RemoteJWKFailedToLoad = 'jwk-remote-failed-to-load',\n  RemoteJWKInvalid = 'jwk-remote-invalid',\n  RemoteJWKMissing = 'jwk-remote-missing',\n\n  JWKFailedToResolve = 'jwk-failed-to-resolve',\n\n  RemoteInterstitialFailedToLoad = 'interstitial-remote-failed-to-load',\n}\n\nexport enum TokenVerificationErrorAction {\n  ContactSupport = 'Contact <EMAIL>',\n  EnsureClerkJWT = 'Make sure that this is a valid Clerk generate JWT.',\n  SetClerkJWTKey = 'Set the CLERK_JWT_KEY environment variable.',\n  SetClerkSecretKeyOrAPIKey = 'Set the CLERK_SECRET_KEY or CLERK_API_KEY environment variable.',\n  EnsureClockSync = 'Make sure your system clock is in sync (e.g. turn off and on automatic time synchronization).',\n}\n\nexport class TokenVerificationError extends Error {\n  action?: TokenVerificationErrorAction;\n  reason: TokenVerificationErrorReason;\n  tokenCarrier?: TokenCarrier;\n\n  constructor({\n    action,\n    message,\n    reason,\n  }: {\n    action?: TokenVerificationErrorAction;\n    message: string;\n    reason: TokenVerificationErrorReason;\n  }) {\n    super(message);\n\n    Object.setPrototypeOf(this, TokenVerificationError.prototype);\n\n    this.reason = reason;\n    this.message = message;\n    this.action = action;\n  }\n\n  public getFullMessage() {\n    return `${[this.message, this.action].filter(m => m).join(' ')} (reason=${this.reason}, token-carrier=${\n      this.tokenCarrier\n    })`;\n  }\n}\n", "import type { MultiDomainAndOrProxyPrimitives } from '@clerk/types';\n\nimport { API_VERSION, USER_AGENT } from '../constants';\n// DO NOT CHANGE: Runtime needs to be imported as a default export so that we can stub its dependencies with Sinon.js\n// For more information refer to https://sinonjs.org/how-to/stub-dependency/\nimport runtime from '../runtime';\nimport { joinPaths } from '../util/path';\nimport {\n  addClerkPrefix,\n  callWithRetry,\n  deprecated,\n  getClerkJsMajorVersionOrTag,\n  getScriptUrl,\n  isDevOrStagingUrl,\n  parsePublishableKey,\n} from '../util/shared';\nimport { TokenVerificationError, TokenVerificationErrorAction, TokenVerificationErrorReason } from './errors';\nimport type { DebugRequestSate } from './request';\n\nexport type LoadInterstitialOptions = {\n  apiUrl: string;\n  frontendApi: string;\n  publishableKey: string;\n  clerkJSUrl?: string;\n  clerkJSVersion?: string;\n  userAgent?: string;\n  /**\n   * @deprecated\n   */\n  pkgVersion?: string;\n  debugData?: DebugRequestSate;\n  isSatellite?: boolean;\n  signInUrl?: string;\n} & MultiDomainAndOrProxyPrimitives;\n\nexport function loadInterstitialFromLocal(options: Omit<LoadInterstitialOptions, 'apiUrl'>) {\n  if (options.frontendApi) {\n    deprecated('frontendApi', 'Use `publishableKey` instead.');\n  }\n  if (options.pkgVersion) {\n    deprecated('pkgVersion', 'Use `clerkJSVersion` instead.');\n  }\n\n  options.frontendApi = parsePublishableKey(options.publishableKey)?.frontendApi || options.frontendApi || '';\n  const domainOnlyInProd = !isDevOrStagingUrl(options.frontendApi) ? addClerkPrefix(options.domain) : '';\n  const {\n    debugData,\n    frontendApi,\n    pkgVersion,\n    clerkJSUrl,\n    clerkJSVersion,\n    publishableKey,\n    proxyUrl,\n    isSatellite = false,\n    domain,\n    signInUrl,\n  } = options;\n  return `\n    <head>\n        <meta charset=\"UTF-8\" />\n        <style>\n          @media (prefers-color-scheme: dark) {\n            body {\n              background-color: black;\n            }\n          }\n        </style>\n    </head>\n    <body>\n        <script>\n            window.__clerk_frontend_api = '${frontendApi}';\n            window.__clerk_debug = ${JSON.stringify(debugData || {})};\n            ${proxyUrl ? `window.__clerk_proxy_url = '${proxyUrl}'` : ''}\n            ${domain ? `window.__clerk_domain = '${domain}'` : ''}\n            window.startClerk = async () => {\n                function formRedirect(){\n                    const form = '<form method=\"get\" action=\"\" name=\"redirect\"></form>';\n                    document.body.innerHTML = document.body.innerHTML + form;\n\n                    const searchParams = new URLSearchParams(window.location.search);\n                    for (let paramTuple of searchParams) {\n                        const input = document.createElement(\"input\");\n                        input.type = \"hidden\";\n                        input.name = paramTuple[0];\n                        input.value = paramTuple[1];\n                        document.forms.redirect.appendChild(input);\n                    }\n                    const url = new URL(window.location.origin + window.location.pathname + window.location.hash);\n                    window.history.pushState({}, '', url);\n\n                    document.forms.redirect.action = window.location.pathname + window.location.hash;\n                    document.forms.redirect.submit();\n                }\n\n                const Clerk = window.Clerk;\n                try {\n                    await Clerk.load({\n                        isSatellite: ${isSatellite},\n                        isInterstitial: ${true},\n                        signInUrl: ${signInUrl ? `'${signInUrl}'` : undefined}\n                    });\n                    if(Clerk.loaded){\n                      if(window.location.href.indexOf(\"#\") === -1){\n                        window.location.href = window.location.href;\n                      } else if (window.navigator.userAgent.toLowerCase().includes(\"firefox/\")){\n                          formRedirect();\n                      } else {\n                          window.location.reload();\n                      }\n                    }\n                } catch (err) {\n                    console.error('Clerk: ', err);\n                }\n            };\n            (() => {\n                const script = document.createElement('script');\n                ${\n                  publishableKey\n                    ? `script.setAttribute('data-clerk-publishable-key', '${publishableKey}');`\n                    : `script.setAttribute('data-clerk-frontend-api', '${frontendApi}');`\n                }\n\n                ${domain ? `script.setAttribute('data-clerk-domain', '${domain}');` : ''}\n                ${proxyUrl ? `script.setAttribute('data-clerk-proxy-url', '${proxyUrl}')` : ''};\n                script.async = true;\n                script.src = '${\n                  clerkJSUrl ||\n                  getScriptUrl(proxyUrl || domainOnlyInProd || frontendApi, {\n                    pkgVersion,\n                    clerkJSVersion,\n                  })\n                }';\n                script.crossOrigin = 'anonymous';\n                script.addEventListener('load', startClerk);\n                document.body.appendChild(script);\n            })();\n        </script>\n    </body>\n`;\n}\n\n// TODO: Add caching to Interstitial\nexport async function loadInterstitialFromBAPI(options: LoadInterstitialOptions) {\n  if (options.frontendApi) {\n    deprecated('frontendApi', 'Use `publishableKey` instead.');\n  }\n  if (options.pkgVersion) {\n    deprecated('pkgVersion', 'Use `clerkJSVersion` instead.');\n  }\n  options.frontendApi = parsePublishableKey(options.publishableKey)?.frontendApi || options.frontendApi || '';\n  const url = buildPublicInterstitialUrl(options);\n  const response = await callWithRetry(() =>\n    runtime.fetch(buildPublicInterstitialUrl(options), {\n      method: 'GET',\n      headers: {\n        'Clerk-Backend-SDK': options.userAgent || USER_AGENT,\n      },\n    }),\n  );\n\n  if (!response.ok) {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.ContactSupport,\n      message: `Error loading Clerk Interstitial from ${url} with code=${response.status}`,\n      reason: TokenVerificationErrorReason.RemoteInterstitialFailedToLoad,\n    });\n  }\n\n  return response.text();\n}\n\nexport function buildPublicInterstitialUrl(options: LoadInterstitialOptions) {\n  if (options.frontendApi) {\n    deprecated('frontendApi', 'Use `publishableKey` instead.');\n  }\n\n  options.frontendApi = parsePublishableKey(options.publishableKey)?.frontendApi || options.frontendApi || '';\n  const { apiUrl, frontendApi, pkgVersion, clerkJSVersion, publishableKey, proxyUrl, isSatellite, domain, signInUrl } =\n    options;\n  const url = new URL(apiUrl);\n  url.pathname = joinPaths(url.pathname, API_VERSION, '/public/interstitial');\n  url.searchParams.append('clerk_js_version', clerkJSVersion || getClerkJsMajorVersionOrTag(frontendApi, pkgVersion));\n  if (publishableKey) {\n    url.searchParams.append('publishable_key', publishableKey);\n  } else {\n    url.searchParams.append('frontend_api', frontendApi);\n  }\n  if (proxyUrl) {\n    url.searchParams.append('proxy_url', proxyUrl);\n  }\n\n  if (isSatellite) {\n    url.searchParams.append('is_satellite', 'true');\n  }\n\n  url.searchParams.append('sign_in_url', signInUrl || '');\n\n  if (!isDevOrStagingUrl(options.frontendApi)) {\n    url.searchParams.append('use_domain_for_script', 'true');\n  }\n\n  if (domain) {\n    url.searchParams.append('domain', domain);\n  }\n\n  return url.href;\n}\n", "import { parse } from 'cookie';\n\nimport runtime from '../runtime';\nimport { buildRequestUrl } from '../utils';\n\ntype IsomorphicRequestOptions = (Request: typeof runtime.Request, Headers: typeof runtime.Headers) => Request;\nexport const createIsomorphicRequest = (cb: IsomorphicRequestOptions): Request => {\n  const req = cb(runtime.Request, runtime.Headers);\n  // Used to fix request.url using the x-forwarded-* headers\n  const headersGeneratedURL = buildRequestUrl(req);\n  return new runtime.Request(headersGeneratedURL, req);\n};\n\nexport const buildRequest = (req?: Request) => {\n  if (!req) {\n    return {};\n  }\n  const cookies = parseIsomorphicRequestCookies(req);\n  const headers = getHeaderFromIsomorphicRequest(req);\n  const searchParams = getSearchParamsFromIsomorphicRequest(req);\n\n  return {\n    cookies,\n    headers,\n    searchParams,\n  };\n};\n\nconst decode = (str: string): string => {\n  if (!str) {\n    return str;\n  }\n  return str.replace(/(%[0-9A-Z]{2})+/g, decodeURIComponent);\n};\n\nconst parseIsomorphicRequestCookies = (req: Request) => {\n  const cookies = req.headers && req.headers?.get('cookie') ? parse(req.headers.get('cookie') as string) : {};\n  return (key: string): string | undefined => {\n    const value = cookies?.[key];\n    if (value === undefined) {\n      return undefined;\n    }\n    return decode(value);\n  };\n};\n\nconst getHeaderFromIsomorphicRequest = (req: Request) => (key: string) => req?.headers?.get(key) || undefined;\n\nconst getSearchParamsFromIsomorphicRequest = (req: Request) => (req?.url ? new URL(req.url)?.searchParams : undefined);\n\nexport const stripAuthorizationHeader = (authValue: string | undefined | null): string | undefined => {\n  return authValue?.replace('Bearer ', '');\n};\n", "import { constants } from './constants';\n\nconst getHeader = (req: Request, key: string) => req.headers.get(key);\nconst getFirstValueFromHeader = (value?: string | null) => value?.split(',')[0];\n\ntype BuildRequestUrl = (request: Request, path?: string) => URL;\nexport const buildRequestUrl: BuildRequestUrl = (request, path) => {\n  const initialUrl = new URL(request.url);\n\n  const forwardedProto = getHeader(request, constants.Headers.ForwardedProto);\n  const forwardedHost = getHeader(request, constants.Headers.ForwardedHost);\n  const host = getHeader(request, constants.Headers.Host);\n  const protocol = initialUrl.protocol;\n\n  const base = buildOrigin({ protocol, forwardedProto, forwardedHost, host: host || initialUrl.host });\n\n  return new URL(path || initialUrl.pathname, base);\n};\n\ntype BuildOriginParams = {\n  protocol?: string;\n  forwardedProto?: string | null;\n  forwardedHost?: string | null;\n  host?: string | null;\n};\ntype BuildOrigin = (params: BuildOriginParams) => string;\nexport const buildOrigin: BuildOrigin = ({ protocol, forwardedProto, forwardedHost, host }) => {\n  const resolvedHost = getFirstValueFromHeader(forwardedHost) ?? host;\n  const resolvedProtocol = getFirstValueFromHeader(forwardedProto) ?? protocol?.replace(/[:/]/, '');\n\n  if (!resolvedHost || !resolvedProtocol) {\n    return '';\n  }\n\n  return `${resolvedProtocol}://${resolvedHost}`;\n};\n", "import type { JwtPayload } from '@clerk/types';\n\nimport { createBackendApiClient } from '../api';\nimport type { SignedInAuthObject, SignedOutAuthObject } from './authObjects';\nimport { signedInAuthObject, signedOutAuthObject } from './authObjects';\nimport type { TokenVerificationErrorReason } from './errors';\n\nexport enum AuthStatus {\n  SignedIn = 'signed-in',\n  SignedOut = 'signed-out',\n  Interstitial = 'interstitial',\n  Unknown = 'unknown',\n}\n\nexport type SignedInState = {\n  status: AuthStatus.SignedIn;\n  reason: null;\n  message: null;\n  frontendApi: string;\n  proxyUrl?: string;\n  publishableKey: string;\n  isSatellite: boolean;\n  domain: string;\n  signInUrl: string;\n  signUpUrl: string;\n  afterSignInUrl: string;\n  afterSignUpUrl: string;\n  isSignedIn: true;\n  isInterstitial: false;\n  isUnknown: false;\n  toAuth: () => SignedInAuthObject;\n  token: string;\n};\n\nexport type SignedOutState = {\n  status: AuthStatus.SignedOut;\n  message: string;\n  reason: AuthReason;\n  frontendApi: string;\n  proxyUrl?: string;\n  publishableKey: string;\n  isSatellite: boolean;\n  domain: string;\n  signInUrl: string;\n  signUpUrl: string;\n  afterSignInUrl: string;\n  afterSignUpUrl: string;\n  isSignedIn: false;\n  isInterstitial: false;\n  isUnknown: false;\n  toAuth: () => SignedOutAuthObject;\n  token: null;\n};\n\nexport type InterstitialState = Omit<SignedOutState, 'isInterstitial' | 'status' | 'toAuth'> & {\n  status: AuthStatus.Interstitial;\n  isInterstitial: true;\n  toAuth: () => null;\n};\n\nexport type UnknownState = Omit<InterstitialState, 'status' | 'isInterstitial' | 'isUnknown'> & {\n  status: AuthStatus.Unknown;\n  isInterstitial: false;\n  isUnknown: true;\n};\n\nexport enum AuthErrorReason {\n  CookieAndUATMissing = 'cookie-and-uat-missing',\n  CookieMissing = 'cookie-missing',\n  CookieOutDated = 'cookie-outdated',\n  CookieUATMissing = 'uat-missing',\n  CrossOriginReferrer = 'cross-origin-referrer',\n  HeaderMissingCORS = 'header-missing-cors',\n  HeaderMissingNonBrowser = 'header-missing-non-browser',\n  SatelliteCookieNeedsSyncing = 'satellite-needs-syncing',\n  SatelliteReturnsFromPrimary = 'satellite-returns-from-primary',\n  PrimaryRespondsToSyncing = 'primary-responds-to-syncing',\n  StandardSignedIn = 'standard-signed-in',\n  StandardSignedOut = 'standard-signed-out',\n  UnexpectedError = 'unexpected-error',\n  Unknown = 'unknown',\n}\n\nexport type AuthReason = AuthErrorReason | TokenVerificationErrorReason;\n\nexport type RequestState = SignedInState | SignedOutState | InterstitialState | UnknownState;\n\nexport async function signedIn<T extends { token: string }>(\n  options: T,\n  sessionClaims: JwtPayload,\n): Promise<SignedInState> {\n  const {\n    apiKey,\n    secretKey,\n    apiUrl,\n    apiVersion,\n    cookieToken,\n    frontendApi,\n    proxyUrl,\n    publishableKey,\n    domain,\n    isSatellite,\n    headerToken,\n    loadSession,\n    loadUser,\n    loadOrganization,\n    signInUrl,\n    signUpUrl,\n    afterSignInUrl,\n    afterSignUpUrl,\n    token,\n  } = options as any;\n\n  const { sid: sessionId, org_id: orgId, sub: userId } = sessionClaims;\n\n  const { sessions, users, organizations } = createBackendApiClient({\n    apiKey,\n    secretKey,\n    apiUrl,\n    apiVersion,\n  });\n\n  const [sessionResp, userResp, organizationResp] = await Promise.all([\n    loadSession ? sessions.getSession(sessionId) : Promise.resolve(undefined),\n    loadUser ? users.getUser(userId) : Promise.resolve(undefined),\n    loadOrganization && orgId ? organizations.getOrganization({ organizationId: orgId }) : Promise.resolve(undefined),\n  ]);\n\n  const session = sessionResp;\n  const user = userResp;\n  const organization = organizationResp;\n  // const session = sessionResp && !sessionResp.errors ? sessionResp.data : undefined;\n  // const user = userResp && !userResp.errors ? userResp.data : undefined;\n  // const organization = organizationResp && !organizationResp.errors ? organizationResp.data : undefined;\n\n  const authObject = signedInAuthObject(\n    sessionClaims,\n    {\n      secretKey,\n      apiKey,\n      apiUrl,\n      apiVersion,\n      token: cookieToken || headerToken || '',\n      session,\n      user,\n      organization,\n    },\n    { ...options, status: AuthStatus.SignedIn },\n  );\n\n  return {\n    status: AuthStatus.SignedIn,\n    reason: null,\n    message: null,\n    frontendApi,\n    proxyUrl,\n    publishableKey,\n    domain,\n    isSatellite,\n    signInUrl,\n    signUpUrl,\n    afterSignInUrl,\n    afterSignUpUrl,\n    isSignedIn: true,\n    isInterstitial: false,\n    isUnknown: false,\n    toAuth: () => authObject,\n    token,\n  };\n}\n\nexport function signedOut<T>(options: T, reason: AuthReason, message = ''): SignedOutState {\n  const {\n    frontendApi,\n    publishableKey,\n    proxyUrl,\n    isSatellite,\n    domain,\n    signInUrl,\n    signUpUrl,\n    afterSignInUrl,\n    afterSignUpUrl,\n  } = options as any;\n\n  return {\n    status: AuthStatus.SignedOut,\n    reason,\n    message,\n    frontendApi,\n    proxyUrl,\n    publishableKey,\n    isSatellite,\n    domain,\n    signInUrl,\n    signUpUrl,\n    afterSignInUrl,\n    afterSignUpUrl,\n    isSignedIn: false,\n    isInterstitial: false,\n    isUnknown: false,\n    toAuth: () => signedOutAuthObject({ ...options, status: AuthStatus.SignedOut, reason, message }),\n    token: null,\n  };\n}\n\nexport function interstitial<T>(options: T, reason: AuthReason, message = ''): InterstitialState {\n  const {\n    frontendApi,\n    publishableKey,\n    proxyUrl,\n    isSatellite,\n    domain,\n    signInUrl,\n    signUpUrl,\n    afterSignInUrl,\n    afterSignUpUrl,\n  } = options as any;\n  return {\n    status: AuthStatus.Interstitial,\n    reason,\n    message,\n    frontendApi,\n    publishableKey,\n    isSatellite,\n    domain,\n    proxyUrl,\n    signInUrl,\n    signUpUrl,\n    afterSignInUrl,\n    afterSignUpUrl,\n    isSignedIn: false,\n    isInterstitial: true,\n    isUnknown: false,\n    toAuth: () => null,\n    token: null,\n  };\n}\n\nexport function unknownState<T>(options: T, reason: AuthReason, message = ''): UnknownState {\n  const { frontendApi, publishableKey, isSatellite, domain, signInUrl, signUpUrl, afterSignInUrl, afterSignUpUrl } =\n    options as any;\n  return {\n    status: AuthStatus.Unknown,\n    reason,\n    message,\n    frontendApi,\n    publishableKey,\n    isSatellite,\n    domain,\n    signInUrl,\n    signUpUrl,\n    afterSignInUrl,\n    afterSignUpUrl,\n    isSignedIn: false,\n    isInterstitial: false,\n    isUnknown: true,\n    toAuth: () => null,\n    token: null,\n  };\n}\n", "import { buildOrigin } from '../utils';\n/**\n * This function is only used in the case where:\n * - DevOrStaging key is present\n * - The request carries referrer information\n * (This case most of the times signifies redirect from Clerk Auth pages)\n *\n */\nexport function checkCrossOrigin({\n  originURL,\n  host,\n  forwardedHost,\n  forwardedProto,\n}: {\n  originURL: URL;\n  host?: string | null;\n  forwardedHost?: string | null;\n  forwardedProto?: string | null;\n}) {\n  const finalURL = buildOrigin({ forwardedProto, forwardedHost, protocol: originURL.protocol, host });\n  return finalURL && new URL(finalURL).origin !== originURL.origin;\n}\n\nexport function convertHostHeaderValueToURL(host?: string, protocol = 'https'): URL {\n  /**\n   * The protocol is added for the URL constructor to work properly.\n   * We do not check for the protocol at any point later on.\n   */\n  return new URL(`${protocol}://${host}`);\n}\n\ntype ErrorFields = {\n  message: string;\n  long_message: string;\n  code: string;\n};\n\nexport const getErrorObjectByCode = (errors: ErrorFields[], code: string) => {\n  if (!errors) {\n    return null;\n  }\n\n  return errors.find((err: <PERSON>rro<PERSON><PERSON>ields) => err.code === code);\n};\n", "import { deprecatedObjectProperty } from '@clerk/shared/deprecated';\nimport type { Jwt, JwtPayload } from '@clerk/types';\n\n// DO NOT CHANGE: Runtime needs to be imported as a default export so that we can stub its dependencies with Sinon.js\n// For more information refer to https://sinonjs.org/how-to/stub-dependency/\nimport runtime from '../../runtime';\nimport { base64url } from '../../util/rfc4648';\nimport { deprecated } from '../../util/shared';\nimport { TokenVerificationError, TokenVerificationErrorAction, TokenVerificationErrorReason } from '../errors';\nimport { getCryptoAlgorithm } from './algorithms';\nimport type { IssuerResolver } from './assertions';\nimport {\n  assertActivationClaim,\n  assertAudienceClaim,\n  assertAuthorizedPartiesClaim,\n  assertExpirationClaim,\n  assertHeaderAlgorithm,\n  assertHeaderType,\n  assertIssuedAtClaim,\n  assertIssuerClaim,\n  assertSubClaim,\n} from './assertions';\nimport { importKey } from './cryptoKeys';\n\nconst DEFAULT_CLOCK_SKEW_IN_SECONDS = 5 * 1000;\n\nexport async function hasValidSignature(jwt: Jwt, key: JsonWebKey | string) {\n  const { header, signature, raw } = jwt;\n  const encoder = new TextEncoder();\n  const data = encoder.encode([raw.header, raw.payload].join('.'));\n  const algorithm = getCryptoAlgorithm(header.alg);\n\n  const cryptoKey = await importKey(key, algorithm, 'verify');\n\n  return runtime.crypto.subtle.verify(algorithm.name, cryptoKey, signature, data);\n}\n\nexport function decodeJwt(token: string): Jwt {\n  const tokenParts = (token || '').toString().split('.');\n  if (tokenParts.length !== 3) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenInvalid,\n      message: `Invalid JWT form. A JWT consists of three parts separated by dots.`,\n    });\n  }\n\n  const [rawHeader, rawPayload, rawSignature] = tokenParts;\n\n  const decoder = new TextDecoder();\n\n  // To verify a JWS with SubtleCrypto you need to be careful to encode and decode\n  // the data properly between binary and base64url representation. Unfortunately\n  // the standard implementation in the V8 of btoa() and atob() are difficult to\n  // work with as they use \"a Unicode string containing only characters in the\n  // range U+0000 to U+00FF, each representing a binary byte with values 0x00 to\n  // 0xFF respectively\" as the representation of binary data.\n\n  // A better solution to represent binary data in Javascript is to use ES6 TypedArray\n  // and use a Javascript library to convert them to base64url that honors RFC 4648.\n\n  // Side note: The difference between base64 and base64url is the characters selected\n  // for value 62 and 63 in the standard, base64 encode them to + and / while base64url\n  // encode - and _.\n\n  // More info at https://stackoverflow.com/questions/54062583/how-to-verify-a-signed-jwt-with-subtlecrypto-of-the-web-crypto-API\n  const header = JSON.parse(decoder.decode(base64url.parse(rawHeader, { loose: true })));\n  const payload = JSON.parse(decoder.decode(base64url.parse(rawPayload, { loose: true })));\n  const signature = base64url.parse(rawSignature, { loose: true });\n\n  deprecatedObjectProperty(\n    payload,\n    'orgs',\n    'Add orgs to your session token using the \"user.organizations\" shortcode in JWT Templates instead.',\n    'decodeJwt:orgs',\n  );\n\n  return {\n    header,\n    payload,\n    signature,\n    raw: {\n      header: rawHeader,\n      payload: rawPayload,\n      signature: rawSignature,\n      text: token,\n    },\n  };\n}\n\nexport type VerifyJwtOptions = {\n  audience?: string | string[];\n  authorizedParties?: string[];\n  /**\n   * @deprecated This option incorrectly accepts milliseconds instead of seconds and has been deprecated. Use clockSkewInMs instead.\n   */\n  clockSkewInSeconds?: number;\n  clockSkewInMs?: number;\n  issuer: IssuerResolver | string | null;\n  key: JsonWebKey | string;\n};\n\n// TODO: Revise the return types. Maybe it's better to throw an error instead of return an object with a reason\nexport async function verifyJwt(\n  token: string,\n  { audience, authorizedParties, clockSkewInSeconds, clockSkewInMs, issuer, key }: VerifyJwtOptions,\n): Promise<JwtPayload> {\n  if (clockSkewInSeconds) {\n    deprecated('clockSkewInSeconds', 'Use `clockSkewInMs` instead.');\n  }\n\n  const clockSkew = clockSkewInMs || clockSkewInSeconds || DEFAULT_CLOCK_SKEW_IN_SECONDS;\n\n  const decoded = decodeJwt(token);\n\n  const { header, payload } = decoded;\n\n  // Header verifications\n  const { typ, alg } = header;\n\n  assertHeaderType(typ);\n  assertHeaderAlgorithm(alg);\n\n  // Payload verifications\n  const { azp, sub, aud, iss, iat, exp, nbf } = payload;\n\n  assertSubClaim(sub);\n  assertAudienceClaim([aud], [audience]);\n  assertAuthorizedPartiesClaim(azp, authorizedParties);\n  assertIssuerClaim(iss, issuer);\n  assertExpirationClaim(exp, clockSkew);\n  assertActivationClaim(nbf, clockSkew);\n  assertIssuedAtClaim(iat, clockSkew);\n\n  let signatureValid: boolean;\n\n  try {\n    signatureValid = await hasValidSignature(decoded, key);\n  } catch (err) {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Error verifying JWT signature. ${err}`,\n    });\n  }\n\n  if (!signatureValid) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenInvalidSignature,\n      message: 'JWT signature is invalid.',\n    });\n  }\n\n  return payload;\n}\n", "/**\n * The base64url helper was extracted from the rfc4648 package\n * in order to resolve CSJ/ESM interoperability issues\n *\n * https://github.com/swansontec/rfc4648.js\n *\n * For more context please refer to:\n * - https://github.com/evanw/esbuild/issues/1719\n * - https://github.com/evanw/esbuild/issues/532\n * - https://github.com/swansontec/rollup-plugin-mjs-entry\n */\nexport const base64url = {\n  parse(string: string, opts?: ParseOptions): Uint8Array {\n    return parse(string, base64UrlEncoding, opts);\n  },\n\n  stringify(data: ArrayLike<number>, opts?: StringifyOptions): string {\n    return stringify(data, base64UrlEncoding, opts);\n  },\n};\n\nconst base64UrlEncoding: Encoding = {\n  chars: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_',\n  bits: 6,\n};\n\ninterface Encoding {\n  bits: number;\n  chars: string;\n  codes?: { [char: string]: number };\n}\n\ninterface ParseOptions {\n  loose?: boolean;\n  out?: new (size: number) => { [index: number]: number };\n}\n\ninterface StringifyOptions {\n  pad?: boolean;\n}\n\nfunction parse(string: string, encoding: Encoding, opts: ParseOptions = {}): Uint8Array {\n  // Build the character lookup table:\n  if (!encoding.codes) {\n    encoding.codes = {};\n    for (let i = 0; i < encoding.chars.length; ++i) {\n      encoding.codes[encoding.chars[i]] = i;\n    }\n  }\n\n  // The string must have a whole number of bytes:\n  if (!opts.loose && (string.length * encoding.bits) & 7) {\n    throw new SyntaxError('Invalid padding');\n  }\n\n  // Count the padding bytes:\n  let end = string.length;\n  while (string[end - 1] === '=') {\n    --end;\n\n    // If we get a whole number of bytes, there is too much padding:\n    if (!opts.loose && !(((string.length - end) * encoding.bits) & 7)) {\n      throw new SyntaxError('Invalid padding');\n    }\n  }\n\n  // Allocate the output:\n  const out = new (opts.out ?? Uint8Array)(((end * encoding.bits) / 8) | 0) as Uint8Array;\n\n  // Parse the data:\n  let bits = 0; // Number of bits currently in the buffer\n  let buffer = 0; // Bits waiting to be written out, MSB first\n  let written = 0; // Next byte to write\n  for (let i = 0; i < end; ++i) {\n    // Read one character from the string:\n    const value = encoding.codes[string[i]];\n    if (value === undefined) {\n      throw new SyntaxError('Invalid character ' + string[i]);\n    }\n\n    // Append the bits to the buffer:\n    buffer = (buffer << encoding.bits) | value;\n    bits += encoding.bits;\n\n    // Write out some bits if the buffer has a byte's worth:\n    if (bits >= 8) {\n      bits -= 8;\n      out[written++] = 0xff & (buffer >> bits);\n    }\n  }\n\n  // Verify that we have received just enough bits:\n  if (bits >= encoding.bits || 0xff & (buffer << (8 - bits))) {\n    throw new SyntaxError('Unexpected end of data');\n  }\n\n  return out;\n}\n\nfunction stringify(data: ArrayLike<number>, encoding: Encoding, opts: StringifyOptions = {}): string {\n  const { pad = true } = opts;\n  const mask = (1 << encoding.bits) - 1;\n  let out = '';\n\n  let bits = 0; // Number of bits currently in the buffer\n  let buffer = 0; // Bits waiting to be written out, MSB first\n  for (let i = 0; i < data.length; ++i) {\n    // Slurp data into the buffer:\n    buffer = (buffer << 8) | (0xff & data[i]);\n    bits += 8;\n\n    // Write out as much as we can:\n    while (bits > encoding.bits) {\n      bits -= encoding.bits;\n      out += encoding.chars[mask & (buffer >> bits)];\n    }\n  }\n\n  // Partial character:\n  if (bits) {\n    out += encoding.chars[mask & (buffer << (encoding.bits - bits))];\n  }\n\n  // Add padding characters until we hit a byte boundary:\n  if (pad) {\n    while ((out.length * encoding.bits) & 7) {\n      out += '=';\n    }\n  }\n\n  return out;\n}\n", "const algToHash: Record<string, string> = {\n  RS256: 'SHA-256',\n  RS384: 'SHA-384',\n  RS512: 'SHA-512',\n};\nconst RSA_ALGORITHM_NAME = 'RSASSA-PKCS1-v1_5';\n\nconst jwksAlgToCryptoAlg: Record<string, string> = {\n  RS256: RSA_ALGORITHM_NAME,\n  RS384: RSA_ALGORITHM_NAME,\n  RS512: RSA_ALGORITHM_NAME,\n};\n\nexport const algs = Object.keys(algToHash);\n\nexport function getCryptoAlgorithm(algorithmName: string): RsaHashedImportParams {\n  const hash = algToHash[algorithmName];\n  const name = jwksAlgToCryptoAlg[algorithmName];\n\n  if (!hash || !name) {\n    throw new Error(`Unsupported algorithm ${algorithmName}, expected one of ${algs.join(',')}.`);\n  }\n\n  return {\n    hash: { name: algToHash[algorithmName] },\n    name: jwksAlgToCryptoAlg[algorithmName],\n  };\n}\n", "import { TokenVerificationError, TokenVerificationErrorAction, TokenVerificationErrorReason } from '../errors';\nimport { algs } from './algorithms';\n\nexport type IssuerResolver = string | ((iss: string) => boolean);\n\nconst isArrayString = (s: unknown): s is string[] => {\n  return Array.isArray(s) && s.length > 0 && s.every(a => typeof a === 'string');\n};\n\nexport const assertAudienceClaim = (aud?: unknown, audience?: unknown) => {\n  const audienceList = [audience].flat().filter(a => !!a);\n  const audList = [aud].flat().filter(a => !!a);\n  const shouldVerifyAudience = audienceList.length > 0 && audList.length > 0;\n\n  if (!shouldVerifyAudience) {\n    // Notice: Clerk JWTs use AZP claim instead of Audience\n    //\n    // return {\n    //   valid: false,\n    //   reason: `Invalid JWT audience claim (aud) ${JSON.stringify(\n    //     aud,\n    //   )}. Expected a string or a non-empty array of strings.`,\n    // };\n    return;\n  }\n\n  if (typeof aud === 'string') {\n    if (!audienceList.includes(aud)) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.EnsureClerkJWT,\n        reason: TokenVerificationErrorReason.TokenVerificationFailed,\n        message: `Invalid JWT audience claim (aud) ${JSON.stringify(aud)}. Is not included in \"${JSON.stringify(\n          audienceList,\n        )}\".`,\n      });\n    }\n  } else if (isArrayString(aud)) {\n    if (!aud.some(a => audienceList.includes(a))) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.EnsureClerkJWT,\n        reason: TokenVerificationErrorReason.TokenVerificationFailed,\n        message: `Invalid JWT audience claim array (aud) ${JSON.stringify(aud)}. Is not included in \"${JSON.stringify(\n          audienceList,\n        )}\".`,\n      });\n    }\n  }\n};\n\nexport const assertHeaderType = (typ?: unknown) => {\n  if (typeof typ === 'undefined') {\n    return;\n  }\n\n  if (typ !== 'JWT') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenInvalid,\n      message: `Invalid JWT type ${JSON.stringify(typ)}. Expected \"JWT\".`,\n    });\n  }\n};\n\nexport const assertHeaderAlgorithm = (alg: string) => {\n  if (!algs.includes(alg)) {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenInvalidAlgorithm,\n      message: `Invalid JWT algorithm ${JSON.stringify(alg)}. Supported: ${algs}.`,\n    });\n  }\n};\n\nexport const assertSubClaim = (sub?: string) => {\n  if (typeof sub !== 'string') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Subject claim (sub) is required and must be a string. Received ${JSON.stringify(sub)}.`,\n    });\n  }\n};\n\nexport const assertAuthorizedPartiesClaim = (azp?: string, authorizedParties?: string[]) => {\n  if (!azp || !authorizedParties || authorizedParties.length === 0) {\n    return;\n  }\n\n  if (!authorizedParties.includes(azp)) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenInvalidAuthorizedParties,\n      message: `Invalid JWT Authorized party claim (azp) ${JSON.stringify(azp)}. Expected \"${authorizedParties}\".`,\n    });\n  }\n};\n\nexport const assertIssuerClaim = (iss: string, issuer: IssuerResolver | null) => {\n  if (typeof issuer === 'function' && !issuer(iss)) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenInvalidIssuer,\n      message: 'Failed JWT issuer resolver. Make sure that the resolver returns a truthy value.',\n    });\n  } else if (typeof issuer === 'string' && iss && iss !== issuer) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenInvalidIssuer,\n      message: `Invalid JWT issuer claim (iss) ${JSON.stringify(iss)}. Expected \"${issuer}\".`,\n    });\n  }\n};\n\nexport const assertExpirationClaim = (exp: number, clockSkewInMs: number) => {\n  if (typeof exp !== 'number') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT expiry date claim (exp) ${JSON.stringify(exp)}. Expected number.`,\n    });\n  }\n\n  const currentDate = new Date(Date.now());\n  const expiryDate = new Date(0);\n  expiryDate.setUTCSeconds(exp);\n\n  const expired = expiryDate.getTime() <= currentDate.getTime() - clockSkewInMs;\n  if (expired) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenExpired,\n      message: `JWT is expired. Expiry date: ${expiryDate.toUTCString()}, Current date: ${currentDate.toUTCString()}.`,\n    });\n  }\n};\n\nexport const assertActivationClaim = (nbf: number | undefined, clockSkewInMs: number) => {\n  if (typeof nbf === 'undefined') {\n    return;\n  }\n\n  if (typeof nbf !== 'number') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT not before date claim (nbf) ${JSON.stringify(nbf)}. Expected number.`,\n    });\n  }\n\n  const currentDate = new Date(Date.now());\n  const notBeforeDate = new Date(0);\n  notBeforeDate.setUTCSeconds(nbf);\n\n  const early = notBeforeDate.getTime() > currentDate.getTime() + clockSkewInMs;\n  if (early) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenNotActiveYet,\n      message: `JWT cannot be used prior to not before date claim (nbf). Not before date: ${notBeforeDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`,\n    });\n  }\n};\n\nexport const assertIssuedAtClaim = (iat: number | undefined, clockSkewInMs: number) => {\n  if (typeof iat === 'undefined') {\n    return;\n  }\n\n  if (typeof iat !== 'number') {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.EnsureClerkJWT,\n      reason: TokenVerificationErrorReason.TokenVerificationFailed,\n      message: `Invalid JWT issued at date claim (iat) ${JSON.stringify(iat)}. Expected number.`,\n    });\n  }\n\n  const currentDate = new Date(Date.now());\n  const issuedAtDate = new Date(0);\n  issuedAtDate.setUTCSeconds(iat);\n\n  const postIssued = issuedAtDate.getTime() > currentDate.getTime() + clockSkewInMs;\n  if (postIssued) {\n    throw new TokenVerificationError({\n      reason: TokenVerificationErrorReason.TokenNotActiveYet,\n      message: `JWT issued at date claim (iat) is in the future. Issued at date: ${issuedAtDate.toUTCString()}; Current date: ${currentDate.toUTCString()};`,\n    });\n  }\n};\n", "import { isomorphicAtob } from '@clerk/shared/isomorphicAtob';\n\nimport runtime from '../../runtime';\n\n// https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/importKey#pkcs_8_import\nfunction pemToBuffer(secret: string): ArrayBuffer {\n  const trimmed = secret\n    .replace(/-----BEGIN.*?-----/g, '')\n    .replace(/-----END.*?-----/g, '')\n    .replace(/\\s/g, '');\n\n  const decoded = isomorphicAtob(trimmed);\n\n  const buffer = new ArrayBuffer(decoded.length);\n  const bufView = new Uint8Array(buffer);\n\n  for (let i = 0, strLen = decoded.length; i < strLen; i++) {\n    bufView[i] = decoded.charCodeAt(i);\n  }\n\n  return bufView;\n}\n\nexport function importKey(\n  key: JsonWebKey | string,\n  algorithm: RsaHashedImportParams,\n  keyUsage: 'verify' | 'sign',\n): Promise<CryptoKey> {\n  if (typeof key === 'object') {\n    return runtime.crypto.subtle.importKey('jwk', key, algorithm, false, [keyUsage]);\n  }\n\n  const keyData = pemToBuffer(key);\n  const format = keyUsage === 'sign' ? 'pkcs8' : 'spki';\n\n  return runtime.crypto.subtle.importKey(format, keyData, algorithm, false, [keyUsage]);\n}\n", "import runtime from '../../runtime';\nimport { base64url } from '../../util/rfc4648';\nimport { getCryptoAlgorithm } from './algorithms';\nimport { importKey } from './cryptoKeys';\n\nexport interface SignJwtOptions {\n  algorithm?: string;\n  header?: Record<string, unknown>;\n}\n\nfunction encodeJwtData(value: unknown): string {\n  const stringified = JSON.stringify(value);\n  const encoder = new TextEncoder();\n  const encoded = encoder.encode(stringified);\n  return base64url.stringify(encoded, { pad: false });\n}\n\n/**\n * Signs a JSON Web Token (JWT) with the given payload, key, and options.\n * This function is intended to be used *internally* by other Clerk packages and typically\n * should not be used directly.\n *\n * @internal\n * @param payload The payload to include in the JWT.\n * @param key The key to use for signing the JWT. Can be a string or a JsonWebKey.\n * @param options The options to use for signing the JWT.\n * @returns A Promise that resolves to the signed JWT string.\n * @throws An error if no algorithm is specified or if the specified algorithm is unsupported.\n * @throws An error if there is an issue with importing the key or signing the JWT.\n */\nexport async function signJwt(\n  payload: Record<string, unknown>,\n  key: string | JsonWebKey,\n  options: SignJwtOptions,\n): Promise<string> {\n  if (!options.algorithm) {\n    throw new Error('No algorithm specified');\n  }\n  const encoder = new TextEncoder();\n\n  const algorithm = getCryptoAlgorithm(options.algorithm);\n  if (!algorithm) {\n    throw new Error(`Unsupported algorithm ${options.algorithm}`);\n  }\n\n  const cryptoKey = await importKey(key, algorithm, 'sign');\n  const header = options.header || { typ: 'JWT' };\n\n  header.alg = options.algorithm;\n  payload.iat = Math.floor(Date.now() / 1000);\n\n  const encodedHeader = encodeJwtData(header);\n  const encodedPayload = encodeJwtData(payload);\n  const firstPart = `${encodedHeader}.${encodedPayload}`;\n\n  const signature = await runtime.crypto.subtle.sign(algorithm, cryptoKey, encoder.encode(firstPart));\n\n  return `${firstPart}.${base64url.stringify(new Uint8Array(signature), { pad: false })}`;\n}\n", "import { API_URL, API_VERSION, MAX_CACHE_LAST_UPDATED_AT_SECONDS } from '../constants';\n// DO NOT CHANGE: Runtime needs to be imported as a default export so that we can stub its dependencies with Sinon.js\n// For more information refer to https://sinonjs.org/how-to/stub-dependency/\nimport runtime from '../runtime';\nimport { joinPaths } from '../util/path';\nimport { getErrorObjectByCode } from '../util/request';\nimport { callWithRetry } from '../util/shared';\nimport {\n  TokenVerificationError,\n  TokenVerificationErrorAction,\n  TokenVerificationErrorCode,\n  TokenVerificationErrorReason,\n} from './errors';\n\ntype JsonWebKeyWithKid = JsonWebKey & { kid: string };\n\ntype JsonWebKeyCache = Record<string, JsonWebKeyWithKid>;\n\nlet cache: JsonWebKeyCache = {};\nlet lastUpdatedAt = 0;\n\nfunction getFromCache(kid: string) {\n  return cache[kid];\n}\n\nfunction getCacheValues() {\n  return Object.values(cache);\n}\n\nfunction setInCache(jwk: JsonWebKeyWithKid, shouldExpire = true) {\n  cache[jwk.kid] = jwk;\n  lastUpdatedAt = shouldExpire ? Date.now() : -1;\n}\n\nconst LocalJwkKid = 'local';\nconst PEM_HEADER = '-----BEGIN PUBLIC KEY-----';\nconst PEM_TRAILER = '-----END PUBLIC KEY-----';\nconst RSA_PREFIX = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA';\nconst RSA_SUFFIX = 'IDAQAB';\n\n/**\n *\n * Loads a local PEM key usually from process.env and transform it to JsonWebKey format.\n * The result is also cached on the module level to avoid unnecessary computations in subsequent invocations.\n *\n * @param {string} localKey\n * @returns {JsonWebKey} key\n */\nexport function loadClerkJWKFromLocal(localKey?: string): JsonWebKey {\n  if (!getFromCache(LocalJwkKid)) {\n    if (!localKey) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.SetClerkJWTKey,\n        message: 'Missing local JWK.',\n        reason: TokenVerificationErrorReason.LocalJWKMissing,\n      });\n    }\n\n    const modulus = localKey\n      .replace(/(\\r\\n|\\n|\\r)/gm, '')\n      .replace(PEM_HEADER, '')\n      .replace(PEM_TRAILER, '')\n      .replace(RSA_PREFIX, '')\n      .replace(RSA_SUFFIX, '')\n      .replace(/\\+/g, '-')\n      .replace(/\\//g, '_');\n\n    // JWK https://datatracker.ietf.org/doc/html/rfc7517\n    setInCache(\n      {\n        kid: 'local',\n        kty: 'RSA',\n        alg: 'RS256',\n        n: modulus,\n        e: 'AQAB',\n      },\n      false, // local key never expires in cache\n    );\n  }\n\n  return getFromCache(LocalJwkKid);\n}\n\nexport type LoadClerkJWKFromRemoteOptions = {\n  kid: string;\n  jwksCacheTtlInMs?: number;\n  skipJwksCache?: boolean;\n  secretKey?: string;\n  /**\n   * @deprecated Use `secretKey` instead.\n   */\n  apiKey?: string;\n  apiUrl?: string;\n  apiVersion?: string;\n  issuer?: string;\n};\n\n/**\n *\n * Loads a key from JWKS retrieved from the well-known Frontend API endpoint of the issuer.\n * The result is also cached on the module level to avoid network requests in subsequent invocations.\n * The cache lasts 1 hour by default.\n *\n * @param {Object} options\n * @param {string} options.issuer - The issuer origin of the JWT\n * @param {string} options.kid - The id of the key that the JWT was signed with\n * @param {string} options.alg - The algorithm of the JWT\n * @param {number} options.jwksCacheTtlInMs - The TTL of the jwks cache (defaults to 1 hour)\n * @returns {JsonWebKey} key\n */\nexport async function loadClerkJWKFromRemote({\n  apiKey,\n  secretKey,\n  apiUrl = API_URL,\n  apiVersion = API_VERSION,\n  issuer,\n  kid,\n  skipJwksCache,\n}: LoadClerkJWKFromRemoteOptions): Promise<JsonWebKey> {\n  if (cacheHasExpired()) {\n    cache = {};\n  }\n  const shouldRefreshCache = !getFromCache(kid);\n  if (skipJwksCache || shouldRefreshCache) {\n    let fetcher;\n    const key = secretKey || apiKey;\n\n    if (key) {\n      fetcher = () => fetchJWKSFromBAPI(apiUrl, key, apiVersion);\n    } else if (issuer) {\n      fetcher = () => fetchJWKSFromFAPI(issuer);\n    } else {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: 'Failed to load JWKS from Clerk Backend or Frontend API.',\n        reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n      });\n    }\n\n    const { keys } = await callWithRetry<{ keys: JsonWebKeyWithKid[] }>(fetcher);\n\n    if (!keys || !keys.length) {\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: 'The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.',\n        reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n      });\n    }\n\n    keys.forEach(key => setInCache(key));\n  }\n\n  const jwk = getFromCache(kid);\n\n  if (!jwk) {\n    const cacheValues = getCacheValues();\n    const jwkKeys = cacheValues.map(jwk => jwk.kid).join(', ');\n\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.ContactSupport,\n      message: `Unable to find a signing key in JWKS that matches the kid='${kid}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT.${\n        jwkKeys ? ` The following kid are available: ${jwkKeys}` : ''\n      }`,\n      reason: TokenVerificationErrorReason.RemoteJWKMissing,\n    });\n  }\n\n  return jwk;\n}\n\nasync function fetchJWKSFromFAPI(issuer: string) {\n  const url = new URL(issuer);\n  url.pathname = joinPaths(url.pathname, '.well-known/jwks.json');\n\n  const response = await runtime.fetch(url.href);\n\n  if (!response.ok) {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.ContactSupport,\n      message: `Error loading Clerk JWKS from ${url.href} with code=${response.status}`,\n      reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n    });\n  }\n\n  return response.json();\n}\n\nasync function fetchJWKSFromBAPI(apiUrl: string, key: string, apiVersion: string) {\n  if (!key) {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.SetClerkSecretKeyOrAPIKey,\n      message:\n        'Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.',\n      reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n    });\n  }\n\n  const url = new URL(apiUrl);\n  url.pathname = joinPaths(url.pathname, apiVersion, '/jwks');\n\n  const response = await runtime.fetch(url.href, {\n    headers: {\n      Authorization: `Bearer ${key}`,\n      'Content-Type': 'application/json',\n    },\n  });\n\n  if (!response.ok) {\n    const json = await response.json();\n    const invalidSecretKeyError = getErrorObjectByCode(json?.errors, TokenVerificationErrorCode.InvalidSecretKey);\n\n    if (invalidSecretKeyError) {\n      const reason = TokenVerificationErrorReason.InvalidSecretKey;\n\n      throw new TokenVerificationError({\n        action: TokenVerificationErrorAction.ContactSupport,\n        message: invalidSecretKeyError.message,\n        reason,\n      });\n    }\n\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.ContactSupport,\n      message: `Error loading Clerk JWKS from ${url.href} with code=${response.status}`,\n      reason: TokenVerificationErrorReason.RemoteJWKFailedToLoad,\n    });\n  }\n\n  return response.json();\n}\n\nfunction cacheHasExpired() {\n  // If lastUpdatedAt is -1, it means that we're using a local JWKS and it never expires\n  if (lastUpdatedAt === -1) {\n    return false;\n  }\n\n  // If the cache has expired, clear the value so we don't attempt to make decisions based on stale data\n  const isExpired = Date.now() - lastUpdatedAt >= MAX_CACHE_LAST_UPDATED_AT_SECONDS * 1000;\n\n  return isExpired;\n}\n", "import type { JwtPayload } from '@clerk/types';\n\nimport { deprecated } from '../util/shared';\nimport { TokenVerificationError, TokenVerificationErrorAction, TokenVerificationErrorReason } from './errors';\nimport type { VerifyJwtOptions } from './jwt';\nimport { decodeJwt, verifyJwt } from './jwt';\nimport type { LoadClerkJWKFromRemoteOptions } from './keys';\nimport { loadClerkJWKFromLocal, loadClerkJWKFromRemote } from './keys';\n\n/**\n *\n */\nexport type VerifyTokenOptions = Pick<\n  VerifyJwtOptions,\n  'authorizedParties' | 'audience' | 'issuer' | 'clockSkewInSeconds' | 'clockSkewInMs'\n> & { jwtKey?: string; proxyUrl?: string } & Pick<\n    LoadClerkJWKFromRemoteOptions,\n    'apiKey' | 'secretKey' | 'apiUrl' | 'apiVersion' | 'jwksCacheTtlInMs' | 'skipJwksCache'\n  >;\n\nexport async function verifyToken(token: string, options: VerifyTokenOptions): Promise<JwtPayload> {\n  const {\n    apiKey,\n    secretKey,\n    apiUrl,\n    apiVersion,\n    audience,\n    authorizedParties,\n    clockSkewInSeconds,\n    clockSkewInMs,\n    issuer,\n    jwksCacheTtlInMs,\n    jwtKey,\n    skipJwksCache,\n  } = options;\n\n  if (options.apiKey) {\n    deprecated('apiKey', 'Use `secretKey` instead.');\n  }\n\n  const { header } = decodeJwt(token);\n  const { kid } = header;\n\n  let key;\n\n  if (jwtKey) {\n    key = loadClerkJWKFromLocal(jwtKey);\n  } else if (typeof issuer === 'string') {\n    // Fetch JWKS from Frontend API if an issuer of type string has been provided\n    key = await loadClerkJWKFromRemote({ issuer, kid, jwksCacheTtlInMs, skipJwksCache });\n  } else if (apiKey || secretKey) {\n    // Fetch JWKS from Backend API using the key\n    key = await loadClerkJWKFromRemote({ apiKey, secretKey, apiUrl, apiVersion, kid, jwksCacheTtlInMs, skipJwksCache });\n  } else {\n    throw new TokenVerificationError({\n      action: TokenVerificationErrorAction.SetClerkJWTKey,\n      message: 'Failed to resolve JWK during verification.',\n      reason: TokenVerificationErrorReason.JWKFailedToResolve,\n    });\n  }\n\n  return await verifyJwt(token, {\n    audience,\n    authorizedParties,\n    clockSkewInSeconds,\n    clockSkewInMs,\n    key,\n    issuer,\n  });\n}\n", "import { checkCross<PERSON>rigin } from '../util/request';\nimport { isDevelopmentFromApi<PERSON>ey, isProductionFromApi<PERSON>ey } from '../util/shared';\nimport type { RequestState } from './authStatus';\nimport { AuthErrorReason, interstitial, signedIn, signedOut } from './authStatus';\nimport type { AuthenticateRequestOptions } from './request';\nimport { verifyToken } from './verify';\n\ntype InterstitialRuleResult = RequestState | undefined;\ntype InterstitialRule = <T extends AuthenticateRequestOptions>(\n  opts: T,\n) => Promise<InterstitialRuleResult> | InterstitialRuleResult;\n\nconst shouldRedirectToSatelliteUrl = (qp?: URLSearchParams) => !!qp?.get('__clerk_satellite_url');\nconst hasJustSynced = (qp?: URLSearchParams) => qp?.get('__clerk_synced') === 'true';\n\nconst VALID_USER_AGENTS = /^Mozilla\\/|(Amazon CloudFront)/;\n\nconst isBrowser = (userAgent: string | undefined) => VALID_USER_AGENTS.test(userAgent || '');\n\n// In development or staging environments only, based on the request's\n// User Agent, detect non-browser requests (e.g. scripts). Since there\n// is no Authorization header, consider the user as signed out and\n// prevent interstitial rendering\n// In production, script requests will be missing both uat and session cookies, which will be\n// automatically treated as signed out. This exception is needed for development, because the any // missing uat throws an interstitial in development.\nexport const nonBrowserRequestInDevRule: InterstitialRule = options => {\n  const { apiKey, secretKey, userAgent } = options;\n  const key = secretKey || apiKey || '';\n  if (isDevelopmentFromApiKey(key) && !isBrowser(userAgent)) {\n    return signedOut(options, AuthErrorReason.HeaderMissingNonBrowser);\n  }\n  return undefined;\n};\n\nexport const crossOriginRequestWithoutHeader: InterstitialRule = options => {\n  const { origin, host, forwardedHost, forwardedProto } = options;\n  const isCrossOrigin =\n    origin &&\n    checkCrossOrigin({\n      originURL: new URL(origin),\n      host,\n      forwardedHost,\n      forwardedProto,\n    });\n\n  if (isCrossOrigin) {\n    return signedOut(options, AuthErrorReason.HeaderMissingCORS);\n  }\n  return undefined;\n};\n\nexport const isPrimaryInDevAndRedirectsToSatellite: InterstitialRule = options => {\n  const { apiKey, secretKey, isSatellite, searchParams } = options;\n  const key = secretKey || apiKey || '';\n  const isDev = isDevelopmentFromApiKey(key);\n\n  if (isDev && !isSatellite && shouldRedirectToSatelliteUrl(searchParams)) {\n    return interstitial(options, AuthErrorReason.PrimaryRespondsToSyncing);\n  }\n  return undefined;\n};\n\nexport const potentialFirstLoadInDevWhenUATMissing: InterstitialRule = options => {\n  const { apiKey, secretKey, clientUat } = options;\n  const key = secretKey || apiKey || '';\n  const res = isDevelopmentFromApiKey(key);\n  if (res && !clientUat) {\n    return interstitial(options, AuthErrorReason.CookieUATMissing);\n  }\n  return undefined;\n};\n\n/**\n * NOTE: Exclude any satellite app that has just synced from throwing an interstitial.\n * It is expected that a primary app will trigger a redirect back to the satellite app.\n */\nexport const potentialRequestAfterSignInOrOutFromClerkHostedUiInDev: InterstitialRule = options => {\n  const { apiKey, secretKey, referrer, host, forwardedHost, forwardedProto } = options;\n  const crossOriginReferrer =\n    referrer && checkCrossOrigin({ originURL: new URL(referrer), host, forwardedHost, forwardedProto });\n  const key = secretKey || apiKey || '';\n\n  if (isDevelopmentFromApiKey(key) && crossOriginReferrer) {\n    return interstitial(options, AuthErrorReason.CrossOriginReferrer);\n  }\n  return undefined;\n};\n\nexport const potentialFirstRequestOnProductionEnvironment: InterstitialRule = options => {\n  const { apiKey, secretKey, clientUat, cookieToken } = options;\n  const key = secretKey || apiKey || '';\n\n  if (isProductionFromApiKey(key) && !clientUat && !cookieToken) {\n    return signedOut(options, AuthErrorReason.CookieAndUATMissing);\n  }\n  return undefined;\n};\n\n// TBD: Can enable if we do not want the __session cookie to be inspected.\n// const signedOutOnDifferentSubdomainButCookieNotRemovedYet: AuthStateRule = (options, key) => {\n//   if (isProduction(key) && !options.clientUat && !options.cookieToken) {\n//     return { status: AuthStatus.Interstitial, errorReason: '' as any };\n//   }\n// };\nexport const isNormalSignedOutState: InterstitialRule = options => {\n  const { clientUat } = options;\n  if (clientUat === '0') {\n    return signedOut(options, AuthErrorReason.StandardSignedOut);\n  }\n  return undefined;\n};\n\n// This happens when a signed in user visits a new subdomain for the first time. The uat will be available because it's set on naked domain, but session will be missing. It can also happen if the cookieToken is manually removed during development.\nexport const hasPositiveClientUatButCookieIsMissing: InterstitialRule = options => {\n  const { clientUat, cookieToken } = options;\n\n  if (clientUat && Number.parseInt(clientUat) > 0 && !cookieToken) {\n    return interstitial(options, AuthErrorReason.CookieMissing);\n  }\n  return undefined;\n};\n\nexport const hasValidHeaderToken: InterstitialRule = async options => {\n  const { headerToken } = options as any;\n  const sessionClaims = await verifyRequestState(options, headerToken);\n  return await signedIn({ ...options, token: headerToken }, sessionClaims);\n};\n\nexport const hasValidCookieToken: InterstitialRule = async options => {\n  const { cookieToken, clientUat } = options as any;\n  const sessionClaims = await verifyRequestState(options, cookieToken);\n  const state = await signedIn({ ...options, token: cookieToken }, sessionClaims);\n\n  const jwt = state.toAuth().sessionClaims;\n  const cookieTokenIsOutdated = jwt.iat < Number.parseInt(clientUat);\n\n  if (!clientUat || cookieTokenIsOutdated) {\n    return interstitial(options, AuthErrorReason.CookieOutDated);\n  }\n\n  return state;\n};\n\nexport async function runInterstitialRules<T extends AuthenticateRequestOptions>(\n  opts: T,\n  rules: InterstitialRule[],\n): Promise<RequestState> {\n  for (const rule of rules) {\n    const res = await rule(opts);\n    if (res) {\n      return res;\n    }\n  }\n\n  return signedOut(opts, AuthErrorReason.UnexpectedError);\n}\n\nasync function verifyRequestState(options: AuthenticateRequestOptions, token: string) {\n  const { isSatellite, proxyUrl } = options;\n  let issuer;\n  if (isSatellite) {\n    issuer = null;\n  } else if (proxyUrl) {\n    issuer = proxyUrl;\n  } else {\n    issuer = (iss: string) => iss.startsWith('https://clerk.') || iss.includes('.clerk.accounts');\n  }\n\n  return verifyToken(token, { ...options, issuer });\n}\n\n/**\n * Avoid throwing this rule for development instances\n * Let the next rule for UatMissing to fire if needed\n */\nexport const isSatelliteAndNeedsSyncing: InterstitialRule = options => {\n  const { clientUat, isSatellite, searchParams, userAgent } = options;\n\n  const isSignedOut = !clientUat || clientUat === '0';\n\n  if (isSatellite && isSignedOut && !isBrowser(userAgent)) {\n    return signedOut(options, AuthErrorReason.SatelliteCookieNeedsSyncing);\n  }\n\n  if (isSatellite && isSignedOut && !hasJustSynced(searchParams)) {\n    return interstitial(options, AuthErrorReason.SatelliteCookieNeedsSyncing);\n  }\n\n  return undefined;\n};\n", "import { API_URL, API_VERSION, constants } from '../constants';\nimport { assertValidSecretKey } from '../util/assertValidSecretKey';\nimport { buildRequest, stripAuthorizationHeader } from '../util/IsomorphicRequest';\nimport { deprecated, isDevelopmentFromApiKey, parsePublishableKey } from '../util/shared';\nimport type { RequestState } from './authStatus';\nimport { AuthErrorReason, interstitial, signedOut, unknownState } from './authStatus';\nimport type { TokenCarrier } from './errors';\nimport { TokenVerificationError, TokenVerificationErrorReason } from './errors';\nimport {\n  crossOriginRequestWithoutHeader,\n  hasPositiveClientUatButCookieIsMissing,\n  hasValidCookieToken,\n  hasValidHeaderToken,\n  isNormalSignedOutState,\n  isPrimaryInDevAndRedirectsToSatellite,\n  isSatelliteAndNeedsSyncing,\n  nonBrowserRequestInDevRule,\n  potentialFirstLoadInDevWhenUATMissing,\n  potentialFirstRequestOnProductionEnvironment,\n  potentialRequestAfterSignInOrOutFromClerkHostedUiInDev,\n  runInterstitialRules,\n} from './interstitialRule';\nimport type { VerifyTokenOptions } from './verify';\n\nexport type LoadResourcesOptions = {\n  loadSession?: boolean;\n  loadUser?: boolean;\n  loadOrganization?: boolean;\n};\n\nexport type RequiredVerifyTokenOptions = Required<\n  Pick<VerifyTokenOptions, 'apiKey' | 'secretKey' | 'apiUrl' | 'apiVersion'>\n>;\n\nexport type OptionalVerifyTokenOptions = Partial<\n  Pick<\n    VerifyTokenOptions,\n    | 'audience'\n    | 'authorizedParties'\n    | 'clockSkewInSeconds'\n    | 'clockSkewInMs'\n    | 'jwksCacheTtlInMs'\n    | 'skipJwksCache'\n    | 'jwtKey'\n  >\n>;\n\nexport type AuthenticateRequestOptions = OptionalVerifyTokenOptions &\n  LoadResourcesOptions & {\n    publishableKey?: string;\n    secretKey?: string;\n    /**\n     * @deprecated Use `publishableKey` instead.\n     */\n    frontendApi?: string;\n    /**\n     * @deprecated Use `secretKey` instead.\n     */\n    apiKey?: string;\n    apiVersion?: string;\n    apiUrl?: string;\n    /* Client token cookie value */\n    cookieToken?: string;\n    /* Client uat cookie value */\n    clientUat?: string;\n    /* Client token header value */\n    headerToken?: string;\n    /* Request origin header value */\n    origin?: string;\n    /* Request host header value */\n    host?: string;\n    /* Request forwarded host value */\n    forwardedHost?: string;\n    /* Request forwarded port value */\n    forwardedPort?: string;\n    /* Request forwarded proto value */\n    forwardedProto?: string;\n    /* Request referrer */\n    referrer?: string;\n    /* Request user-agent value */\n    userAgent?: string;\n    domain?: string;\n    isSatellite?: boolean;\n    proxyUrl?: string;\n    searchParams?: URLSearchParams;\n    signInUrl?: string;\n    signUpUrl?: string;\n    afterSignInUrl?: string;\n    afterSignUpUrl?: string;\n    request?: Request;\n  };\n\nfunction assertSignInUrlExists(signInUrl: string | undefined, key: string): asserts signInUrl is string {\n  if (!signInUrl && isDevelopmentFromApiKey(key)) {\n    throw new Error(`Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite`);\n  }\n}\n\nfunction assertProxyUrlOrDomain(proxyUrlOrDomain: string | undefined) {\n  if (!proxyUrlOrDomain) {\n    throw new Error(`Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl`);\n  }\n}\n\nfunction assertSignInUrlFormatAndOrigin(_signInUrl: string, origin: string) {\n  let signInUrl: URL;\n  try {\n    signInUrl = new URL(_signInUrl);\n  } catch {\n    throw new Error(`The signInUrl needs to have a absolute url format.`);\n  }\n\n  if (signInUrl.origin === origin) {\n    throw new Error(`The signInUrl needs to be on a different origin than your satellite application.`);\n  }\n}\n\nexport async function authenticateRequest(options: AuthenticateRequestOptions): Promise<RequestState> {\n  const { cookies, headers, searchParams } = buildRequest(options?.request);\n\n  if (options.frontendApi) {\n    deprecated('frontendApi', 'Use `publishableKey` instead.');\n  }\n\n  if (options.apiKey) {\n    deprecated('apiKey', 'Use `secretKey` instead.');\n  }\n\n  options = {\n    ...options,\n    ...loadOptionsFromHeaders(options, headers),\n    frontendApi: parsePublishableKey(options.publishableKey)?.frontendApi || options.frontendApi,\n    apiUrl: options.apiUrl || API_URL,\n    apiVersion: options.apiVersion || API_VERSION,\n    cookieToken: options.cookieToken || cookies?.(constants.Cookies.Session),\n    clientUat: options.clientUat || cookies?.(constants.Cookies.ClientUat),\n    searchParams: options.searchParams || searchParams || undefined,\n  };\n\n  assertValidSecretKey(options.secretKey || options.apiKey);\n\n  if (options.isSatellite) {\n    assertSignInUrlExists(options.signInUrl, (options.secretKey || options.apiKey) as string);\n    if (options.signInUrl && options.origin /* could this actually be undefined? */) {\n      assertSignInUrlFormatAndOrigin(options.signInUrl, options.origin);\n    }\n    assertProxyUrlOrDomain(options.proxyUrl || options.domain);\n  }\n\n  async function authenticateRequestWithTokenInHeader() {\n    try {\n      const state = await runInterstitialRules(options, [hasValidHeaderToken]);\n      return state;\n    } catch (err) {\n      return handleError(err, 'header');\n    }\n  }\n\n  async function authenticateRequestWithTokenInCookie() {\n    try {\n      const state = await runInterstitialRules(options, [\n        crossOriginRequestWithoutHeader,\n        nonBrowserRequestInDevRule,\n        isSatelliteAndNeedsSyncing,\n        isPrimaryInDevAndRedirectsToSatellite,\n        potentialFirstRequestOnProductionEnvironment,\n        potentialFirstLoadInDevWhenUATMissing,\n        potentialRequestAfterSignInOrOutFromClerkHostedUiInDev,\n        hasPositiveClientUatButCookieIsMissing,\n        isNormalSignedOutState,\n        hasValidCookieToken,\n      ]);\n\n      return state;\n    } catch (err) {\n      return handleError(err, 'cookie');\n    }\n  }\n\n  function handleError(err: unknown, tokenCarrier: TokenCarrier) {\n    if (err instanceof TokenVerificationError) {\n      err.tokenCarrier = tokenCarrier;\n\n      const reasonToReturnInterstitial = [\n        TokenVerificationErrorReason.TokenExpired,\n        TokenVerificationErrorReason.TokenNotActiveYet,\n      ].includes(err.reason);\n\n      if (reasonToReturnInterstitial) {\n        if (tokenCarrier === 'header') {\n          return unknownState<AuthenticateRequestOptions>(options, err.reason, err.getFullMessage());\n        }\n        return interstitial<AuthenticateRequestOptions>(options, err.reason, err.getFullMessage());\n      }\n      return signedOut<AuthenticateRequestOptions>(options, err.reason, err.getFullMessage());\n    }\n    return signedOut<AuthenticateRequestOptions>(options, AuthErrorReason.UnexpectedError, (err as Error).message);\n  }\n\n  if (options.headerToken) {\n    return authenticateRequestWithTokenInHeader();\n  }\n  return authenticateRequestWithTokenInCookie();\n}\n\nexport const debugRequestState = (params: RequestState) => {\n  const { frontendApi, isSignedIn, proxyUrl, isInterstitial, reason, message, publishableKey, isSatellite, domain } =\n    params;\n  return { frontendApi, isSignedIn, proxyUrl, isInterstitial, reason, message, publishableKey, isSatellite, domain };\n};\n\nexport type DebugRequestSate = ReturnType<typeof debugRequestState>;\n\n/**\n * Load authenticate request options from the options provided or fallback to headers.\n */\nexport const loadOptionsFromHeaders = (\n  options: AuthenticateRequestOptions,\n  headers: ReturnType<typeof buildRequest>['headers'],\n) => {\n  if (!headers) {\n    return {};\n  }\n\n  return {\n    headerToken: stripAuthorizationHeader(options.headerToken || headers(constants.Headers.Authorization)),\n    origin: options.origin || headers(constants.Headers.Origin),\n    host: options.host || headers(constants.Headers.Host),\n    forwardedHost: options.forwardedHost || headers(constants.Headers.ForwardedHost),\n    forwardedPort: options.forwardedPort || headers(constants.Headers.ForwardedPort),\n    forwardedProto:\n      options.forwardedProto ||\n      headers(constants.Headers.CloudFrontForwardedProto) ||\n      headers(constants.Headers.ForwardedProto),\n    referrer: options.referrer || headers(constants.Headers.Referrer),\n    userAgent: options.userAgent || headers(constants.Headers.UserAgent),\n  };\n};\n", "import type { ApiClient } from '../api';\nimport { API_URL, API_VERSION } from '../constants';\nimport type { LoadInterstitialOptions } from './interstitial';\nimport { buildPublicInterstitialUrl, loadInterstitialFromBAPI, loadInterstitialFromLocal } from './interstitial';\nimport type { AuthenticateRequestOptions } from './request';\nimport { authenticateRequest as authenticateRequestOriginal, debugRequestState } from './request';\n\nexport type CreateAuthenticateRequestOptions = {\n  options: Partial<\n    Pick<\n      AuthenticateRequestOptions,\n      | 'audience'\n      | 'apiKey'\n      | 'secretKey'\n      | 'apiUrl'\n      | 'apiVersion'\n      | 'frontendApi'\n      | 'publishableKey'\n      | 'jwtKey'\n      | 'proxyUrl'\n      | 'domain'\n      | 'isSatellite'\n      | 'userAgent'\n    >\n  >;\n  apiClient: ApiClient;\n};\n\nexport function createAuthenticateRequest(params: CreateAuthenticateRequestOptions) {\n  const { apiClient } = params;\n  const {\n    apiKey: buildtimeApiKey = '',\n    secretKey: buildtimeSecretKey = '',\n    jwtKey: buildtimeJwtKey = '',\n    apiUrl = API_URL,\n    apiVersion = API_VERSION,\n    frontendApi: buildtimeFrontendApi = '',\n    proxyUrl: buildProxyUrl = '',\n    publishableKey: buildtimePublishableKey = '',\n    isSatellite: buildtimeIsSatellite = false,\n    domain: buildtimeDomain = '',\n    audience: buildtimeAudience = '',\n    userAgent: buildUserAgent,\n  } = params.options;\n\n  const authenticateRequest = ({\n    apiKey: runtimeApiKey,\n    secretKey: runtimeSecretKey,\n    audience: runtimeAudience,\n    frontendApi: runtimeFrontendApi,\n    proxyUrl: runtimeProxyUrl,\n    publishableKey: runtimePublishableKey,\n    jwtKey: runtimeJwtKey,\n    isSatellite: runtimeIsSatellite,\n    domain: runtimeDomain,\n    searchParams,\n    ...rest\n  }: Omit<AuthenticateRequestOptions, 'apiUrl' | 'apiVersion'>) => {\n    return authenticateRequestOriginal({\n      ...rest,\n      apiKey: runtimeApiKey || buildtimeApiKey,\n      secretKey: runtimeSecretKey || buildtimeSecretKey,\n      audience: runtimeAudience || buildtimeAudience,\n      apiUrl,\n      apiVersion,\n      frontendApi: runtimeFrontendApi || buildtimeFrontendApi,\n      proxyUrl: runtimeProxyUrl || buildProxyUrl,\n      publishableKey: runtimePublishableKey || buildtimePublishableKey,\n      isSatellite: runtimeIsSatellite || buildtimeIsSatellite,\n      domain: runtimeDomain || buildtimeDomain,\n      jwtKey: runtimeJwtKey || buildtimeJwtKey,\n      searchParams,\n    });\n  };\n\n  const localInterstitial = ({\n    frontendApi: runtimeFrontendApi,\n    publishableKey: runtimePublishableKey,\n    proxyUrl: runtimeProxyUrl,\n    isSatellite: runtimeIsSatellite,\n    domain: runtimeDomain,\n    ...rest\n  }: Omit<LoadInterstitialOptions, 'apiUrl'>) =>\n    loadInterstitialFromLocal({\n      ...rest,\n      frontendApi: runtimeFrontendApi || buildtimeFrontendApi,\n      proxyUrl: runtimeProxyUrl || buildProxyUrl,\n      publishableKey: runtimePublishableKey || buildtimePublishableKey,\n      isSatellite: runtimeIsSatellite || buildtimeIsSatellite,\n      domain: runtimeDomain || buildtimeDomain,\n    });\n\n  const remotePublicInterstitial = ({\n    frontendApi: runtimeFrontendApi,\n    publishableKey: runtimePublishableKey,\n    proxyUrl: runtimeProxyUrl,\n    isSatellite: runtimeIsSatellite,\n    domain: runtimeDomain,\n    userAgent: runtimeUserAgent,\n    ...rest\n  }: LoadInterstitialOptions) => {\n    return loadInterstitialFromBAPI({\n      ...rest,\n      apiUrl,\n      frontendApi: runtimeFrontendApi || buildtimeFrontendApi,\n      publishableKey: runtimePublishableKey || buildtimePublishableKey,\n      proxyUrl: runtimeProxyUrl || buildProxyUrl,\n      isSatellite: runtimeIsSatellite || buildtimeIsSatellite,\n      domain: (runtimeDomain || buildtimeDomain) as any,\n      userAgent: runtimeUserAgent || buildUserAgent,\n    });\n  };\n\n  const remotePublicInterstitialUrl = buildPublicInterstitialUrl;\n\n  // TODO: Replace this function with remotePublicInterstitial\n  const remotePrivateInterstitial = () => apiClient.interstitial.getInterstitial();\n\n  return {\n    authenticateRequest,\n    localInterstitial,\n    remotePublicInterstitial,\n    remotePrivateInterstitial,\n    remotePublicInterstitialUrl,\n    debugRequestState,\n  };\n}\n", "import { deprecated, errorThrower, parsePublishableKey } from './util/shared';\n\ntype RedirectAdapter = (url: string) => any;\n\ntype SignUpParams = { returnBackUrl?: string };\ntype SignInParams = { returnBackUrl?: string };\n\nconst buildUrl = (targetUrl: string, redirectUrl?: string) => {\n  let url;\n  if (!targetUrl.startsWith('http')) {\n    if (!redirectUrl || !redirectUrl.startsWith('http')) {\n      throw new Error('destination url or return back url should be an absolute path url!');\n    }\n\n    const baseURL = new URL(redirectUrl);\n    url = new URL(targetUrl, baseURL.origin);\n  } else {\n    url = new URL(targetUrl);\n  }\n\n  if (redirectUrl) {\n    url.searchParams.set('redirect_url', redirectUrl);\n  }\n\n  return url.toString();\n};\n\ntype RedirectParams = {\n  redirectAdapter: RedirectAdapter;\n  signInUrl?: string;\n  signUpUrl?: string;\n  publishableKey?: string;\n  /**\n   * @deprecated Use `publishableKey` instead.\n   */\n  frontendApi?: string;\n};\n\nexport function redirect({ redirectAdapter, signUpUrl, signInUrl, frontendApi, publishableKey }: RedirectParams) {\n  if (!frontendApi) {\n    frontendApi = parsePublishableKey(publishableKey)?.frontendApi;\n  } else {\n    deprecated('frontendApi', 'Use `publishableKey` instead.');\n  }\n\n  const accountsBaseUrl = buildAccountsBaseUrl(frontendApi);\n\n  const redirectToSignUp = ({ returnBackUrl }: SignUpParams = {}) => {\n    if (!signUpUrl && !accountsBaseUrl) {\n      errorThrower.throwMissingPublishableKeyError();\n    }\n\n    const accountsSignUpUrl = `${accountsBaseUrl}/sign-up`;\n    return redirectAdapter(buildUrl(signUpUrl || accountsSignUpUrl, returnBackUrl));\n  };\n\n  const redirectToSignIn = ({ returnBackUrl }: SignInParams = {}) => {\n    if (!signInUrl && !accountsBaseUrl) {\n      errorThrower.throwMissingPublishableKeyError();\n    }\n\n    const accountsSignInUrl = `${accountsBaseUrl}/sign-in`;\n    return redirectAdapter(buildUrl(signInUrl || accountsSignInUrl, returnBackUrl));\n  };\n\n  return { redirectToSignUp, redirectToSignIn };\n}\n\nfunction buildAccountsBaseUrl(frontendApi?: string) {\n  if (!frontendApi) {\n    return '';\n  }\n\n  // convert url from FAPI to accounts for Kima and legacy (prod & dev) instances\n  const accountsBaseUrl = frontendApi\n    // staging accounts\n    .replace(/(clerk\\.accountsstage\\.)/, 'accountsstage.')\n    .replace(/(clerk\\.accounts\\.|clerk\\.)/, 'accounts.');\n  return `https://${accountsBaseUrl}`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAA,qBAAyC;;;ACElC,IAAe,cAAf,MAA2B;AAAA,EAChC,YAAsB,SAA0B;AAA1B;AAAA,EAA2B;AAAA,EAEvC,UAAU,IAAY;AAC9B,QAAI,CAAC,IAAI;AACP,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AAAA,EACF;AACF;;;ACVA,IAAM,YAAY;AAClB,IAAM,2BAA2B,IAAI,OAAO,WAAW,YAAY,QAAQ,GAAG;AAIvE,SAAS,aAAa,MAA4B;AACvD,SAAO,KACJ,OAAO,OAAK,CAAC,EACb,KAAK,SAAS,EACd,QAAQ,0BAA0B,SAAS;AAChD;;;ACNA,IAAM,WAAW;AAOV,IAAM,yBAAN,cAAqC,YAAY;AAAA,EACtD,MAAa,6BAA6B;AACxC,WAAO,KAAK,QAA+B;AAAA,MACzC,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,0BAA0B,QAAyC;AAC9E,WAAO,KAAK,QAA6B;AAAA,MACvC,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,0BAA0B,uBAA+B;AACpE,SAAK,UAAU,qBAAqB;AACpC,WAAO,KAAK,QAA6B;AAAA,MACvC,QAAQ;AAAA,MACR,MAAM,UAAU,UAAU,qBAAqB;AAAA,IACjD,CAAC;AAAA,EACH;AACF;;;AC9BA,IAAMC,YAAW;AAEV,IAAM,YAAN,cAAwB,YAAY;AAAA,EACzC,MAAa,gBAAgB;AAC3B,WAAO,KAAK,QAAkB;AAAA,MAC5B,QAAQ;AAAA,MACR,MAAMA;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,UAAU,UAAkB;AACvC,SAAK,UAAU,QAAQ;AACvB,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,QAAQ;AAAA,IACpC,CAAC;AAAA,EACH;AAAA,EAEO,aAAa,OAAe;AACjC,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,QAAQ;AAAA,MAClC,YAAY,EAAE,MAAM;AAAA,IACtB,CAAC;AAAA,EACH;AACF;;;ACzBA,IAAMC,YAAW;AAEV,IAAM,YAAN,cAAwB,YAAY;AAAA,EACzC,MAAa,aAAa,IAAY;AACpC,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,EAAE;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;;;ACTA,IAAMC,YAAW;AAcV,IAAM,kBAAN,cAA8B,YAAY;AAAA,EAC/C,MAAa,gBAAgB,gBAAwB;AACnD,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,cAAc;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,QAAkC;AAChE,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,gBAAwB,SAAmC,CAAC,GAAG;AAC7F,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,cAAc;AAAA,MACxC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,gBAAwB;AACtD,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,cAAc;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;;;ACtDA,wBAA2B;AAY3B,IAAMC,YAAW;AAKV,IAAM,WAAN,cAAuB,YAAY;AAAA;AAAA;AAAA;AAAA,EAIxC,MAAa,YAAY,QAAqB;AAC5C;AAAA,MACE;AAAA,MACA;AAAA,IACF;AACA,WAAO,KAAK,QAAe;AAAA,MACzB,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;;;AChCA,iBAA0E;AAC1E,2BAA8B;AAC9B,kBAAqF;AACrF,IAAAC,qBAA+C;AAE/C,mBAAkC;AAIlC,IAAAC,eAA2C;AAFpC,IAAM,mBAAe,gCAAkB,EAAE,aAAa,iBAAiB,CAAC;AAGxE,IAAM,EAAE,kBAAkB,QAAI,yCAA2B;;;ACLzD,IAAM,kBAAN,cAA8B,YAAY;AAAA,EAC/C,MAAa,kBAAkB;AAC7B;AAAA,MACE;AAAA,MACA;AAAA,IACF;AAEA,WAAO,KAAK,QAAgB;AAAA,MAC1B,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,cAAc;AAAA,QACZ,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;AChBA,IAAMC,YAAW;AAyBV,IAAM,gBAAN,cAA4B,YAAY;AAAA,EAC7C,MAAa,kBAAkB,SAAkC,CAAC,GAAG;AACnE,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,iBAAiB,QAAsB;AAClD,WAAO,KAAK,QAAoB;AAAA,MAC9B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,iBAAiB,cAAsB;AAClD,SAAK,UAAU,YAAY;AAC3B,WAAO,KAAK,QAAoB;AAAA,MAC9B,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,cAAc,QAAQ;AAAA,IAClD,CAAC;AAAA,EACH;AACF;;;ACrDA,IAAAC,qBAA2B;;;ACe3B,oBAAmB;AAEnB,wBAAmC;AAEnC,IAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI;AAmBJ,IAAM,cAAc,aAAa,KAAK,UAAU;AAGhD,IAAM,UAAmB;AAAA,EACvB,sBAAAC;AAAA,EACA,OAAO;AAAA,EACP,iBAAiB;AAAA,EACjB,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AACZ;AAEA,IAAO,kBAAQ;;;AD/Cf,IAAMC,YAAW;AA8FV,IAAM,kBAAN,cAA8B,YAAY;AAAA,EAC/C,MAAa,oBAAoB,QAAoC;AACnE,WAAO,KAAK,QAAwB;AAAA,MAClC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,QAAsB;AACpD,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,gBAAgB,QAA+B;AAC1D,UAAM,uBAAuB,oBAAoB,SAAS,OAAO,iBAAiB,OAAO;AACzF,SAAK,UAAU,oBAAoB;AAEnC,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,oBAAoB;AAAA,IAChD,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,gBAAwB,QAAsB;AAC5E,SAAK,UAAU,cAAc;AAC7B,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,cAAc;AAAA,MACxC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,uBAAuB,gBAAwB,QAA0B;AACpF,SAAK,UAAU,cAAc;AAE7B,UAAM,WAAW,IAAI,gBAAQ,SAAS;AACtC,aAAS,OAAO,QAAQ,QAAQ,IAAI;AACpC,aAAS,OAAO,oBAAoB,QAAQ,cAAc;AAE1D,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,MAAM;AAAA,MAChD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,uBAAuB,gBAAwB;AAC1D,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,MAAM;AAAA,IAClD,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,2BAA2B,gBAAwB,QAA8B;AAC5F,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,UAAU;AAAA,MACpD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,gBAAwB;AACtD,WAAO,KAAK,QAAsB;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,cAAc;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,8BAA8B,QAA6C;AACtF,UAAM,EAAE,gBAAgB,OAAO,OAAO,IAAI;AAC1C,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAkC;AAAA,MAC5C,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,aAAa;AAAA,MACvD,aAAa,EAAE,OAAO,OAAO;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,6BAA6B,QAA4C;AACpF,UAAM,EAAE,gBAAgB,QAAQ,KAAK,IAAI;AACzC,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,aAAa;AAAA,MACvD,YAAY;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,6BAA6B,QAA4C;AACpF,UAAM,EAAE,gBAAgB,QAAQ,KAAK,IAAI;AACzC,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,eAAe,MAAM;AAAA,MAC/D,YAAY;AAAA,QACV;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,qCAAqC,QAAoD;AACpG,UAAM,EAAE,gBAAgB,QAAQ,gBAAgB,gBAAgB,IAAI;AAEpE,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,eAAe,QAAQ,UAAU;AAAA,MAC3E,YAAY;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,6BAA6B,QAA4C;AACpF,UAAM,EAAE,gBAAgB,OAAO,IAAI;AACnC,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,eAAe,MAAM;AAAA,IACjE,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,8BAA8B,QAA6C;AACtF,UAAM,EAAE,gBAAgB,QAAQ,OAAO,OAAO,IAAI;AAClD,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAkC;AAAA,MAC5C,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,aAAa;AAAA,MACvD,aAAa,EAAE,QAAQ,OAAO,OAAO;AAAA,IACvC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAa,qCAAqC,QAAoD;AACpG,uCAAW,wCAAwC,8CAA8C;AAEjG,UAAM,EAAE,gBAAgB,OAAO,OAAO,IAAI;AAC1C,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAkC;AAAA,MAC5C,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,eAAe,SAAS;AAAA,MAClE,aAAa,EAAE,OAAO,OAAO;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,6BAA6B,QAA4C;AACpF,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,aAAa;AAAA,MACvD,YAAY,EAAE,GAAG,WAAW;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,0BAA0B,QAAyC;AAC9E,UAAM,EAAE,gBAAgB,aAAa,IAAI;AACzC,SAAK,UAAU,cAAc;AAC7B,SAAK,UAAU,YAAY;AAE3B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,eAAe,YAAY;AAAA,IACvE,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,6BAA6B,QAA4C;AACpF,UAAM,EAAE,gBAAgB,cAAc,iBAAiB,IAAI;AAC3D,SAAK,UAAU,cAAc;AAE7B,WAAO,KAAK,QAAgC;AAAA,MAC1C,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,gBAAgB,eAAe,cAAc,QAAQ;AAAA,MAC/E,YAAY;AAAA,QACV;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;AE7SA,IAAMC,YAAW;AAcV,IAAM,iBAAN,cAA6B,YAAY;AAAA,EAC9C,MAAa,eAAe,eAAuB;AACjD,SAAK,UAAU,aAAa;AAE5B,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,aAAa;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,QAAiC;AAC9D,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,eAAuB,SAAkC,CAAC,GAAG;AAC1F,SAAK,UAAU,aAAa;AAE5B,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,aAAa;AAAA,MACvC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,eAAuB;AACpD,SAAK,UAAU,aAAa;AAE5B,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,aAAa;AAAA,IACzC,CAAC;AAAA,EACH;AACF;;;AClDA,IAAMC,YAAW;AAMV,IAAM,iBAAN,cAA6B,YAAY;AAAA,EAC9C,MAAa,qBAAqB;AAChC,WAAO,KAAK,QAAuB;AAAA,MACjC,QAAQ;AAAA,MACR,MAAMA;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,eAAe,eAAuB;AACjD,SAAK,UAAU,aAAa;AAC5B,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,aAAa;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,QAAiC;AAC9D,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,eAAuB;AACpD,SAAK,UAAU,aAAa;AAC5B,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,WAAU,aAAa;AAAA,IACzC,CAAC;AAAA,EACH;AACF;;;AClCA,IAAMC,aAAW;AAQV,IAAM,aAAN,cAAyB,YAAY;AAAA,EAC1C,MAAa,eAAe,aAA2B;AACrD,WAAO,KAAK,QAAmB;AAAA,MAC7B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WAAW,WAAmB;AACzC,SAAK,UAAU,SAAS;AACxB,WAAO,KAAK,QAAiB;AAAA,MAC3B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,SAAS;AAAA,IACrC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,cAAc,WAAmB;AAC5C,SAAK,UAAU,SAAS;AACxB,WAAO,KAAK,QAAiB;AAAA,MAC3B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,WAAW,QAAQ;AAAA,IAC/C,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,cAAc,WAAmB,OAAe;AAC3D,SAAK,UAAU,SAAS;AACxB,WAAO,KAAK,QAAiB;AAAA,MAC3B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,WAAW,QAAQ;AAAA,MAC7C,YAAY,EAAE,MAAM;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,SAAS,WAAmB,UAAkB;AACzD,SAAK,UAAU,SAAS;AACxB,YACG,MAAM,KAAK,QAAe;AAAA,MACzB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,WAAW,UAAU,YAAY,EAAE;AAAA,IAC/D,CAAC,GACD;AAAA,EACJ;AACF;;;ACjDA,IAAMC,aAAW;AAEV,IAAM,iBAAN,cAA6B,YAAY;AAAA,EAC9C,MAAa,kBAAkB,QAAkC;AAC/D,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,kBAAkB,eAAuB;AACpD,SAAK,UAAU,aAAa;AAC5B,WAAO,KAAK,QAAqB;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,eAAe,QAAQ;AAAA,IACnD,CAAC;AAAA,EACH;AACF;;;AC3BA,IAAAC,qBAA2B;AAK3B,IAAMC,aAAW;AAUV,IAAM,gBAAN,cAA4B,YAAY;AAAA;AAAA;AAAA;AAAA,EAI7C,MAAa,iBAAiB,QAAmB;AAC/C;AAAA,MACE;AAAA,MACA;AAAA,IACF;AACA,WAAO,KAAK,QAAoB;AAAA,MAC9B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;;;ACvBA,IAAMC,aAAW;AA4FV,IAAM,UAAN,cAAsB,YAAY;AAAA,EACvC,MAAa,YAAY,SAAyB,CAAC,GAAG;AACpD,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,QAAQ,QAAgB;AACnC,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,MAAM;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WAAW,QAA0B;AAChD,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAMA;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WAAW,QAAgB,SAA2B,CAAC,GAAG;AACrE,SAAK,UAAU,MAAM;AAErB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,MAAM;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,uBAAuB,QAAgB,QAA+B;AACjF,SAAK,UAAU,MAAM;AAErB,UAAM,WAAW,IAAI,gBAAQ,SAAS;AACtC,aAAS,OAAO,QAAQ,QAAQ,IAAI;AAEpC,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,eAAe;AAAA,MACjD;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,mBAAmB,QAAgB,QAA4B;AAC1E,SAAK,UAAU,MAAM;AAErB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,UAAU;AAAA,MAC5C,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WAAW,QAAgB;AACtC,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,MAAM;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,SAAS,SAAyB,CAAC,GAAG;AACjD,WAAO,KAAK,QAAgB;AAAA,MAC1B,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,OAAO;AAAA,MACjC,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,wBAAwB,QAAgB,UAAoC;AACvF,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAA4B;AAAA,MACtC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,uBAAuB,QAAQ;AAAA,IACnE,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,eAAe,QAAgB;AAC1C,SAAK,UAAU,MAAM;AACrB,WAAO,KAAK,QAAc;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,KAAK;AAAA,IACzC,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,8BAA8B,QAA6C;AACtF,UAAM,EAAE,QAAQ,OAAO,OAAO,IAAI;AAClC,SAAK,UAAU,MAAM;AAErB,WAAO,KAAK,QAAkC;AAAA,MAC5C,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,0BAA0B;AAAA,MAC5D,aAAa,EAAE,OAAO,OAAO;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,eAAe,QAA8B;AACxD,UAAM,EAAE,QAAQ,SAAS,IAAI;AAC7B,SAAK,UAAU,MAAM;AAErB,WAAO,KAAK,QAA4B;AAAA,MACtC,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,iBAAiB;AAAA,MACnD,YAAY,EAAE,SAAS;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EAEA,MAAa,WAAW,QAA0B;AAChD,UAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,SAAK,UAAU,MAAM;AAErB,WAAO,KAAK,QAA+C;AAAA,MACzD,QAAQ;AAAA,MACR,MAAM,UAAUA,YAAU,QAAQ,aAAa;AAAA,MAC/C,YAAY,EAAE,KAAK;AAAA,IACrB,CAAC;AAAA,EACH;AACF;;;AC7NA,IAAAC,gBAAsC;AAEtC,uBAAsB;AACtB,4BAA0B;;;ACHnB,IAAM,UAAU;AAChB,IAAM,cAAc;AAEpB,IAAM,aAAa,GAAG,gBAAY,IAAI,SAAe;AACrD,IAAM,oCAAoC,IAAI;AAErD,IAAM,aAAa;AAAA,EACjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AACf;AAEA,IAAM,UAAU;AAAA,EACd,SAAS;AAAA,EACT,WAAW;AACb;AAEA,IAAM,UAAU;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,eAAe;AAAA,EACf,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,aAAa;AACf;AAEA,IAAM,eAAe;AAAA,EACnB,YAAY,QAAQ;AAAA,EACpB,WAAW,QAAQ;AACrB;AAEA,IAAM,eAAe;AAAA,EACnB,MAAM;AACR;AAEO,IAAM,YAAY;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACpDO,SAAS,qBAAqB,KAAqC;AACxE,MAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACnC,UAAM;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAGF;;;ACNO,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EAC/B,YACW,IACA,YACA,WACA,WACA,cACT;AALS;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAoD;AAClE,WAAO,IAAI,qBAAoB,KAAK,IAAI,KAAK,YAAY,KAAK,YAAY,KAAK,YAAY,KAAK,aAAa;AAAA,EAC/G;AACF;;;ACZO,IAAM,UAAN,MAAM,SAAQ;AAAA,EACnB,YACW,IACA,UACA,QACA,QACA,cACA,UACA,WACA,WACA,WACT;AATS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA4B;AAC1C,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACzBO,IAAM,SAAN,MAAM,QAAO;AAAA,EAClB,YACW,IACA,YACA,UACA,UACA,UACA,qBACA,WACA,WACT;AARS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA0B;AACxC,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,SAAS,IAAI,OAAK,QAAQ,SAAS,CAAC,CAAC;AAAA,MAC1C,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACzBO,IAAM,gBAAN,MAAM,eAAc;AAAA,EACzB,YACW,QACA,IACA,MACA,SACT;AAJS;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAyB;AACvC,WAAO,IAAI,eAAc,KAAK,QAAQ,KAAK,MAAM,MAAM,KAAK,QAAQ,MAAM,KAAK,OAAO;AAAA,EACxF;AACF;;;ACXO,IAAM,QAAN,MAAM,OAAM;AAAA,EACjB,YACW,IACA,eACA,gBACA,gBACA,SACA,MACA,WACA,QACA,MACA,MACA,kBACT;AAXS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAwB;AACtC,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AC9BO,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EAC9B,YAAqB,IAAqB,MAAc;AAAnC;AAAqB;AAAA,EAAe;AAAA,EAEzD,OAAO,SAAS,MAAkD;AAChE,WAAO,IAAI,oBAAmB,KAAK,IAAI,KAAK,IAAI;AAAA,EAClD;AACF;;;ACNO,IAAM,eAAN,MAAM,cAAa;AAAA,EACxB,YACW,QACA,UACA,kCAA8C,MAC9C,WAA0B,MAC1B,WAA0B,MAC1B,QAAuB,MAChC;AANS;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAsC;AACpD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,qCAAqC,IAAI,IAAI,KAAK,kCAAkC,IAAI;AAAA,MAC7F,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AClBO,IAAM,eAAN,MAAM,cAAa;AAAA,EACxB,YACW,IACA,cACA,cACA,UACT;AAJS;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAsC;AACpD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,gBAAgB,aAAa,SAAS,KAAK,YAAY;AAAA,MAC5D,KAAK,UAAU,IAAI,UAAQ,mBAAmB,SAAS,IAAI,CAAC;AAAA,IAC9D;AAAA,EACF;AACF;;;AChBO,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EAC3B,YACW,IACA,UACA,kBACA,YACA,gBACA,cACA,WACA,UAIA,SACA,UACA,UACA,iBAAiD,CAAC,GAClD,OACA,cACT;AAjBS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA4C;AAC1D,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,gBAAgB,aAAa,SAAS,KAAK,YAAY;AAAA,IAC9D;AAAA,EACF;AACF;AAAA,IAEA,uCAAmB,iBAAiB,WAAW,yBAAyB;;;AC1CjE,IAAM,aAAN,MAAM,YAAW;AAAA,EACtB,YACW,IACA,cACA,gBACA,WACA,WACA,QACA,SACT;AAPS;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAkC;AAChD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;AChBO,IAAK,aAAL,kBAAKC,gBAAL;AACL,EAAAA,YAAA,yBAAsB;AACtB,EAAAA,YAAA,YAAS;AACT,EAAAA,YAAA,WAAQ;AACR,EAAAA,YAAA,kBAAe;AACf,EAAAA,YAAA,qBAAkB;AAClB,EAAAA,YAAA,qBAAkB;AAClB,EAAAA,YAAA,mBAAgB;AAChB,EAAAA,YAAA,gBAAa;AACb,EAAAA,YAAA,sBAAmB;AACnB,EAAAA,YAAA,kBAAe;AACf,EAAAA,YAAA,4BAAyB;AACzB,EAAAA,YAAA,4BAAyB;AACzB,EAAAA,YAAA,iBAAc;AACd,EAAAA,YAAA,iBAAc;AACd,EAAAA,YAAA,aAAU;AACV,EAAAA,YAAA,mBAAgB;AAChB,EAAAA,YAAA,iBAAc;AACd,EAAAA,YAAA,mBAAgB;AAChB,EAAAA,YAAA,gBAAa;AACb,EAAAA,YAAA,UAAO;AACP,EAAAA,YAAA,gBAAa;AACb,EAAAA,YAAA,WAAQ;AACR,EAAAA,YAAA,gBAAa;AAvBH,SAAAA;AAAA,GAAA;;;ACPL,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EAC5B,YACW,UACA,OACA,iBAA0C,CAAC,GAC3C,OACA,QACA,aACT;AANS;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAA4B;AAC1C,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACnBO,IAAM,eAAN,MAAM,cAAa;AAAA,EACxB,YACW,IACA,MACA,MAIA,SACA,UACA,UACA,WACA,WACA,WACA,iBAAoD,CAAC,GACrD,kBAA+C,CAAC,GAChD,uBACA,oBAIA,eACA,cACT;AArBS;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAsC;AACpD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;AAAA,IAEA,uCAAmB,cAAc,WAAW,yBAAyB;AAAA,IACrE,uCAAmB,cAAc,iBAAiB,6BAA6B;;;AC/CxE,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAClC,YACW,IACA,cACA,MACA,gBACA,WACA,WACA,QACA,iBAAuD,CAAC,GACxD,kBAAyD,CAAC,GACnE;AATS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAkC;AAChD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACxBO,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAClC,YACW,IACA,MACA,iBAAuD,CAAC,GACxD,kBAAyD,CAAC,GAC1D,WACA,WACA,cACA,gBACT;AARS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAkC;AAChD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,aAAa,SAAS,KAAK,YAAY;AAAA,MACvC,qCAAqC,SAAS,KAAK,gBAAgB;AAAA,IACrE;AAAA,EACF;AACF;AAEO,IAAM,uCAAN,MAAM,sCAAqC;AAAA,EAChD,YACW,YACA,WACA,UAIA,iBACA,UACA,UACA,QACT;AAVS;AACA;AACA;AAIA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAgD;AAC9D,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;AAAA,IAEA,uCAAmB,sCAAsC,mBAAmB,yBAAyB;;;ACtD9F,IAAM,cAAN,MAAM,aAAY;AAAA,EACvB,YACW,IACA,aACA,yBACA,qBACA,cACA,UACT;AANS;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAoC;AAClD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,gBAAgB,aAAa,SAAS,KAAK,YAAY;AAAA,MAC5D,KAAK,UAAU,IAAI,UAAQ,mBAAmB,SAAS,IAAI,CAAC;AAAA,IAC9D;AAAA,EACF;AACF;;;ACtBO,IAAM,cAAN,MAAM,aAAY;AAAA,EACvB,YAAqB,IAAqB,KAAsB,WAA4B,WAAmB;AAA1F;AAAqB;AAAsB;AAA4B;AAAA,EAAoB;AAAA,EAEhH,OAAO,SAAS,MAAoC;AAClD,WAAO,IAAI,aAAY,KAAK,IAAI,KAAK,KAAK,KAAK,YAAY,KAAK,UAAU;AAAA,EAC5E;AACF;;;ACNO,IAAM,cAAN,MAAM,aAAY;AAAA,EACvB,YACW,IACA,QACA,OACA,QACA,KACA,WACA,WACT;AAPS;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAoC;AAClD,WAAO,IAAI,aAAY,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO,KAAK,QAAQ,KAAK,KAAK,KAAK,YAAY,KAAK,UAAU;AAAA,EACnH;AACF;;;ACdO,IAAM,aAAN,MAAM,YAAW;AAAA,EACtB,YACW,IACA,iBACA,eACA,SACA,QACA,eACA,MACT;AAPS;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAkC;AAChD,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;;;ACtBO,IAAM,QAAN,MAAM,OAAM;AAAA,EACjB,YAAqB,KAAa;AAAb;AAAA,EAAc;AAAA,EAEnC,OAAO,SAAS,MAAwB;AACtC,WAAO,IAAI,OAAM,KAAK,GAAG;AAAA,EAC3B;AACF;;;ACLO,IAAM,aAAN,MAAM,YAAW;AAAA,EACtB,YAAqB,IAAqB,YAA6B,cAAmC;AAArF;AAAqB;AAA6B;AAAA,EAAoC;AAAA,EAE3G,OAAO,SAAS,MAAkC;AAChD,WAAO,IAAI,YAAW,KAAK,IAAI,KAAK,aAAa,KAAK,gBAAgB,aAAa,SAAS,KAAK,YAAY,CAAC;AAAA,EAChH;AACF;;;ACFO,IAAM,OAAN,MAAM,MAAK;AAAA,EAChB,YACW,IACA,iBACA,aACA,mBACA,kBACA,QACA,WACA,WAIA,iBACA,UACA,UACA,QACA,UACA,uBACA,sBACA,qBACA,cACA,YACA,UACA,WACA,UACA,iBAAqC,CAAC,GACtC,kBAAuC,CAAC,GACxC,iBAAqC,CAAC,GACtC,iBAAiC,CAAC,GAClC,eAA8B,CAAC,GAC/B,cAA4B,CAAC,GAC7B,mBAAsC,CAAC,GACvC,2BACT;AAhCS;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,EACR;AAAA,EAEH,OAAO,SAAS,MAAsB;AACpC,WAAO,IAAI;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,OACJ,KAAK,mBAAmB,CAAC,GAAG,IAAI,OAAK,aAAa,SAAS,CAAC,CAAC;AAAA,OAC7D,KAAK,iBAAiB,CAAC,GAAG,IAAI,OAAK,YAAY,SAAS,CAAC,CAAC;AAAA,OAC1D,KAAK,gBAAgB,CAAC,GAAG,IAAI,OAAK,WAAW,SAAS,CAAC,CAAC;AAAA,OACxD,KAAK,qBAAqB,CAAC,GAAG,IAAI,CAAC,MAA2B,gBAAgB,SAAS,CAAC,CAAC;AAAA,MAC1F,KAAK;AAAA,IACP;AAAA,EACF;AACF;AAAA,IAEA,uCAAmB,MAAM,mBAAmB,yBAAyB;;;ACxD9D,SAAS,YAAY,SAAmB;AAC7C,MAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,WAAO,QAAQ,IAAI,UAAQ,aAAa,IAAI,CAAC;AAAA,EAC/C,WAAW,YAAY,OAAO,GAAG;AAC/B,WAAO,QAAQ,KAAK,IAAI,UAAQ,aAAa,IAAI,CAAC;AAAA,EACpD,OAAO;AACL,WAAO,aAAa,OAAO;AAAA,EAC7B;AACF;AAMA,SAAS,YAAY,SAA4C;AAC/D,SAAO,MAAM,QAAQ,QAAQ,IAAI,KAAwB,QAAQ,SAAS;AAC5E;AAEA,SAAS,SAAS,MAA+B;AAC/C,SAAO,KAAK;AACd;AAGA,SAAS,aAAa,MAAgB;AAGpC,MAAI,OAAO,SAAS,YAAY,YAAY,QAAQ,aAAa,MAAM;AACrE,WAAO,cAAc,SAAS,IAAI;AAAA,EACpC;AAEA,UAAQ,KAAK,QAAQ;AAAA,IACnB;AACE,aAAO,oBAAoB,SAAS,IAAI;AAAA,IAC1C;AACE,aAAO,OAAO,SAAS,IAAI;AAAA,IAC7B;AACE,aAAO,aAAa,SAAS,IAAI;AAAA,IACnC;AACE,aAAO,MAAM,SAAS,IAAI;AAAA,IAC5B;AACE,aAAO,WAAW,SAAS,IAAI;AAAA,IACjC;AACE,aAAO,iBAAiB,SAAS,IAAI;AAAA,IACvC;AACE,aAAO,aAAa,SAAS,IAAI;AAAA,IACnC;AACE,aAAO,uBAAuB,SAAS,IAAI;AAAA,IAC7C;AACE,aAAO,uBAAuB,SAAS,IAAI;AAAA,IAC7C;AACE,aAAO,YAAY,SAAS,IAAI;AAAA,IAClC;AACE,aAAO,YAAY,SAAS,IAAI;AAAA,IAClC;AACE,aAAO,YAAY,SAAS,IAAI;AAAA,IAClC;AACE,aAAO,QAAQ,SAAS,IAAI;AAAA,IAC9B;AACE,aAAO,WAAW,SAAS,IAAI;AAAA,IACjC;AACE,aAAO,MAAM,SAAS,IAAI;AAAA,IAC5B;AACE,aAAO,SAAS,IAAI;AAAA,IACtB;AACE,aAAO,KAAK,SAAS,IAAI;AAAA,IAC3B;AACE,aAAO;AAAA,EACX;AACF;;;AzBtCA,IAAM,mBACJ,CAAC,OACD,UAAU,SAAS;AACjB,QAAM,WAAW,MAAM,GAAG,GAAG,IAAI;AACjC,MAAI,SAAS,WAAW,MAAM;AAC5B,WAAO,SAAS;AAAA,EAClB,OAAO;AACL,UAAM,EAAE,QAAQ,aAAa,IAAI;AAEjC,UAAM,EAAE,QAAQ,WAAW,IAAI;AAC/B,UAAM,QAAQ,IAAI,oCAAsB,cAAc,IAAI;AAAA,MACxD,MAAM,CAAC;AAAA,MACP,QAAQ,UAAU;AAAA,MAClB;AAAA,IACF,CAAC;AACD,UAAM,SAAS;AACf,UAAM;AAAA,EACR;AACF;AAEK,SAAS,aAAa,SAAkC;AAC7D,QAAM,UAAU,OAAU,mBAAuF;AAC/G,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT,aAAa;AAAA,MACb,YAAY;AAAA,IACd,IAAI;AACJ,QAAI,QAAQ;AACV,yCAAW,UAAU,0BAA0B;AAAA,IACjD;AACA,QAAI,aAAa;AACf;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,UAAM,EAAE,MAAM,QAAQ,aAAa,cAAc,YAAY,SAAS,IAAI;AAC1E,UAAM,MAAM,aAAa;AAEzB,yBAAqB,GAAG;AAExB,UAAM,MAAM,UAAU,QAAQ,YAAY,IAAI;AAG9C,UAAM,WAAW,IAAI,IAAI,GAAG;AAE5B,QAAI,aAAa;AAEf,YAAM,4BAAwB,sBAAAC,SAAc,EAAE,GAAG,YAAY,CAAC;AAG9D,iBAAW,CAACC,MAAK,GAAG,KAAK,OAAO,QAAQ,qBAAqB,GAAG;AAC9D,YAAI,KAAK;AACP,WAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,OAAK,SAAS,aAAa,OAAOA,MAAK,CAAW,CAAC;AAAA,QAC1E;AAAA,MACF;AAAA,IACF;AAGA,UAAM,UAA+B;AAAA,MACnC,eAAe,UAAU,GAAG;AAAA,MAC5B,cAAc;AAAA,MACd,GAAG;AAAA,IACL;AAEA,QAAI,MAA4B;AAChC,QAAI;AACF,UAAI,UAAU;AACZ,cAAM,MAAM,gBAAQ,MAAM,SAAS,MAAM;AAAA,UACvC,GAAG;AAAA,UACH;AAAA,UACA;AAAA,UACA,MAAM;AAAA,QACR,CAAC;AAAA,MACH,OAAO;AAEL,gBAAQ,cAAc,IAAI;AAE1B,cAAM,UAAU,WAAW,SAAS,cAAc,OAAO,KAAK,UAAU,EAAE,SAAS;AACnF,cAAM,OAAO,UAAU,EAAE,MAAM,KAAK,cAAU,sBAAAD,SAAc,YAAY,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,IAAI;AAE9F,cAAM,MAAM,gBAAQ;AAAA,UAClB,SAAS;AAAA,cACT,iBAAAE,SAAU,eAAe,CAAC,GAAG;AAAA,YAC3B;AAAA,YACA;AAAA,YACA,GAAG;AAAA,UACL,CAAC;AAAA,QACH;AAAA,MACF;AAGA,YAAM,iBACJ,KAAK,WAAW,IAAI,SAAS,IAAI,UAAU,QAAQ,WAAW,MAAM,UAAU,aAAa;AAC7F,YAAM,OAAO,OAAO,iBAAiB,IAAI,KAAK,IAAI,IAAI,KAAK;AAE3D,UAAI,CAAC,IAAI,IAAI;AACX,cAAM;AAAA,MACR;AAEA,aAAO;AAAA,QACL,MAAM,YAAY,IAAI;AAAA,QACtB,QAAQ;AAAA,MACV;AAAA,IACF,SAAS,KAAK;AACZ,UAAI,eAAe,OAAO;AACxB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,YACN;AAAA,cACE,MAAM;AAAA,cACN,SAAS,IAAI,WAAW;AAAA,YAC1B;AAAA,UACF;AAAA,UACA,cAAc,WAAW,KAAK,KAAK,OAAO;AAAA,QAC5C;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,QAAQ,YAAY,GAAG;AAAA;AAAA;AAAA,QAGvB,QAAQ,KAAK;AAAA,QACb,YAAY,KAAK;AAAA,QACjB,cAAc,WAAW,KAAK,KAAK,OAAO;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AAEA,SAAO,iBAAiB,OAAO;AACjC;AAIA,SAAS,WAAW,MAAe,SAA2B;AAC5D,MAAI,QAAQ,OAAO,SAAS,YAAY,oBAAoB,QAAQ,OAAO,KAAK,mBAAmB,UAAU;AAC3G,WAAO,KAAK;AAAA,EACd;AAEA,QAAM,QAAQ,SAAS,IAAI,QAAQ;AACnC,SAAO,SAAS;AAClB;AAEA,SAAS,YAAY,MAAgC;AACnD,MAAI,CAAC,CAAC,QAAQ,OAAO,SAAS,YAAY,YAAY,MAAM;AAC1D,UAAM,SAAS,KAAK;AACpB,WAAO,OAAO,SAAS,IAAI,OAAO,IAAI,UAAU,IAAI,CAAC;AAAA,EACvD;AACA,SAAO,CAAC;AACV;AAEA,SAAS,WAAW,OAAyC;AAC3D,SAAO;AAAA,IACL,MAAM,MAAM;AAAA,IACZ,SAAS,MAAM;AAAA,IACf,aAAa,MAAM;AAAA,IACnB,MAAM;AAAA,MACJ,WAAW,OAAO,MAAM;AAAA,MACxB,WAAW,OAAO,MAAM;AAAA,IAC1B;AAAA,EACF;AACF;;;A0BjLO,SAAS,uBAAuB,SAAkC;AACvE,QAAM,UAAU,aAAa,OAAO;AAEpC,SAAO;AAAA,IACL,sBAAsB,IAAI,uBAAuB,OAAO;AAAA,IACxD,SAAS,IAAI,UAAU,OAAO;AAAA,IAC9B,gBAAgB,IAAI,gBAAgB,OAAO;AAAA,IAC3C,QAAQ,IAAI,SAAS,OAAO;AAAA,IAC5B,cAAc,IAAI,gBAAgB,OAAO;AAAA,IACzC,aAAa,IAAI,cAAc,OAAO;AAAA,IACtC,eAAe,IAAI,gBAAgB,OAAO;AAAA,IAC1C,cAAc,IAAI,eAAe,OAAO;AAAA,IACxC,cAAc,IAAI,eAAe,OAAO;AAAA,IACxC,UAAU,IAAI,WAAW,OAAO;AAAA,IAChC,cAAc,IAAI,eAAe,OAAO;AAAA,IACxC,aAAa,IAAI,cAAc,OAAO;AAAA,IACtC,OAAO,IAAI,QAAQ,OAAO;AAAA,IAC1B,SAAS,IAAI,UAAU,OAAO;AAAA,EAChC;AACF;;;AC5DA,IAAAC,qBAA2B;AAsE3B,IAAM,cAAqC,UAAQ;AACjD,SAAO,MAAM;AACX,UAAM,MAAM,EAAE,GAAG,KAAK;AACtB,QAAI,UAAW,IAAI,UAAqB,IAAI,UAAU,GAAG,CAAC;AAC1D,QAAI,aAAc,IAAI,aAAwB,IAAI,UAAU,GAAG,CAAC;AAChE,QAAI,UAAW,IAAI,UAAqB,IAAI,UAAU,GAAG,CAAC;AAC1D,WAAO,EAAE,GAAG,IAAI;AAAA,EAClB;AACF;AAEO,SAAS,mBACd,eACA,SACA,WACoB;AACpB,QAAM;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,KAAK;AAAA,EACP,IAAI;AACJ,QAAM,EAAE,QAAQ,WAAW,QAAQ,YAAY,OAAO,SAAS,MAAM,aAAa,IAAI;AAEtF,MAAI,QAAQ;AACV,uCAAW,UAAU,0BAA0B;AAAA,EACjD;AAEA,QAAM,EAAE,SAAS,IAAI,uBAAuB;AAAA,IAC1C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,WAAW,eAAe;AAAA,IAC9B;AAAA,IACA,cAAc;AAAA,IACd,SAAS,IAAI,SAAS,SAAS,SAAS,GAAG,IAAI;AAAA,EACjD,CAAC;AAED,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK,uBAAuB,EAAE,OAAO,SAAS,gBAAgB,OAAO,CAAC;AAAA,IACtE,OAAO,YAAY,EAAE,GAAG,SAAS,GAAG,UAAU,CAAC;AAAA,EACjD;AACF;AAEO,SAAS,oBAAoB,WAAsD;AACxF,MAAI,WAAW,QAAQ;AACrB,uCAAW,UAAU,0BAA0B;AAAA,EACjD;AAEA,SAAO;AAAA,IACL,eAAe;AAAA,IACf,WAAW;AAAA,IACX,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,UAAU,MAAM,QAAQ,QAAQ,IAAI;AAAA,IACpC,KAAK,MAAM;AAAA,IACX,OAAO,YAAY,SAAS;AAAA,EAC9B;AACF;AAEO,SAAS,qBACd,UAQA;AAEA,MAAI,UAAU;AAEZ,WAAO,SAAS,iBAAiB;AAEjC,WAAO,SAAS,kBAAkB;AAAA,EACpC;AAEA,SAAO;AACT;AAEO,SAAS,mBAA+C,YAAkB;AAC/E,QAAM,OAAO,WAAW,OAAO,EAAE,GAAG,WAAW,KAAK,IAAI,WAAW;AACnE,QAAM,eAAe,WAAW,eAAe,EAAE,GAAG,WAAW,aAAa,IAAI,WAAW;AAE3F,uBAAqB,IAAI;AACzB,uBAAqB,YAAY;AAEjC,SAAO,EAAE,GAAG,YAAY,MAAM,aAAa;AAC7C;AASO,IAAM,6BAA6B,CAAoC,QAAc;AAG1F,QAAM,EAAE,OAAO,UAAU,KAAK,GAAG,KAAK,IAAI;AAC1C,SAAO;AACT;AAMA,IAAM,iBAAiC,YAAU;AAC/C,QAAM,EAAE,SAAS,cAAc,UAAU,IAAI,UAAU,CAAC;AAExD,SAAO,OAAO,UAAiC,CAAC,MAAM;AACpD,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AAEA,QAAI,QAAQ,UAAU;AACpB,aAAO,QAAQ,WAAW,QAAQ,QAAQ;AAAA,IAC5C;AAEA,WAAO;AAAA,EACT;AACF;AAEA,IAAM,yBACJ,CAAC;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAMA,YAAU;AACR,MAAI,CAAC,QAAQ,cAAc,CAAC,QAAQ,MAAM;AACxC,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,gBAAgB;AACpD,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,YAAY;AACrB,WAAO,eAAe,SAAS,OAAO,UAAU;AAAA,EAClD;AAEA,MAAI,OAAO,MAAM;AACf,WAAO,YAAY,OAAO;AAAA,EAC5B;AAEA,SAAO;AACT;;;ACvNK,IAAM,yBAAN,MAAM,gCAA+B,MAAM;AAAA,EAKhD,YAAY;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,OAAO;AAEb,WAAO,eAAe,MAAM,wBAAuB,SAAS;AAE5D,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EAEO,iBAAiB;AACtB,WAAO,GAAG,CAAC,KAAK,SAAS,KAAK,MAAM,EAAE,OAAO,OAAK,CAAC,EAAE,KAAK,GAAG,CAAC,YAAY,KAAK,MAAM,mBACnF,KAAK,YACP;AAAA,EACF;AACF;;;AC7BO,SAAS,0BAA0B,SAAkD;AAC1F,MAAI,QAAQ,aAAa;AACvB,uCAAW,eAAe,+BAA+B;AAAA,EAC3D;AACA,MAAI,QAAQ,YAAY;AACtB,uCAAW,cAAc,+BAA+B;AAAA,EAC1D;AAEA,UAAQ,kBAAc,iCAAoB,QAAQ,cAAc,GAAG,eAAe,QAAQ,eAAe;AACzG,QAAM,mBAAmB,CAAC,kBAAkB,QAAQ,WAAW,QAAI,2BAAe,QAAQ,MAAM,IAAI;AACpG,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,6CAaoC,WAAW;AAAA,qCACnB,KAAK,UAAU,aAAa,CAAC,CAAC,CAAC;AAAA,cACtD,WAAW,+BAA+B,QAAQ,MAAM,EAAE;AAAA,cAC1D,SAAS,4BAA4B,MAAM,MAAM,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uCAwB1B,WAAW;AAAA,0CACR,IAAI;AAAA,qCACT,YAAY,IAAI,SAAS,MAAM,MAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAkB3D,iBACI,sDAAsD,cAAc,QACpE,mDAAmD,WAAW,KACpE;AAAA;AAAA,kBAEE,SAAS,6CAA6C,MAAM,QAAQ,EAAE;AAAA,kBACtE,WAAW,gDAAgD,QAAQ,OAAO,EAAE;AAAA;AAAA,gCAG5E,kBACA,yBAAa,YAAY,oBAAoB,aAAa;AAAA,IACxD;AAAA,IACA;AAAA,EACF,CAAC,CACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQhB;AAGA,eAAsB,yBAAyB,SAAkC;AAC/E,MAAI,QAAQ,aAAa;AACvB,uCAAW,eAAe,+BAA+B;AAAA,EAC3D;AACA,MAAI,QAAQ,YAAY;AACtB,uCAAW,cAAc,+BAA+B;AAAA,EAC1D;AACA,UAAQ,kBAAc,iCAAoB,QAAQ,cAAc,GAAG,eAAe,QAAQ,eAAe;AACzG,QAAM,MAAM,2BAA2B,OAAO;AAC9C,QAAM,WAAW,UAAM;AAAA,IAAc,MACnC,gBAAQ,MAAM,2BAA2B,OAAO,GAAG;AAAA,MACjD,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,qBAAqB,QAAQ,aAAa;AAAA,MAC5C;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS,yCAAyC,GAAG,cAAc,SAAS,MAAM;AAAA,MAClF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,SAAS,KAAK;AACvB;AAEO,SAAS,2BAA2B,SAAkC;AAC3E,MAAI,QAAQ,aAAa;AACvB,uCAAW,eAAe,+BAA+B;AAAA,EAC3D;AAEA,UAAQ,kBAAc,iCAAoB,QAAQ,cAAc,GAAG,eAAe,QAAQ,eAAe;AACzG,QAAM,EAAE,QAAQ,aAAa,YAAY,gBAAgB,gBAAgB,UAAU,aAAa,QAAQ,UAAU,IAChH;AACF,QAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,MAAI,WAAW,UAAU,IAAI,UAAU,aAAa,sBAAsB;AAC1E,MAAI,aAAa,OAAO,oBAAoB,sBAAkB,wCAA4B,aAAa,UAAU,CAAC;AAClH,MAAI,gBAAgB;AAClB,QAAI,aAAa,OAAO,mBAAmB,cAAc;AAAA,EAC3D,OAAO;AACL,QAAI,aAAa,OAAO,gBAAgB,WAAW;AAAA,EACrD;AACA,MAAI,UAAU;AACZ,QAAI,aAAa,OAAO,aAAa,QAAQ;AAAA,EAC/C;AAEA,MAAI,aAAa;AACf,QAAI,aAAa,OAAO,gBAAgB,MAAM;AAAA,EAChD;AAEA,MAAI,aAAa,OAAO,eAAe,aAAa,EAAE;AAEtD,MAAI,CAAC,kBAAkB,QAAQ,WAAW,GAAG;AAC3C,QAAI,aAAa,OAAO,yBAAyB,MAAM;AAAA,EACzD;AAEA,MAAI,QAAQ;AACV,QAAI,aAAa,OAAO,UAAU,MAAM;AAAA,EAC1C;AAEA,SAAO,IAAI;AACb;;;AC9MA,oBAAsB;;;ACEtB,IAAM,YAAY,CAAC,KAAc,QAAgB,IAAI,QAAQ,IAAI,GAAG;AACpE,IAAM,0BAA0B,CAAC,UAA0B,OAAO,MAAM,GAAG,EAAE,CAAC;AAGvE,IAAM,kBAAmC,CAAC,SAAS,SAAS;AACjE,QAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;AAEtC,QAAM,iBAAiB,UAAU,SAAS,UAAU,QAAQ,cAAc;AAC1E,QAAM,gBAAgB,UAAU,SAAS,UAAU,QAAQ,aAAa;AACxE,QAAM,OAAO,UAAU,SAAS,UAAU,QAAQ,IAAI;AACtD,QAAM,WAAW,WAAW;AAE5B,QAAM,OAAO,YAAY,EAAE,UAAU,gBAAgB,eAAe,MAAM,QAAQ,WAAW,KAAK,CAAC;AAEnG,SAAO,IAAI,IAAI,QAAQ,WAAW,UAAU,IAAI;AAClD;AASO,IAAM,cAA2B,CAAC,EAAE,UAAU,gBAAgB,eAAe,KAAK,MAAM;AAC7F,QAAM,eAAe,wBAAwB,aAAa,KAAK;AAC/D,QAAM,mBAAmB,wBAAwB,cAAc,KAAK,UAAU,QAAQ,QAAQ,EAAE;AAEhG,MAAI,CAAC,gBAAgB,CAAC,kBAAkB;AACtC,WAAO;AAAA,EACT;AAEA,SAAO,GAAG,gBAAgB,MAAM,YAAY;AAC9C;;;AD7BO,IAAM,0BAA0B,CAAC,OAA0C;AAChF,QAAM,MAAM,GAAG,gBAAQ,SAAS,gBAAQ,OAAO;AAE/C,QAAM,sBAAsB,gBAAgB,GAAG;AAC/C,SAAO,IAAI,gBAAQ,QAAQ,qBAAqB,GAAG;AACrD;AAEO,IAAMC,gBAAe,CAAC,QAAkB;AAC7C,MAAI,CAAC,KAAK;AACR,WAAO,CAAC;AAAA,EACV;AACA,QAAM,UAAU,8BAA8B,GAAG;AACjD,QAAM,UAAU,+BAA+B,GAAG;AAClD,QAAM,eAAe,qCAAqC,GAAG;AAE7D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,SAAS,CAAC,QAAwB;AACtC,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,SAAO,IAAI,QAAQ,oBAAoB,kBAAkB;AAC3D;AAEA,IAAM,gCAAgC,CAAC,QAAiB;AACtD,QAAM,UAAU,IAAI,WAAW,IAAI,SAAS,IAAI,QAAQ,QAAI,qBAAM,IAAI,QAAQ,IAAI,QAAQ,CAAW,IAAI,CAAC;AAC1G,SAAO,CAAC,QAAoC;AAC1C,UAAM,QAAQ,UAAU,GAAG;AAC3B,QAAI,UAAU,QAAW;AACvB,aAAO;AAAA,IACT;AACA,WAAO,OAAO,KAAK;AAAA,EACrB;AACF;AAEA,IAAM,iCAAiC,CAAC,QAAiB,CAAC,QAAgB,KAAK,SAAS,IAAI,GAAG,KAAK;AAEpG,IAAM,uCAAuC,CAAC,QAAkB,KAAK,MAAM,IAAI,IAAI,IAAI,GAAG,GAAG,eAAe;AAErG,IAAM,2BAA2B,CAAC,cAA6D;AACpG,SAAO,WAAW,QAAQ,WAAW,EAAE;AACzC;;;AE7CO,IAAK,aAAL,kBAAKC,gBAAL;AACL,EAAAA,YAAA,cAAW;AACX,EAAAA,YAAA,eAAY;AACZ,EAAAA,YAAA,kBAAe;AACf,EAAAA,YAAA,aAAU;AAJA,SAAAA;AAAA,GAAA;AAgFZ,eAAsB,SACpB,SACA,eACwB;AACxB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,EAAE,KAAK,WAAW,QAAQ,OAAO,KAAK,OAAO,IAAI;AAEvD,QAAM,EAAE,UAAU,OAAO,cAAc,IAAI,uBAAuB;AAAA,IAChE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAED,QAAM,CAAC,aAAa,UAAU,gBAAgB,IAAI,MAAM,QAAQ,IAAI;AAAA,IAClE,cAAc,SAAS,WAAW,SAAS,IAAI,QAAQ,QAAQ,MAAS;AAAA,IACxE,WAAW,MAAM,QAAQ,MAAM,IAAI,QAAQ,QAAQ,MAAS;AAAA,IAC5D,oBAAoB,QAAQ,cAAc,gBAAgB,EAAE,gBAAgB,MAAM,CAAC,IAAI,QAAQ,QAAQ,MAAS;AAAA,EAClH,CAAC;AAED,QAAM,UAAU;AAChB,QAAM,OAAO;AACb,QAAM,eAAe;AAKrB,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,eAAe,eAAe;AAAA,MACrC;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA,EAAE,GAAG,SAAS,QAAQ,2BAAoB;AAAA,EAC5C;AAEA,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,QAAQ,MAAM;AAAA,IACd;AAAA,EACF;AACF;AAEO,SAAS,UAAa,SAAY,QAAoB,UAAU,IAAoB;AACzF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,SAAO;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,QAAQ,MAAM,oBAAoB,EAAE,GAAG,SAAS,QAAQ,8BAAsB,QAAQ,QAAQ,CAAC;AAAA,IAC/F,OAAO;AAAA,EACT;AACF;AAEO,SAAS,aAAgB,SAAY,QAAoB,UAAU,IAAuB;AAC/F,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,QAAQ,MAAM;AAAA,IACd,OAAO;AAAA,EACT;AACF;AAEO,SAAS,aAAgB,SAAY,QAAoB,UAAU,IAAkB;AAC1F,QAAM,EAAE,aAAa,gBAAgB,aAAa,QAAQ,WAAW,WAAW,gBAAgB,eAAe,IAC7G;AACF,SAAO;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,QAAQ,MAAM;AAAA,IACd,OAAO;AAAA,EACT;AACF;;;AC3PO,SAAS,iBAAiB;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKG;AACD,QAAM,WAAW,YAAY,EAAE,gBAAgB,eAAe,UAAU,UAAU,UAAU,KAAK,CAAC;AAClG,SAAO,YAAY,IAAI,IAAI,QAAQ,EAAE,WAAW,UAAU;AAC5D;AAgBO,IAAM,uBAAuB,CAAC,QAAuB,SAAiB;AAC3E,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,KAAK,CAAC,QAAqB,IAAI,SAAS,IAAI;AAC5D;;;AC3CA,IAAAC,qBAAyC;;;ACWlC,IAAM,YAAY;AAAA,EACvB,MAAM,QAAgB,MAAiC;AACrD,WAAOC,OAAM,QAAQ,mBAAmB,IAAI;AAAA,EAC9C;AAAA,EAEA,UAAU,MAAyB,MAAiC;AAClE,WAAO,UAAU,MAAM,mBAAmB,IAAI;AAAA,EAChD;AACF;AAEA,IAAM,oBAA8B;AAAA,EAClC,OAAO;AAAA,EACP,MAAM;AACR;AAiBA,SAASA,OAAM,QAAgB,UAAoB,OAAqB,CAAC,GAAe;AAEtF,MAAI,CAAC,SAAS,OAAO;AACnB,aAAS,QAAQ,CAAC;AAClB,aAAS,IAAI,GAAG,IAAI,SAAS,MAAM,QAAQ,EAAE,GAAG;AAC9C,eAAS,MAAM,SAAS,MAAM,CAAC,CAAC,IAAI;AAAA,IACtC;AAAA,EACF;AAGA,MAAI,CAAC,KAAK,SAAU,OAAO,SAAS,SAAS,OAAQ,GAAG;AACtD,UAAM,IAAI,YAAY,iBAAiB;AAAA,EACzC;AAGA,MAAI,MAAM,OAAO;AACjB,SAAO,OAAO,MAAM,CAAC,MAAM,KAAK;AAC9B,MAAE;AAGF,QAAI,CAAC,KAAK,SAAS,GAAI,OAAO,SAAS,OAAO,SAAS,OAAQ,IAAI;AACjE,YAAM,IAAI,YAAY,iBAAiB;AAAA,IACzC;AAAA,EACF;AAGA,QAAM,MAAM,KAAK,KAAK,OAAO,YAAc,MAAM,SAAS,OAAQ,IAAK,CAAC;AAGxE,MAAI,OAAO;AACX,MAAI,SAAS;AACb,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAE5B,UAAM,QAAQ,SAAS,MAAM,OAAO,CAAC,CAAC;AACtC,QAAI,UAAU,QAAW;AACvB,YAAM,IAAI,YAAY,uBAAuB,OAAO,CAAC,CAAC;AAAA,IACxD;AAGA,aAAU,UAAU,SAAS,OAAQ;AACrC,YAAQ,SAAS;AAGjB,QAAI,QAAQ,GAAG;AACb,cAAQ;AACR,UAAI,SAAS,IAAI,MAAQ,UAAU;AAAA,IACrC;AAAA,EACF;AAGA,MAAI,QAAQ,SAAS,QAAQ,MAAQ,UAAW,IAAI,MAAQ;AAC1D,UAAM,IAAI,YAAY,wBAAwB;AAAA,EAChD;AAEA,SAAO;AACT;AAEA,SAAS,UAAU,MAAyB,UAAoB,OAAyB,CAAC,GAAW;AACnG,QAAM,EAAE,MAAM,KAAK,IAAI;AACvB,QAAM,QAAQ,KAAK,SAAS,QAAQ;AACpC,MAAI,MAAM;AAEV,MAAI,OAAO;AACX,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAEpC,aAAU,UAAU,IAAM,MAAO,KAAK,CAAC;AACvC,YAAQ;AAGR,WAAO,OAAO,SAAS,MAAM;AAC3B,cAAQ,SAAS;AACjB,aAAO,SAAS,MAAM,OAAQ,UAAU,IAAK;AAAA,IAC/C;AAAA,EACF;AAGA,MAAI,MAAM;AACR,WAAO,SAAS,MAAM,OAAQ,UAAW,SAAS,OAAO,IAAM;AAAA,EACjE;AAGA,MAAI,KAAK;AACP,WAAQ,IAAI,SAAS,SAAS,OAAQ,GAAG;AACvC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACnIA,IAAM,YAAoC;AAAA,EACxC,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACT;AACA,IAAM,qBAAqB;AAE3B,IAAM,qBAA6C;AAAA,EACjD,OAAO;AAAA,EACP,OAAO;AAAA,EACP,OAAO;AACT;AAEO,IAAM,OAAO,OAAO,KAAK,SAAS;AAElC,SAAS,mBAAmB,eAA8C;AAC/E,QAAM,OAAO,UAAU,aAAa;AACpC,QAAM,OAAO,mBAAmB,aAAa;AAE7C,MAAI,CAAC,QAAQ,CAAC,MAAM;AAClB,UAAM,IAAI,MAAM,yBAAyB,aAAa,qBAAqB,KAAK,KAAK,GAAG,CAAC,GAAG;AAAA,EAC9F;AAEA,SAAO;AAAA,IACL,MAAM,EAAE,MAAM,UAAU,aAAa,EAAE;AAAA,IACvC,MAAM,mBAAmB,aAAa;AAAA,EACxC;AACF;;;ACtBA,IAAM,gBAAgB,CAAC,MAA8B;AACnD,SAAO,MAAM,QAAQ,CAAC,KAAK,EAAE,SAAS,KAAK,EAAE,MAAM,OAAK,OAAO,MAAM,QAAQ;AAC/E;AAEO,IAAM,sBAAsB,CAAC,KAAe,aAAuB;AACxE,QAAM,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,OAAK,CAAC,CAAC,CAAC;AACtD,QAAM,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,OAAK,CAAC,CAAC,CAAC;AAC5C,QAAM,uBAAuB,aAAa,SAAS,KAAK,QAAQ,SAAS;AAEzE,MAAI,CAAC,sBAAsB;AASzB;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,CAAC,aAAa,SAAS,GAAG,GAAG;AAC/B,YAAM,IAAI,uBAAuB;AAAA,QAC/B;AAAA,QACA;AAAA,QACA,SAAS,oCAAoC,KAAK,UAAU,GAAG,CAAC,yBAAyB,KAAK;AAAA,UAC5F;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,WAAW,cAAc,GAAG,GAAG;AAC7B,QAAI,CAAC,IAAI,KAAK,OAAK,aAAa,SAAS,CAAC,CAAC,GAAG;AAC5C,YAAM,IAAI,uBAAuB;AAAA,QAC/B;AAAA,QACA;AAAA,QACA,SAAS,0CAA0C,KAAK,UAAU,GAAG,CAAC,yBAAyB,KAAK;AAAA,UAClG;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEO,IAAM,mBAAmB,CAAC,QAAkB;AACjD,MAAI,OAAO,QAAQ,aAAa;AAC9B;AAAA,EACF;AAEA,MAAI,QAAQ,OAAO;AACjB,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA;AAAA,MACA,SAAS,oBAAoB,KAAK,UAAU,GAAG,CAAC;AAAA,IAClD,CAAC;AAAA,EACH;AACF;AAEO,IAAM,wBAAwB,CAAC,QAAgB;AACpD,MAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACvB,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA;AAAA,MACA,SAAS,yBAAyB,KAAK,UAAU,GAAG,CAAC,gBAAgB,IAAI;AAAA,IAC3E,CAAC;AAAA,EACH;AACF;AAEO,IAAM,iBAAiB,CAAC,QAAiB;AAC9C,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA;AAAA,MACA,SAAS,kEAAkE,KAAK,UAAU,GAAG,CAAC;AAAA,IAChG,CAAC;AAAA,EACH;AACF;AAEO,IAAM,+BAA+B,CAAC,KAAc,sBAAiC;AAC1F,MAAI,CAAC,OAAO,CAAC,qBAAqB,kBAAkB,WAAW,GAAG;AAChE;AAAA,EACF;AAEA,MAAI,CAAC,kBAAkB,SAAS,GAAG,GAAG;AACpC,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS,4CAA4C,KAAK,UAAU,GAAG,CAAC,eAAe,iBAAiB;AAAA,IAC1G,CAAC;AAAA,EACH;AACF;AAEO,IAAM,oBAAoB,CAAC,KAAa,WAAkC;AAC/E,MAAI,OAAO,WAAW,cAAc,CAAC,OAAO,GAAG,GAAG;AAChD,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH,WAAW,OAAO,WAAW,YAAY,OAAO,QAAQ,QAAQ;AAC9D,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS,kCAAkC,KAAK,UAAU,GAAG,CAAC,eAAe,MAAM;AAAA,IACrF,CAAC;AAAA,EACH;AACF;AAEO,IAAM,wBAAwB,CAAC,KAAa,kBAA0B;AAC3E,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA;AAAA,MACA,SAAS,uCAAuC,KAAK,UAAU,GAAG,CAAC;AAAA,IACrE,CAAC;AAAA,EACH;AAEA,QAAM,cAAc,IAAI,KAAK,KAAK,IAAI,CAAC;AACvC,QAAM,aAAa,oBAAI,KAAK,CAAC;AAC7B,aAAW,cAAc,GAAG;AAE5B,QAAM,UAAU,WAAW,QAAQ,KAAK,YAAY,QAAQ,IAAI;AAChE,MAAI,SAAS;AACX,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS,gCAAgC,WAAW,YAAY,CAAC,mBAAmB,YAAY,YAAY,CAAC;AAAA,IAC/G,CAAC;AAAA,EACH;AACF;AAEO,IAAM,wBAAwB,CAAC,KAAyB,kBAA0B;AACvF,MAAI,OAAO,QAAQ,aAAa;AAC9B;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA;AAAA,MACA,SAAS,2CAA2C,KAAK,UAAU,GAAG,CAAC;AAAA,IACzE,CAAC;AAAA,EACH;AAEA,QAAM,cAAc,IAAI,KAAK,KAAK,IAAI,CAAC;AACvC,QAAM,gBAAgB,oBAAI,KAAK,CAAC;AAChC,gBAAc,cAAc,GAAG;AAE/B,QAAM,QAAQ,cAAc,QAAQ,IAAI,YAAY,QAAQ,IAAI;AAChE,MAAI,OAAO;AACT,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS,6EAA6E,cAAc,YAAY,CAAC,mBAAmB,YAAY,YAAY,CAAC;AAAA,IAC/J,CAAC;AAAA,EACH;AACF;AAEO,IAAM,sBAAsB,CAAC,KAAyB,kBAA0B;AACrF,MAAI,OAAO,QAAQ,aAAa;AAC9B;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA;AAAA,MACA,SAAS,0CAA0C,KAAK,UAAU,GAAG,CAAC;AAAA,IACxE,CAAC;AAAA,EACH;AAEA,QAAM,cAAc,IAAI,KAAK,KAAK,IAAI,CAAC;AACvC,QAAM,eAAe,oBAAI,KAAK,CAAC;AAC/B,eAAa,cAAc,GAAG;AAE9B,QAAM,aAAa,aAAa,QAAQ,IAAI,YAAY,QAAQ,IAAI;AACpE,MAAI,YAAY;AACd,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS,oEAAoE,aAAa,YAAY,CAAC,mBAAmB,YAAY,YAAY,CAAC;AAAA,IACrJ,CAAC;AAAA,EACH;AACF;;;ACtLA,4BAA+B;AAK/B,SAAS,YAAY,QAA6B;AAChD,QAAM,UAAU,OACb,QAAQ,uBAAuB,EAAE,EACjC,QAAQ,qBAAqB,EAAE,EAC/B,QAAQ,OAAO,EAAE;AAEpB,QAAM,cAAU,sCAAe,OAAO;AAEtC,QAAM,SAAS,IAAI,YAAY,QAAQ,MAAM;AAC7C,QAAM,UAAU,IAAI,WAAW,MAAM;AAErC,WAAS,IAAI,GAAG,SAAS,QAAQ,QAAQ,IAAI,QAAQ,KAAK;AACxD,YAAQ,CAAC,IAAI,QAAQ,WAAW,CAAC;AAAA,EACnC;AAEA,SAAO;AACT;AAEO,SAAS,UACd,KACA,WACA,UACoB;AACpB,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,gBAAQ,OAAO,OAAO,UAAU,OAAO,KAAK,WAAW,OAAO,CAAC,QAAQ,CAAC;AAAA,EACjF;AAEA,QAAM,UAAU,YAAY,GAAG;AAC/B,QAAM,SAAS,aAAa,SAAS,UAAU;AAE/C,SAAO,gBAAQ,OAAO,OAAO,UAAU,QAAQ,SAAS,WAAW,OAAO,CAAC,QAAQ,CAAC;AACtF;;;AJZA,IAAM,gCAAgC,IAAI;AAE1C,eAAsB,kBAAkB,KAAU,KAA0B;AAC1E,QAAM,EAAE,QAAQ,WAAW,IAAI,IAAI;AACnC,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,OAAO,QAAQ,OAAO,CAAC,IAAI,QAAQ,IAAI,OAAO,EAAE,KAAK,GAAG,CAAC;AAC/D,QAAM,YAAY,mBAAmB,OAAO,GAAG;AAE/C,QAAM,YAAY,MAAM,UAAU,KAAK,WAAW,QAAQ;AAE1D,SAAO,gBAAQ,OAAO,OAAO,OAAO,UAAU,MAAM,WAAW,WAAW,IAAI;AAChF;AAEO,SAAS,UAAU,OAAoB;AAC5C,QAAM,cAAc,SAAS,IAAI,SAAS,EAAE,MAAM,GAAG;AACrD,MAAI,WAAW,WAAW,GAAG;AAC3B,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,QAAM,CAAC,WAAW,YAAY,YAAY,IAAI;AAE9C,QAAM,UAAU,IAAI,YAAY;AAiBhC,QAAM,SAAS,KAAK,MAAM,QAAQ,OAAO,UAAU,MAAM,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC;AACrF,QAAM,UAAU,KAAK,MAAM,QAAQ,OAAO,UAAU,MAAM,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC;AACvF,QAAM,YAAY,UAAU,MAAM,cAAc,EAAE,OAAO,KAAK,CAAC;AAE/D;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK;AAAA,MACH,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAAA,EACF;AACF;AAeA,eAAsB,UACpB,OACA,EAAE,UAAU,mBAAmB,oBAAoB,eAAe,QAAQ,IAAI,GACzD;AACrB,MAAI,oBAAoB;AACtB,uCAAW,sBAAsB,8BAA8B;AAAA,EACjE;AAEA,QAAM,YAAY,iBAAiB,sBAAsB;AAEzD,QAAM,UAAU,UAAU,KAAK;AAE/B,QAAM,EAAE,QAAQ,QAAQ,IAAI;AAG5B,QAAM,EAAE,KAAK,IAAI,IAAI;AAErB,mBAAiB,GAAG;AACpB,wBAAsB,GAAG;AAGzB,QAAM,EAAE,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI;AAE9C,iBAAe,GAAG;AAClB,sBAAoB,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC;AACrC,+BAA6B,KAAK,iBAAiB;AACnD,oBAAkB,KAAK,MAAM;AAC7B,wBAAsB,KAAK,SAAS;AACpC,wBAAsB,KAAK,SAAS;AACpC,sBAAoB,KAAK,SAAS;AAElC,MAAI;AAEJ,MAAI;AACF,qBAAiB,MAAM,kBAAkB,SAAS,GAAG;AAAA,EACvD,SAAS,KAAK;AACZ,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA;AAAA,MACA,SAAS,kCAAkC,GAAG;AAAA,IAChD,CAAC;AAAA,EACH;AAEA,MAAI,CAAC,gBAAgB;AACnB,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,SAAO;AACT;;;AK/IA,SAAS,cAAc,OAAwB;AAC7C,QAAM,cAAc,KAAK,UAAU,KAAK;AACxC,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,UAAU,QAAQ,OAAO,WAAW;AAC1C,SAAO,UAAU,UAAU,SAAS,EAAE,KAAK,MAAM,CAAC;AACpD;AAeA,eAAsB,QACpB,SACA,KACA,SACiB;AACjB,MAAI,CAAC,QAAQ,WAAW;AACtB,UAAM,IAAI,MAAM,wBAAwB;AAAA,EAC1C;AACA,QAAM,UAAU,IAAI,YAAY;AAEhC,QAAM,YAAY,mBAAmB,QAAQ,SAAS;AACtD,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,yBAAyB,QAAQ,SAAS,EAAE;AAAA,EAC9D;AAEA,QAAM,YAAY,MAAM,UAAU,KAAK,WAAW,MAAM;AACxD,QAAM,SAAS,QAAQ,UAAU,EAAE,KAAK,MAAM;AAE9C,SAAO,MAAM,QAAQ;AACrB,UAAQ,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAE1C,QAAM,gBAAgB,cAAc,MAAM;AAC1C,QAAM,iBAAiB,cAAc,OAAO;AAC5C,QAAM,YAAY,GAAG,aAAa,IAAI,cAAc;AAEpD,QAAM,YAAY,MAAM,gBAAQ,OAAO,OAAO,KAAK,WAAW,WAAW,QAAQ,OAAO,SAAS,CAAC;AAElG,SAAO,GAAG,SAAS,IAAI,UAAU,UAAU,IAAI,WAAW,SAAS,GAAG,EAAE,KAAK,MAAM,CAAC,CAAC;AACvF;;;ACxCA,IAAI,QAAyB,CAAC;AAC9B,IAAI,gBAAgB;AAEpB,SAAS,aAAa,KAAa;AACjC,SAAO,MAAM,GAAG;AAClB;AAEA,SAAS,iBAAiB;AACxB,SAAO,OAAO,OAAO,KAAK;AAC5B;AAEA,SAAS,WAAW,KAAwB,eAAe,MAAM;AAC/D,QAAM,IAAI,GAAG,IAAI;AACjB,kBAAgB,eAAe,KAAK,IAAI,IAAI;AAC9C;AAEA,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,aAAa;AAUZ,SAAS,sBAAsB,UAA+B;AACnE,MAAI,CAAC,aAAa,WAAW,GAAG;AAC9B,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,uBAAuB;AAAA,QAC/B;AAAA,QACA,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,UAAU,SACb,QAAQ,kBAAkB,EAAE,EAC5B,QAAQ,YAAY,EAAE,EACtB,QAAQ,aAAa,EAAE,EACvB,QAAQ,YAAY,EAAE,EACtB,QAAQ,YAAY,EAAE,EACtB,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,GAAG;AAGrB;AAAA,MACE;AAAA,QACE,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA;AAAA;AAAA,IACF;AAAA,EACF;AAEA,SAAO,aAAa,WAAW;AACjC;AA6BA,eAAsB,uBAAuB;AAAA,EAC3C;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF,GAAuD;AACrD,MAAI,gBAAgB,GAAG;AACrB,YAAQ,CAAC;AAAA,EACX;AACA,QAAM,qBAAqB,CAAC,aAAa,GAAG;AAC5C,MAAI,iBAAiB,oBAAoB;AACvC,QAAI;AACJ,UAAM,MAAM,aAAa;AAEzB,QAAI,KAAK;AACP,gBAAU,MAAM,kBAAkB,QAAQ,KAAK,UAAU;AAAA,IAC3D,WAAW,QAAQ;AACjB,gBAAU,MAAM,kBAAkB,MAAM;AAAA,IAC1C,OAAO;AACL,YAAM,IAAI,uBAAuB;AAAA,QAC/B;AAAA,QACA,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,EAAE,KAAK,IAAI,UAAM,oCAA6C,OAAO;AAE3E,QAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ;AACzB,YAAM,IAAI,uBAAuB;AAAA,QAC/B;AAAA,QACA,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAEA,SAAK,QAAQ,CAAAC,SAAO,WAAWA,IAAG,CAAC;AAAA,EACrC;AAEA,QAAM,MAAM,aAAa,GAAG;AAE5B,MAAI,CAAC,KAAK;AACR,UAAM,cAAc,eAAe;AACnC,UAAM,UAAU,YAAY,IAAI,CAAAC,SAAOA,KAAI,GAAG,EAAE,KAAK,IAAI;AAEzD,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS,8DAA8D,GAAG,sJACxE,UAAU,qCAAqC,OAAO,KAAK,EAC7D;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,eAAe,kBAAkB,QAAgB;AAC/C,QAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,MAAI,WAAW,UAAU,IAAI,UAAU,uBAAuB;AAE9D,QAAM,WAAW,MAAM,gBAAQ,MAAM,IAAI,IAAI;AAE7C,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS,iCAAiC,IAAI,IAAI,cAAc,SAAS,MAAM;AAAA,MAC/E;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,SAAS,KAAK;AACvB;AAEA,eAAe,kBAAkB,QAAgB,KAAa,YAAoB;AAChF,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SACE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,MAAM,IAAI,IAAI,MAAM;AAC1B,MAAI,WAAW,UAAU,IAAI,UAAU,YAAY,OAAO;AAE1D,QAAM,WAAW,MAAM,gBAAQ,MAAM,IAAI,MAAM;AAAA,IAC7C,SAAS;AAAA,MACP,eAAe,UAAU,GAAG;AAAA,MAC5B,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AAED,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,UAAM,wBAAwB,qBAAqB,MAAM,kDAAmD;AAE5G,QAAI,uBAAuB;AACzB,YAAM;AAEN,YAAM,IAAI,uBAAuB;AAAA,QAC/B;AAAA,QACA,SAAS,sBAAsB;AAAA,QAC/B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS,iCAAiC,IAAI,IAAI,cAAc,SAAS,MAAM;AAAA,MAC/E;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,SAAS,KAAK;AACvB;AAEA,SAAS,kBAAkB;AAEzB,MAAI,kBAAkB,IAAI;AACxB,WAAO;AAAA,EACT;AAGA,QAAM,YAAY,KAAK,IAAI,IAAI,iBAAiB,oCAAoC;AAEpF,SAAO;AACT;;;AC7NA,eAAsB,YAAY,OAAe,SAAkD;AACjG,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,MAAI,QAAQ,QAAQ;AAClB,uCAAW,UAAU,0BAA0B;AAAA,EACjD;AAEA,QAAM,EAAE,OAAO,IAAI,UAAU,KAAK;AAClC,QAAM,EAAE,IAAI,IAAI;AAEhB,MAAI;AAEJ,MAAI,QAAQ;AACV,UAAM,sBAAsB,MAAM;AAAA,EACpC,WAAW,OAAO,WAAW,UAAU;AAErC,UAAM,MAAM,uBAAuB,EAAE,QAAQ,KAAK,kBAAkB,cAAc,CAAC;AAAA,EACrF,WAAW,UAAU,WAAW;AAE9B,UAAM,MAAM,uBAAuB,EAAE,QAAQ,WAAW,QAAQ,YAAY,KAAK,kBAAkB,cAAc,CAAC;AAAA,EACpH,OAAO;AACL,UAAM,IAAI,uBAAuB;AAAA,MAC/B;AAAA,MACA,SAAS;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,MAAM,UAAU,OAAO;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;;;ACzDA,IAAM,+BAA+B,CAAC,OAAyB,CAAC,CAAC,IAAI,IAAI,uBAAuB;AAChG,IAAM,gBAAgB,CAAC,OAAyB,IAAI,IAAI,gBAAgB,MAAM;AAE9E,IAAM,oBAAoB;AAE1B,IAAM,YAAY,CAAC,cAAkC,kBAAkB,KAAK,aAAa,EAAE;AAQpF,IAAM,6BAA+C,aAAW;AACrE,QAAM,EAAE,QAAQ,WAAW,UAAU,IAAI;AACzC,QAAM,MAAM,aAAa,UAAU;AACnC,UAAI,qCAAwB,GAAG,KAAK,CAAC,UAAU,SAAS,GAAG;AACzD,WAAO,UAAU,mEAAgD;AAAA,EACnE;AACA,SAAO;AACT;AAEO,IAAM,kCAAoD,aAAW;AAC1E,QAAM,EAAE,QAAQ,MAAM,eAAe,eAAe,IAAI;AACxD,QAAM,gBACJ,UACA,iBAAiB;AAAA,IACf,WAAW,IAAI,IAAI,MAAM;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AAEH,MAAI,eAAe;AACjB,WAAO,UAAU,sDAA0C;AAAA,EAC7D;AACA,SAAO;AACT;AAEO,IAAM,wCAA0D,aAAW;AAChF,QAAM,EAAE,QAAQ,WAAW,aAAa,aAAa,IAAI;AACzD,QAAM,MAAM,aAAa,UAAU;AACnC,QAAM,YAAQ,qCAAwB,GAAG;AAEzC,MAAI,SAAS,CAAC,eAAe,6BAA6B,YAAY,GAAG;AACvE,WAAO,aAAa,qEAAiD;AAAA,EACvE;AACA,SAAO;AACT;AAEO,IAAM,wCAA0D,aAAW;AAChF,QAAM,EAAE,QAAQ,WAAW,UAAU,IAAI;AACzC,QAAM,MAAM,aAAa,UAAU;AACnC,QAAM,UAAM,qCAAwB,GAAG;AACvC,MAAI,OAAO,CAAC,WAAW;AACrB,WAAO,aAAa,6CAAyC;AAAA,EAC/D;AACA,SAAO;AACT;AAMO,IAAM,yDAA2E,aAAW;AACjG,QAAM,EAAE,QAAQ,WAAW,UAAU,MAAM,eAAe,eAAe,IAAI;AAC7E,QAAM,sBACJ,YAAY,iBAAiB,EAAE,WAAW,IAAI,IAAI,QAAQ,GAAG,MAAM,eAAe,eAAe,CAAC;AACpG,QAAM,MAAM,aAAa,UAAU;AAEnC,UAAI,qCAAwB,GAAG,KAAK,qBAAqB;AACvD,WAAO,aAAa,0DAA4C;AAAA,EAClE;AACA,SAAO;AACT;AAEO,IAAM,+CAAiE,aAAW;AACvF,QAAM,EAAE,QAAQ,WAAW,WAAW,YAAY,IAAI;AACtD,QAAM,MAAM,aAAa,UAAU;AAEnC,UAAI,oCAAuB,GAAG,KAAK,CAAC,aAAa,CAAC,aAAa;AAC7D,WAAO,UAAU,2DAA4C;AAAA,EAC/D;AACA,SAAO;AACT;AAQO,IAAM,yBAA2C,aAAW;AACjE,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,cAAc,KAAK;AACrB,WAAO,UAAU,sDAA0C;AAAA,EAC7D;AACA,SAAO;AACT;AAGO,IAAM,yCAA2D,aAAW;AACjF,QAAM,EAAE,WAAW,YAAY,IAAI;AAEnC,MAAI,aAAa,OAAO,SAAS,SAAS,IAAI,KAAK,CAAC,aAAa;AAC/D,WAAO,aAAa,6CAAsC;AAAA,EAC5D;AACA,SAAO;AACT;AAEO,IAAM,sBAAwC,OAAM,YAAW;AACpE,QAAM,EAAE,YAAY,IAAI;AACxB,QAAM,gBAAgB,MAAM,mBAAmB,SAAS,WAAW;AACnE,SAAO,MAAM,SAAS,EAAE,GAAG,SAAS,OAAO,YAAY,GAAG,aAAa;AACzE;AAEO,IAAM,sBAAwC,OAAM,YAAW;AACpE,QAAM,EAAE,aAAa,UAAU,IAAI;AACnC,QAAM,gBAAgB,MAAM,mBAAmB,SAAS,WAAW;AACnE,QAAM,QAAQ,MAAM,SAAS,EAAE,GAAG,SAAS,OAAO,YAAY,GAAG,aAAa;AAE9E,QAAM,MAAM,MAAM,OAAO,EAAE;AAC3B,QAAM,wBAAwB,IAAI,MAAM,OAAO,SAAS,SAAS;AAEjE,MAAI,CAAC,aAAa,uBAAuB;AACvC,WAAO,aAAa,+CAAuC;AAAA,EAC7D;AAEA,SAAO;AACT;AAEA,eAAsB,qBACpB,MACA,OACuB;AACvB,aAAW,QAAQ,OAAO;AACxB,UAAM,MAAM,MAAM,KAAK,IAAI;AAC3B,QAAI,KAAK;AACP,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO,UAAU,8CAAqC;AACxD;AAEA,eAAe,mBAAmB,SAAqC,OAAe;AACpF,QAAM,EAAE,aAAa,SAAS,IAAI;AAClC,MAAI;AACJ,MAAI,aAAa;AACf,aAAS;AAAA,EACX,WAAW,UAAU;AACnB,aAAS;AAAA,EACX,OAAO;AACL,aAAS,CAAC,QAAgB,IAAI,WAAW,gBAAgB,KAAK,IAAI,SAAS,iBAAiB;AAAA,EAC9F;AAEA,SAAO,YAAY,OAAO,EAAE,GAAG,SAAS,OAAO,CAAC;AAClD;AAMO,IAAM,6BAA+C,aAAW;AACrE,QAAM,EAAE,WAAW,aAAa,cAAc,UAAU,IAAI;AAE5D,QAAM,cAAc,CAAC,aAAa,cAAc;AAEhD,MAAI,eAAe,eAAe,CAAC,UAAU,SAAS,GAAG;AACvD,WAAO,UAAU,oEAAoD;AAAA,EACvE;AAEA,MAAI,eAAe,eAAe,CAAC,cAAc,YAAY,GAAG;AAC9D,WAAO,aAAa,oEAAoD;AAAA,EAC1E;AAEA,SAAO;AACT;;;ACjGA,SAAS,sBAAsB,WAA+B,KAA0C;AACtG,MAAI,CAAC,iBAAa,qCAAwB,GAAG,GAAG;AAC9C,UAAM,IAAI,MAAM,8EAA8E;AAAA,EAChG;AACF;AAEA,SAAS,uBAAuB,kBAAsC;AACpE,MAAI,CAAC,kBAAkB;AACrB,UAAM,IAAI,MAAM,8FAA8F;AAAA,EAChH;AACF;AAEA,SAAS,+BAA+B,YAAoB,QAAgB;AAC1E,MAAI;AACJ,MAAI;AACF,gBAAY,IAAI,IAAI,UAAU;AAAA,EAChC,QAAQ;AACN,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AAEA,MAAI,UAAU,WAAW,QAAQ;AAC/B,UAAM,IAAI,MAAM,kFAAkF;AAAA,EACpG;AACF;AAEA,eAAsB,oBAAoB,SAA4D;AACpG,QAAM,EAAE,SAAS,SAAS,aAAa,IAAIC,cAAa,SAAS,OAAO;AAExE,MAAI,QAAQ,aAAa;AACvB,uCAAW,eAAe,+BAA+B;AAAA,EAC3D;AAEA,MAAI,QAAQ,QAAQ;AAClB,uCAAW,UAAU,0BAA0B;AAAA,EACjD;AAEA,YAAU;AAAA,IACR,GAAG;AAAA,IACH,GAAG,uBAAuB,SAAS,OAAO;AAAA,IAC1C,iBAAa,iCAAoB,QAAQ,cAAc,GAAG,eAAe,QAAQ;AAAA,IACjF,QAAQ,QAAQ,UAAU;AAAA,IAC1B,YAAY,QAAQ,cAAc;AAAA,IAClC,aAAa,QAAQ,eAAe,UAAU,UAAU,QAAQ,OAAO;AAAA,IACvE,WAAW,QAAQ,aAAa,UAAU,UAAU,QAAQ,SAAS;AAAA,IACrE,cAAc,QAAQ,gBAAgB,gBAAgB;AAAA,EACxD;AAEA,uBAAqB,QAAQ,aAAa,QAAQ,MAAM;AAExD,MAAI,QAAQ,aAAa;AACvB,0BAAsB,QAAQ,WAAY,QAAQ,aAAa,QAAQ,MAAiB;AACxF,QAAI,QAAQ,aAAa,QAAQ,QAAgD;AAC/E,qCAA+B,QAAQ,WAAW,QAAQ,MAAM;AAAA,IAClE;AACA,2BAAuB,QAAQ,YAAY,QAAQ,MAAM;AAAA,EAC3D;AAEA,iBAAe,uCAAuC;AACpD,QAAI;AACF,YAAM,QAAQ,MAAM,qBAAqB,SAAS,CAAC,mBAAmB,CAAC;AACvE,aAAO;AAAA,IACT,SAAS,KAAK;AACZ,aAAO,YAAY,KAAK,QAAQ;AAAA,IAClC;AAAA,EACF;AAEA,iBAAe,uCAAuC;AACpD,QAAI;AACF,YAAM,QAAQ,MAAM,qBAAqB,SAAS;AAAA,QAChD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT,SAAS,KAAK;AACZ,aAAO,YAAY,KAAK,QAAQ;AAAA,IAClC;AAAA,EACF;AAEA,WAAS,YAAY,KAAc,cAA4B;AAC7D,QAAI,eAAe,wBAAwB;AACzC,UAAI,eAAe;AAEnB,YAAM,6BAA6B;AAAA;AAAA;AAAA,MAGnC,EAAE,SAAS,IAAI,MAAM;AAErB,UAAI,4BAA4B;AAC9B,YAAI,iBAAiB,UAAU;AAC7B,iBAAO,aAAyC,SAAS,IAAI,QAAQ,IAAI,eAAe,CAAC;AAAA,QAC3F;AACA,eAAO,aAAyC,SAAS,IAAI,QAAQ,IAAI,eAAe,CAAC;AAAA,MAC3F;AACA,aAAO,UAAsC,SAAS,IAAI,QAAQ,IAAI,eAAe,CAAC;AAAA,IACxF;AACA,WAAO,UAAsC,mDAA2C,IAAc,OAAO;AAAA,EAC/G;AAEA,MAAI,QAAQ,aAAa;AACvB,WAAO,qCAAqC;AAAA,EAC9C;AACA,SAAO,qCAAqC;AAC9C;AAEO,IAAM,oBAAoB,CAAC,WAAyB;AACzD,QAAM,EAAE,aAAa,YAAY,UAAU,gBAAgB,QAAQ,SAAS,gBAAgB,aAAa,OAAO,IAC9G;AACF,SAAO,EAAE,aAAa,YAAY,UAAU,gBAAgB,QAAQ,SAAS,gBAAgB,aAAa,OAAO;AACnH;AAOO,IAAM,yBAAyB,CACpC,SACA,YACG;AACH,MAAI,CAAC,SAAS;AACZ,WAAO,CAAC;AAAA,EACV;AAEA,SAAO;AAAA,IACL,aAAa,yBAAyB,QAAQ,eAAe,QAAQ,UAAU,QAAQ,aAAa,CAAC;AAAA,IACrG,QAAQ,QAAQ,UAAU,QAAQ,UAAU,QAAQ,MAAM;AAAA,IAC1D,MAAM,QAAQ,QAAQ,QAAQ,UAAU,QAAQ,IAAI;AAAA,IACpD,eAAe,QAAQ,iBAAiB,QAAQ,UAAU,QAAQ,aAAa;AAAA,IAC/E,eAAe,QAAQ,iBAAiB,QAAQ,UAAU,QAAQ,aAAa;AAAA,IAC/E,gBACE,QAAQ,kBACR,QAAQ,UAAU,QAAQ,wBAAwB,KAClD,QAAQ,UAAU,QAAQ,cAAc;AAAA,IAC1C,UAAU,QAAQ,YAAY,QAAQ,UAAU,QAAQ,QAAQ;AAAA,IAChE,WAAW,QAAQ,aAAa,QAAQ,UAAU,QAAQ,SAAS;AAAA,EACrE;AACF;;;ACjNO,SAAS,0BAA0B,QAA0C;AAClF,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM;AAAA,IACJ,QAAQ,kBAAkB;AAAA,IAC1B,WAAW,qBAAqB;AAAA,IAChC,QAAQ,kBAAkB;AAAA,IAC1B,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa,uBAAuB;AAAA,IACpC,UAAU,gBAAgB;AAAA,IAC1B,gBAAgB,0BAA0B;AAAA,IAC1C,aAAa,uBAAuB;AAAA,IACpC,QAAQ,kBAAkB;AAAA,IAC1B,UAAU,oBAAoB;AAAA,IAC9B,WAAW;AAAA,EACb,IAAI,OAAO;AAEX,QAAMC,uBAAsB,CAAC;AAAA,IAC3B,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,UAAU;AAAA,IACV,aAAa;AAAA,IACb,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,QAAQ;AAAA,IACR;AAAA,IACA,GAAG;AAAA,EACL,MAAiE;AAC/D,WAAO,oBAA4B;AAAA,MACjC,GAAG;AAAA,MACH,QAAQ,iBAAiB;AAAA,MACzB,WAAW,oBAAoB;AAAA,MAC/B,UAAU,mBAAmB;AAAA,MAC7B;AAAA,MACA;AAAA,MACA,aAAa,sBAAsB;AAAA,MACnC,UAAU,mBAAmB;AAAA,MAC7B,gBAAgB,yBAAyB;AAAA,MACzC,aAAa,sBAAsB;AAAA,MACnC,QAAQ,iBAAiB;AAAA,MACzB,QAAQ,iBAAiB;AAAA,MACzB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,oBAAoB,CAAC;AAAA,IACzB,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,GAAG;AAAA,EACL,MACE,0BAA0B;AAAA,IACxB,GAAG;AAAA,IACH,aAAa,sBAAsB;AAAA,IACnC,UAAU,mBAAmB;AAAA,IAC7B,gBAAgB,yBAAyB;AAAA,IACzC,aAAa,sBAAsB;AAAA,IACnC,QAAQ,iBAAiB;AAAA,EAC3B,CAAC;AAEH,QAAM,2BAA2B,CAAC;AAAA,IAChC,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,GAAG;AAAA,EACL,MAA+B;AAC7B,WAAO,yBAAyB;AAAA,MAC9B,GAAG;AAAA,MACH;AAAA,MACA,aAAa,sBAAsB;AAAA,MACnC,gBAAgB,yBAAyB;AAAA,MACzC,UAAU,mBAAmB;AAAA,MAC7B,aAAa,sBAAsB;AAAA,MACnC,QAAS,iBAAiB;AAAA,MAC1B,WAAW,oBAAoB;AAAA,IACjC,CAAC;AAAA,EACH;AAEA,QAAM,8BAA8B;AAGpC,QAAM,4BAA4B,MAAM,UAAU,aAAa,gBAAgB;AAE/E,SAAO;AAAA,IACL,qBAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACvHA,IAAM,WAAW,CAAC,WAAmB,gBAAyB;AAC5D,MAAI;AACJ,MAAI,CAAC,UAAU,WAAW,MAAM,GAAG;AACjC,QAAI,CAAC,eAAe,CAAC,YAAY,WAAW,MAAM,GAAG;AACnD,YAAM,IAAI,MAAM,oEAAoE;AAAA,IACtF;AAEA,UAAM,UAAU,IAAI,IAAI,WAAW;AACnC,UAAM,IAAI,IAAI,WAAW,QAAQ,MAAM;AAAA,EACzC,OAAO;AACL,UAAM,IAAI,IAAI,SAAS;AAAA,EACzB;AAEA,MAAI,aAAa;AACf,QAAI,aAAa,IAAI,gBAAgB,WAAW;AAAA,EAClD;AAEA,SAAO,IAAI,SAAS;AACtB;AAaO,SAAS,SAAS,EAAE,iBAAiB,WAAW,WAAW,aAAa,eAAe,GAAmB;AAC/G,MAAI,CAAC,aAAa;AAChB,sBAAc,iCAAoB,cAAc,GAAG;AAAA,EACrD,OAAO;AACL,uCAAW,eAAe,+BAA+B;AAAA,EAC3D;AAEA,QAAM,kBAAkB,qBAAqB,WAAW;AAExD,QAAM,mBAAmB,CAAC,EAAE,cAAc,IAAkB,CAAC,MAAM;AACjE,QAAI,CAAC,aAAa,CAAC,iBAAiB;AAClC,mBAAa,gCAAgC;AAAA,IAC/C;AAEA,UAAM,oBAAoB,GAAG,eAAe;AAC5C,WAAO,gBAAgB,SAAS,aAAa,mBAAmB,aAAa,CAAC;AAAA,EAChF;AAEA,QAAM,mBAAmB,CAAC,EAAE,cAAc,IAAkB,CAAC,MAAM;AACjE,QAAI,CAAC,aAAa,CAAC,iBAAiB;AAClC,mBAAa,gCAAgC;AAAA,IAC/C;AAEA,UAAM,oBAAoB,GAAG,eAAe;AAC5C,WAAO,gBAAgB,SAAS,aAAa,mBAAmB,aAAa,CAAC;AAAA,EAChF;AAEA,SAAO,EAAE,kBAAkB,iBAAiB;AAC9C;AAEA,SAAS,qBAAqB,aAAsB;AAClD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAGA,QAAM,kBAAkB,YAErB,QAAQ,4BAA4B,gBAAgB,EACpD,QAAQ,+BAA+B,WAAW;AACrD,SAAO,WAAW,eAAe;AACnC;;;AhEtDO,SAAS,MAAM,SAAuB;AAC3C,QAAM,OAAO,EAAE,GAAG,QAAQ;AAC1B,QAAM,YAAY,uBAAuB,IAAI;AAC7C,QAAM,eAAe,0BAA0B,EAAE,SAAS,MAAM,UAAU,CAAC;AAE3E,QAAM,gBAAgB;AAAA,IACpB,GAAG;AAAA,IACH,GAAG;AAAA;AAAA;AAAA;AAAA,IAIH,oBAAoB;AAAA,EACtB;AAIA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SAAO;AACT;", "names": ["import_deprecated", "basePath", "basePath", "basePath", "basePath", "import_deprecated", "import_keys", "basePath", "import_deprecated", "crypto", "basePath", "basePath", "basePath", "basePath", "basePath", "import_deprecated", "basePath", "basePath", "import_error", "ObjectType", "snakecase<PERSON><PERSON>s", "key", "deepmerge", "import_deprecated", "buildRequest", "AuthStatus", "import_deprecated", "parse", "key", "jwk", "buildRequest", "authenticateRequest"]}